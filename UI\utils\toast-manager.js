/**
 * 全局 Toast 管理器
 * 统一使用 uview-plus 的 toast 组件
 */

/**
 * 显示 Toast 消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型: success, error, warning, info
 * @param {number} duration - 显示时长（毫秒）
 */
function showToast(message, type = 'success', duration = 3000) {
  try {
    // 尝试使用全局 toast 组件
    const app = getApp();
    if (app && app.globalToast) {
      app.globalToast.show({
        message: message,
        type: type,
        duration: duration
      });
      return;
    }
  } catch (error) {
    console.warn('全局 toast 组件不可用，使用系统 toast:', error);
  }

  // 降级到系统 toast
  const iconMap = {
    success: 'success',
    error: 'none',
    warning: 'none',
    info: 'none'
  };

  uni.showToast({
    title: message,
    icon: iconMap[type] || 'none',
    duration: duration
  });
}

/**
 * 显示成功消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长
 */
export function showSuccess(message, duration = 2000) {
  showToast(message, 'success', duration);
}

/**
 * 显示错误消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长
 */
export function showError(message, duration = 3000) {
  showToast(message, 'error', duration);
}

/**
 * 显示警告消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长
 */
export function showWarning(message, duration = 3000) {
  showToast(message, 'warning', duration);
}

/**
 * 显示信息消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长
 */
export function showInfo(message, duration = 3000) {
  showToast(message, 'info', duration);
}

/**
 * 通用 Toast 函数
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型
 * @param {number} duration - 显示时长
 */
export function toast(message, type = 'success', duration = 3000) {
  showToast(message, type, duration);
}

// 默认导出
export default {
  showSuccess,
  showError,
  showWarning,
  showInfo,
  toast
};
