// 应用配置
const config = {
  // API基础地址
  apiBaseUrl: process.env.NODE_ENV === 'development'
    ? 'https://localhost:7048'
    : 'https://your-production-api.com',

  // 上传配置
  upload: {
    maxFileSize: 2 * 1024 * 1024 * 1024, // 2GB
    allowedVideoFormats: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'],
    allowedImageFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  },

  // 压缩配置
  compression: {
    defaultQuality: 7,
    qualityRange: [1, 10],
    defaultEnabled: true
  }
};

export default config;
