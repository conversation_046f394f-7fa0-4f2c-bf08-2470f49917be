/* ===== 主样式入口文件 ===== */

/* 导入设计系统变量 */
@import './variables.scss';

/* 导入全局样式 */
@import './global.scss';

/* 导入组件样式 */
@import './components.scss';

/* 导入页面专用样式 */
@import './user-management.scss';

/* ===== 工具类样式 ===== */

/* === 间距工具类 === */
.m-0 {
  margin: 0;
}

.m-xs {
  margin: $spacing-xs;
}

.m-sm {
  margin: $spacing-sm;
}

.m-base {
  margin: $spacing-base;
}

.m-md {
  margin: $spacing-md;
}

.m-lg {
  margin: $spacing-lg;
}

.m-xl {
  margin: $spacing-xl;
}

.m-xxl {
  margin: $spacing-xxl;
}

.mt-0 {
  margin-top: 0;
}

.mt-xs {
  margin-top: $spacing-xs;
}

.mt-sm {
  margin-top: $spacing-sm;
}

.mt-base {
  margin-top: $spacing-base;
}

.mt-md {
  margin-top: $spacing-md;
}

.mt-lg {
  margin-top: $spacing-lg;
}

.mt-xl {
  margin-top: $spacing-xl;
}

.mt-xxl {
  margin-top: $spacing-xxl;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-xs {
  margin-bottom: $spacing-xs;
}

.mb-sm {
  margin-bottom: $spacing-sm;
}

.mb-base {
  margin-bottom: $spacing-base;
}

.mb-md {
  margin-bottom: $spacing-md;
}

.mb-lg {
  margin-bottom: $spacing-lg;
}

.mb-xl {
  margin-bottom: $spacing-xl;
}

.mb-xxl {
  margin-bottom: $spacing-xxl;
}

.ml-0 {
  margin-left: 0;
}

.ml-xs {
  margin-left: $spacing-xs;
}

.ml-sm {
  margin-left: $spacing-sm;
}

.ml-base {
  margin-left: $spacing-base;
}

.ml-md {
  margin-left: $spacing-md;
}

.ml-lg {
  margin-left: $spacing-lg;
}

.ml-xl {
  margin-left: $spacing-xl;
}

.ml-xxl {
  margin-left: $spacing-xxl;
}

.mr-0 {
  margin-right: 0;
}

.mr-xs {
  margin-right: $spacing-xs;
}

.mr-sm {
  margin-right: $spacing-sm;
}

.mr-base {
  margin-right: $spacing-base;
}

.mr-md {
  margin-right: $spacing-md;
}

.mr-lg {
  margin-right: $spacing-lg;
}

.mr-xl {
  margin-right: $spacing-xl;
}

.mr-xxl {
  margin-right: $spacing-xxl;
}

.p-0 {
  padding: 0;
}

.p-xs {
  padding: $spacing-xs;
}

.p-sm {
  padding: $spacing-sm;
}

.p-base {
  padding: $spacing-base;
}

.p-md {
  padding: $spacing-md;
}

.p-lg {
  padding: $spacing-lg;
}

.p-xl {
  padding: $spacing-xl;
}

.p-xxl {
  padding: $spacing-xxl;
}

.pt-0 {
  padding-top: 0;
}

.pt-xs {
  padding-top: $spacing-xs;
}

.pt-sm {
  padding-top: $spacing-sm;
}

.pt-base {
  padding-top: $spacing-base;
}

.pt-md {
  padding-top: $spacing-md;
}

.pt-lg {
  padding-top: $spacing-lg;
}

.pt-xl {
  padding-top: $spacing-xl;
}

.pt-xxl {
  padding-top: $spacing-xxl;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-xs {
  padding-bottom: $spacing-xs;
}

.pb-sm {
  padding-bottom: $spacing-sm;
}

.pb-base {
  padding-bottom: $spacing-base;
}

.pb-md {
  padding-bottom: $spacing-md;
}

.pb-lg {
  padding-bottom: $spacing-lg;
}

.pb-xl {
  padding-bottom: $spacing-xl;
}

.pb-xxl {
  padding-bottom: $spacing-xxl;
}

.pl-0 {
  padding-left: 0;
}

.pl-xs {
  padding-left: $spacing-xs;
}

.pl-sm {
  padding-left: $spacing-sm;
}

.pl-base {
  padding-left: $spacing-base;
}

.pl-md {
  padding-left: $spacing-md;
}

.pl-lg {
  padding-left: $spacing-lg;
}

.pl-xl {
  padding-left: $spacing-xl;
}

.pl-xxl {
  padding-left: $spacing-xxl;
}

.pr-0 {
  padding-right: 0;
}

.pr-xs {
  padding-right: $spacing-xs;
}

.pr-sm {
  padding-right: $spacing-sm;
}

.pr-base {
  padding-right: $spacing-base;
}

.pr-md {
  padding-right: $spacing-md;
}

.pr-lg {
  padding-right: $spacing-lg;
}

.pr-xl {
  padding-right: $spacing-xl;
}

.pr-xxl {
  padding-right: $spacing-xxl;
}

/* === 文本工具类 === */
.text-xs {
  font-size: $font-size-xs;
}

.text-sm {
  font-size: $font-size-sm;
}

.text-base {
  font-size: $font-size-base;
}

.text-md {
  font-size: $font-size-md;
}

.text-lg {
  font-size: $font-size-lg;
}

.text-xl {
  font-size: $font-size-xl;
}

.text-xxl {
  font-size: $font-size-xxl;
}

.text-primary {
  color: $text-primary;
}

.text-secondary {
  color: $text-secondary;
}

.text-tertiary {
  color: $text-tertiary;
}

.text-disabled {
  color: $text-disabled;
}

.text-white {
  color: $text-white;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-info {
  color: $info-color;
}

.font-light {
  font-weight: $font-weight-light;
}

.font-normal {
  font-weight: $font-weight-normal;
}

.font-medium {
  font-weight: $font-weight-medium;
}

.font-semibold {
  font-weight: $font-weight-semibold;
}

.font-bold {
  font-weight: $font-weight-bold;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* === 布局工具类 === */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

/* === 背景工具类 === */
.bg-primary {
  background-color: $bg-primary;
}

.bg-secondary {
  background-color: $bg-secondary;
}

.bg-tertiary {
  background-color: $bg-tertiary;
}

.bg-disabled {
  background-color: $bg-disabled;
}

.bg-success {
  background-color: $success-color;
}

.bg-warning {
  background-color: $warning-color;
}

.bg-error {
  background-color: $error-color;
}

.bg-info {
  background-color: $info-color;
}

/* === 边框工具类 === */
.border {
  border: 1rpx solid $border-primary;
}

.border-secondary {
  border: 1rpx solid $border-secondary;
}

.border-light {
  border: 1rpx solid $border-light;
}

.border-t {
  border-top: 1rpx solid $border-primary;
}

.border-b {
  border-bottom: 1rpx solid $border-primary;
}

.border-l {
  border-left: 1rpx solid $border-primary;
}

.border-r {
  border-right: 1rpx solid $border-primary;
}

.rounded-none {
  border-radius: 0;
}

.rounded-xs {
  border-radius: $border-radius-xs;
}

.rounded-sm {
  border-radius: $border-radius-sm;
}

.rounded-base {
  border-radius: $border-radius-base;
}

.rounded-md {
  border-radius: $border-radius-md;
}

.rounded-lg {
  border-radius: $border-radius-lg;
}

.rounded-xl {
  border-radius: $border-radius-xl;
}

.rounded-full {
  border-radius: $border-radius-round;
}

/* === 阴影工具类 === */
.shadow-none {
  box-shadow: none;
}

.shadow-sm {
  box-shadow: $shadow-sm;
}

.shadow-base {
  box-shadow: $shadow-base;
}

.shadow-md {
  box-shadow: $shadow-md;
}

.shadow-lg {
  box-shadow: $shadow-lg;
}

/* === 显示工具类 === */
.block {
  display: block;
}

.inline {
  display: inline;
}

.inline-block {
  display: inline-block;
}

.hidden {
  display: none;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-scroll {
  overflow: scroll;
}

/* === 宽高工具类 === */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.w-auto {
  width: auto;
}

.h-auto {
  height: auto;
}

/* === 透明度工具类 === */
.opacity-0 {
  opacity: 0;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-100 {
  opacity: 1;
}

/* === 过渡动画工具类 === */
.transition-fast {
  transition: all $transition-fast ease;
}

.transition-base {
  transition: all $transition-base ease;
}

.transition-slow {
  transition: all $transition-slow ease;
}

/* === 响应式工具类 === */
@media (max-width: #{$breakpoint-sm}) {
  .sm\:hidden {
    display: none;
  }

  .sm\:block {
    display: block;
  }
}

@media (max-width: #{$breakpoint-md}) {
  .md\:hidden {
    display: none;
  }

  .md\:block {
    display: block;
  }
}

@media (max-width: #{$breakpoint-lg}) {
  .lg\:hidden {
    display: none;
  }

  .lg\:block {
    display: block;
  }
}