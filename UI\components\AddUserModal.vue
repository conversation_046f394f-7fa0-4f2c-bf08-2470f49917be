<template>
  <view class="modal-overlay" v-if="modalVisible" @tap="handleClose">
    <view class="modal-container" @tap.stop>
      <view class="modal-header">
        <text class="modal-title">{{ title }}</text>
      </view>

      <view class="modal-body">
        <view class="form-group">
          <text class="label">用户名</text>
          <input v-model="formData.userName" placeholder="请输入用户名" class="input-field" type="text" />
        </view>

        <view class="form-group">
          <text class="label">密码</text>
          <input v-model="formData.password" placeholder="请输入密码" class="input-field" type="password" />
        </view>

        <button class="submit-btn" @tap="handleSubmit" :disabled="loading">
          {{ loading ? '创建中...' : '确认添加' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AddUserModal',
  props: {
    title: {
      type: String,
      default: '添加用户'
    },
    userType: {
      type: Number,
      required: true
    },
    show: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:show', 'submit', 'close'],
  data () {
    return {
      formData: {
        userName: '',
        password: '',
        userType: this.userType,
        status: 1
      }
    };
  },
  computed: {
    modalVisible: {
      get () {
        return this.show;
      },
      set (value) {
        this.$emit('update:show', value);
      }
    }
  },
  watch: {
    userType (newVal) {
      this.formData.userType = newVal;
    },
    show (newVal) {
      if (!newVal) {
        this.resetForm();
      }
    }
  },
  methods: {
    handleSubmit () {
      // 简单验证
      if (!this.formData.userName || !this.formData.password) {
        uni.showToast({
          title: "用户名和密码不能为空",
          icon: "none",
        });
        return;
      }

      if (this.formData.password.length < 6) {
        uni.showToast({
          title: "密码长度不能少于6位",
          icon: "none",
        });
        return;
      }

      // 提交表单数据
      this.$emit('submit', { ...this.formData });
    },
    handleClose () {
      this.modalVisible = false;
      this.$emit('close');
    },
    resetForm () {
      this.formData = {
        userName: '',
        password: '',
        userType: this.userType,
        status: 1
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  background: #fff;
  border-radius: 20rpx;
  width: 92%;
  max-width: 680rpx;
  margin: 0 auto;
  overflow: hidden;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  text-align: center;
  padding: 40rpx 40rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-body {
  padding: 24rpx 40rpx 40rpx;
}

.form-group {
  margin-bottom: 28rpx;
}

.label {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  width: 100%;
  padding: 28rpx 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  background: #fff;
  transition: all 0.2s ease;
  height: 88rpx;

  &:focus {
    border-color: #007aff;
    background: #fff;
    outline: none;
    box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
  }

  &::placeholder {
    color: #bbb;
    font-size: 30rpx;
  }
}

.submit-btn {
  width: 100%;
  padding: 0;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 24rpx;
  height: 88rpx;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    background: #0056cc;
    transform: scale(0.98);
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
  }
}
</style>
