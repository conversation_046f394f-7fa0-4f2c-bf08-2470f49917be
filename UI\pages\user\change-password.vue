<template>
  <view class="container">
    <PageHeader title="修改密码" />

    <view class="form-container">
      <view class="form-section">
        <view class="form-item">
          <text class="label">当前密码</text>
          <input type="password" v-model="form.oldPassword" placeholder="请输入当前密码" class="input" />
        </view>

        <view class="form-item">
          <text class="label">新密码</text>
          <input type="password" v-model="form.newPassword" placeholder="请输入新密码" class="input" />
        </view>

        <view class="form-item">
          <text class="label">确认新密码</text>
          <input type="password" v-model="form.confirmPassword" placeholder="请再次输入新密码" class="input" />
        </view>

        <view class="password-tips">
          <text class="tip-title">密码要求：</text>
          <text class="tip-item">• 长度不少于6位</text>
          <text class="tip-item">• 必须包含字母和数字</text>
          <text class="tip-item">• 建议包含特殊字符</text>
        </view>
      </view>

      <view class="button-section">
        <button class="submit-btn" @tap="changePassword" :disabled="loading">
          {{ loading ? '修改中...' : '确认修改' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import PageHeader from '../../components/PageHeader.vue'
import { changeSysUserPassword } from '../../api/sysuser.js'
import { getAdminLoginInfo as getLoginInfo } from '../../utils/adminAuthService.js'
import CryptoJS from 'crypto-js'

export default {
  components: {
    PageHeader
  },
  data () {
    return {
      form: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      loading: false
    }
  },
  methods: {
    // 验证密码格式
    validatePassword (password) {
      if (!password || password.length < 6) {
        return '密码长度不能少于6位'
      }
      if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(password)) {
        return '密码必须包含字母和数字'
      }
      return null
    },

    // 修改密码
    async changePassword () {
      // 表单验证
      if (!this.form.oldPassword) {
        uni.showToast({
          title: '请输入当前密码',
          icon: 'none'
        })
        return
      }

      if (!this.form.newPassword) {
        uni.showToast({
          title: '请输入新密码',
          icon: 'none'
        })
        return
      }

      if (this.form.newPassword !== this.form.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        })
        return
      }

      const passwordError = this.validatePassword(this.form.newPassword)
      if (passwordError) {
        uni.showToast({
          title: passwordError,
          icon: 'none'
        })
        return
      }

      if (this.form.oldPassword === this.form.newPassword) {
        uni.showToast({
          title: '新密码不能与当前密码相同',
          icon: 'none'
        })
        return
      }

      try {
        this.loading = true

        // 获取当前用户信息
        const loginInfo = getLoginInfo()
        if (!loginInfo || !loginInfo.userId) {
          uni.showToast({
            title: '用户信息获取失败',
            icon: 'none'
          })
          return
        }

        // 对密码进行MD5加密
        const hashedOldPassword = CryptoJS.MD5(this.form.oldPassword).toString()
        const hashedNewPassword = CryptoJS.MD5(this.form.newPassword).toString()

        await changeSysUserPassword({
          userId: loginInfo.userId,
          oldPassword: hashedOldPassword,
          newPassword: hashedNewPassword
        })

        uni.showToast({
          title: '密码修改成功',
          icon: 'success'
        })

        // 清空表单
        this.form = {
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        }

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        uni.showToast({
          title: error.message || '密码修改失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style>
.container {
  background-color: #f5f5f5;

}

.form-container {
  padding: 30rpx 20rpx;
}

.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.input:focus {
  border-color: #186BFF;
  box-shadow: 0 0 8rpx rgba(24, 144, 255, 0.2);
}

.password-tips {
  background-color: #f6f8fa;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-top: 30rpx;
}

.tip-title {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.tip-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.button-section {
  padding: 0 10rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #186BFF;
  color: #fff;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

.submit-btn:active {
  transform: scale(0.98);
  background-color: #0c7ad9;
}

.submit-btn[disabled] {
  background-color: #ccc;
  box-shadow: none;
  transform: none;
}
</style>
