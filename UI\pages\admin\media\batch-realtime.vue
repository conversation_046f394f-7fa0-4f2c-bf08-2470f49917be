<template>
    <view class="container">
        <view class="page-header">
            <!-- 页面标题 -->
            <view class="header-section">
                <view class="title-area">
                    <view class="back-btn" @tap="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <view>
                        <text class="page-title">{{ batch.title }}</text>
                        <view class="page-subtitle">实时数据</view>
                    </view>
                </view>

                <view class="action-area">
                    <button class="refresh-btn" @tap="refreshData">
                        <text class="refresh-icon">🔄</text>
                        <text>刷新</text>
                    </button>
                </view>
            </view>
        </view>

        <view class="page-header-1"></view>

        <!-- 批次信息卡片 -->
        <view class="batch-info-card">
            <view class="batch-title-row">
                <text class="batch-title">{{ batch.title }}</text>
                <view class="batch-status" :class="getBatchStatusClass(batch)">{{ getBatchStatusText(batch) }}</view>
            </view>
            <view class="batch-id">批次编号: {{ batch.batchId }}</view>
            <view class="batch-period">
                {{ formatDate(batch.startTime) }} ~ {{ formatDate(batch.endTime) }}
            </view>
        </view>

        <!-- 实时数据概览 -->
        <view class="data-overview">
            <view class="data-card">
                <view class="data-value">{{ realTimeData.currentOnline }}</view>
                <view class="data-label">当前在线</view>
            </view>
            <view class="data-card">
                <view class="data-value">{{ realTimeData.todayViews }}</view>
                <view class="data-label">今日观看</view>
            </view>
            <view class="data-card">
                <view class="data-value">{{ realTimeData.todayParticipants }}</view>
                <view class="data-label">今日参与人</view>
            </view>
            <view class="data-card">
                <view class="data-value">{{ realTimeData.todayCompletions }}</view>
                <view class="data-label">今日完成</view>
            </view>
        </view>

        <!-- 总体数据 -->
        <view class="section-card">
            <view class="section-header">
                <text class="section-title">总体数据</text>
                <text class="section-subtitle">累计统计</text>
            </view>

            <view class="total-data">
                <view class="data-row">
                    <view class="data-item">
                        <text class="data-label">总观看量</text>
                        <text class="data-value primary">{{ batch.totalViews || 0 }}</text>
                    </view>
                    <view class="data-item">
                        <text class="data-label">总参与人数</text>
                        <text class="data-value success">{{ batch.participants || 0 }}</text>
                    </view>
                </view>
                <view class="data-row">
                    <view class="data-item">
                        <text class="data-label">完成率</text>
                        <text class="data-value warning">{{ realTimeData.completionRate }}%</text>
                    </view>
                    <view class="data-item">
                        <text class="data-label">总奖励金额</text>
                        <text class="data-value danger">{{ batch.totalReward || 0 }}元</text>
                    </view>
                </view>
            </view>

            <view class="data-charts">
                <view class="chart-title">近7天观看趋势</view>
                <view class="chart-placeholder">
                    <!-- 这里可以集成第三方图表库或自定义图表 -->
                    <view class="chart-bars">
                        <view class="chart-bar" v-for="(item, index) in recentViewsData" :key="index">
                            <view class="bar-value" :style="{ height: item.height }"></view>
                            <view class="bar-label">{{ item.day }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 热门视频排行 -->
        <view class="section-card">
            <view class="section-header">
                <text class="section-title">热门视频排行</text>
                <text class="section-subtitle">今日最受欢迎</text>
            </view>

            <view class="video-rank-list">
                <view class="video-rank-item" v-for="(video, index) in topVideos" :key="video.id"
                    @tap="viewVideoDetail(video)">
                    <view class="rank-number" :class="index < 3 ? 'top-rank' : ''">{{ index + 1 }}</view>
                    <view class="video-thumbnail">
                        <image :src="video.thumbnail" mode="aspectFill"></image>
                    </view>
                    <view class="video-info">
                        <text class="video-title">{{ video.title }}</text>
                        <view class="video-stats">
                            <view class="stat-item">
                                <text class="stat-label">今日观看:</text>
                                <text class="stat-value">{{ video.todayViews }}</text>
                            </view>
                            <view class="stat-item">
                                <text class="stat-label">当前在线:</text>
                                <text class="stat-value">{{ video.currentOnline }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 活跃用户排行 -->
        <view class="section-card">
            <view class="section-header">
                <text class="section-title">活跃用户排行</text>
                <text class="section-subtitle">最积极用户的活跃</text>
            </view>

            <view class="user-rank-list">
                <view class="user-rank-item" v-for="(user, index) in topUsers" :key="user.id">
                    <view class="rank-number" :class="index < 3 ? 'top-rank' : ''">{{ index + 1 }}</view>
                    <view class="user-avatar">
                        <image :src="user.avatar" mode="aspectFill"></image>
                    </view>
                    <view class="user-info">
                        <text class="user-name">{{ user.name }}</text>
                        <text class="user-type">{{ user.type }}</text>
                    </view>
                    <view class="user-progress">
                        <view class="progress-bar">
                            <view class="progress-inner" :style="{ width: user.completionRate + '%' }"></view>
                        </view>
                        <text class="progress-text">完成率 {{ user.completionRate }}%</text>
                    </view>
                    <view class="user-reward">
                        <text class="reward-value">{{ user.totalReward }}元</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { getBatchDetail } from "@/api/batch.js";
import { getDashboard } from "@/api/dashboard.js";

export default {
    data () {
        return {
            batchId: '',
            batch: {
                id: '',
                batchId: '',
                title: '',
                status: '',
                startTime: '',
                endTime: '',
                totalViews: 0,
                participants: 0,
                totalReward: 0
            },
            realTimeData: {
                currentOnline: 0,
                todayViews: 0,
                todayParticipants: 0,
                todayCompletions: 0,
                completionRate: 0
            },
            recentViewsData: [],
            topVideos: [],
            topUsers: []
        }
    },
    onLoad (options) {
        if (options.id) {
            this.batchId = options.id;
            this.loadBatchDetail();
            this.loadRealTimeData();
            this.loadTopVideos();
            this.loadTopUsers();
        }
    },
    methods: {
        async loadBatchDetail () {
            try {
                // 调用API获取批次详情
                const response = await getBatchDetail(this.batchId);

                if (response.success && response.data) {
                    const batch = response.data;
                    this.batch = {
                        id: batch.id,
                        batchId: `B${batch.id}`,
                        title: batch.name || batch.title,
                        status: this.mapBatchStatus(batch.status),
                        createTime: batch.createTime,
                        startTime: batch.startTime,
                        endTime: batch.endTime,
                        creator: batch.creatorName || '未知',
                        videoCount: 1, // 新接口中一个批次对应一个视频
                        totalViews: batch.currentParticipants || 0,
                        participants: batch.currentParticipants || 0,
                        totalReward: batch.rewardAmount || 0,
                        redPacketAmount: batch.redPacketAmount || 0
                    };
                } else {
                    throw new Error(response.msg || '获取批次详情失败');
                }
            } catch (error) {
                uni.showToast({
                    title: '加载失败',
                    icon: 'none'
                });
            }
        },

        // 映射批次状态
        mapBatchStatus (apiStatus) {
            const statusMap = {
                0: 'pending', // 草稿/待开始
                1: 'active',  // 进行中
                2: 'ended',   // 已结束
                3: 'paused'   // 已暂停
            };
            return statusMap[apiStatus] || 'pending';
        },
        async loadRealTimeData () {
            try {
                // 调用统计API获取实时数据
                const params = {
                    batchId: this.batchId,
                    timeRange: 'today'
                };

                const response = await getDashboard(params);

                if (response.success && response.data) {
                    const stats = response.data;
                    this.realTimeData = {
                        currentOnline: stats.currentOnline || 0,
                        todayViews: stats.todayViews || 0,
                        todayParticipants: stats.todayParticipants || 0,
                        todayCompletions: stats.todayCompletions || 0,
                        completionRate: stats.completionRate || 0
                    };
                } else {
                    throw new Error(response.msg || '获取实时数据失败');
                }
            } catch (error) {
                uni.showToast({
                    title: '加载失败',
                    icon: 'none'
                });
            }
        },
        loadTopVideos () {
            this.topVideos = [];
        },
        loadTopUsers () {
            this.topUsers = [];
        },
        async refreshData () {
            try {
                uni.showLoading({
                    title: '刷新数据中...'
                });

                await Promise.all([
                    this.loadRealTimeData(),
                    this.loadTopVideos(),
                    this.loadTopUsers()
                ]);

                uni.hideLoading();
                uni.showToast({
                    title: '数据已更新',
                    icon: 'success'
                });
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: '刷新失败',
                    icon: 'none'
                });
            }
        },
        getBatchStatusClass (batch) {
            if (batch.status === 'ended') return 'status-expired';
            if (batch.status === 'pending') return 'status-scheduled';
            return 'status-active';
        },
        getBatchStatusText (batch) {
            if (batch.status === 'ended') return '已过期';
            if (batch.status === 'pending') return '未开始';
            return '进行中';
        },
        formatDate (dateString) {
            if (!dateString) return '未设置';
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');

            return `${year}-${month}-${day}`;
        },
        viewVideoDetail (video) {
            uni.navigateTo({
                url: `/pages/admin/media/detail?id=${video.id}`
            });
        },
        goBack () {
            uni.navigateBack();
        }
    }
}
</script>

<style scoped>
.container {
    background-color: #f5f5f5;

}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20rpx 30rpx;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.page-header-1 {
    height: 120rpx;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 40rpx;
}

.title-area {
    display: flex;
    align-items: center;
}

.back-btn {
    margin-right: 20rpx;
    padding: 10rpx;
}

.icon-back {
    color: white;
    font-size: 40rpx;
}

.page-title {
    color: white;
    font-size: 36rpx;
    font-weight: bold;
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 24rpx;
    margin-top: 5rpx;
}

.action-area {
    display: flex;
    align-items: center;
}

.refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 20rpx;
    padding: 10rpx 20rpx;
    color: white;
    font-size: 24rpx;
    display: flex;
    align-items: center;
}

.refresh-icon {
    margin-right: 10rpx;
    font-size: 28rpx;
}

.batch-info-card {
    background: white;
    margin: 20rpx;
    padding: 30rpx;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.batch-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.batch-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.batch-status {
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    color: white;
}

.status-active {
    background: #52c41a;
}

.status-expired {
    background: #ff4d4f;
}

.status-scheduled {
    background: #faad14;
}

.batch-id {
    color: #666;
    font-size: 28rpx;
    margin-bottom: 10rpx;
}

.batch-period {
    color: #999;
    font-size: 26rpx;
}

.data-overview {
    display: flex;
    margin: 0 20rpx 20rpx;
    gap: 15rpx;
}

.data-card {
    flex: 1;
    background: white;
    padding: 30rpx 20rpx;
    border-radius: 15rpx;
    text-align: center;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.data-value {
    font-size: 48rpx;
    font-weight: bold;
    color: #186BFF;
    margin-bottom: 10rpx;
}

.data-label {
    font-size: 24rpx;
    color: #666;
}

.section-card {
    background: white;
    margin: 0 20rpx 20rpx;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 5rpx;
}

.section-subtitle {
    font-size: 24rpx;
    color: #999;
}

.total-data {
    padding: 30rpx;
}

.data-row {
    display: flex;
    margin-bottom: 30rpx;
}

.data-row:last-child {
    margin-bottom: 0;
}

.data-item {
    flex: 1;
    text-align: center;
}

.data-item .data-label {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 10rpx;
}

.data-item .data-value {
    font-size: 36rpx;
    font-weight: bold;
}

.data-item .data-value.primary {
    color: #186BFF;
}

.data-item .data-value.success {
    color: #52c41a;
}

.data-item .data-value.warning {
    color: #faad14;
}

.data-item .data-value.danger {
    color: #ff4d4f;
}

.data-charts {
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
}

.chart-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
}

.chart-placeholder {
    height: 200rpx;
    background: #f8f9fa;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-bars {
    display: flex;
    align-items: end;
    height: 150rpx;
    gap: 10rpx;
}

.chart-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.bar-value {
    background: linear-gradient(to top, #186BFF, #40a9ff);
    border-radius: 4rpx 4rpx 0 0;
    width: 100%;
    min-height: 20rpx;
}

.bar-label {
    font-size: 20rpx;
    color: #666;
    margin-top: 10rpx;
}

.video-rank-list,
.user-rank-list {
    padding: 30rpx;
}

.video-rank-item,
.user-rank-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
}

.video-rank-item:last-child,
.user-rank-item:last-child {
    border-bottom: none;
}

.rank-number {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: #f0f0f0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: bold;
    margin-right: 20rpx;
}

.rank-number.top-rank {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    color: white;
}

.video-thumbnail,
.user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 10rpx;
    overflow: hidden;
    margin-right: 20rpx;
}

.user-avatar {
    border-radius: 50%;
}

.video-thumbnail image,
.user-avatar image {
    width: 100%;
    height: 100%;
}

.video-info,
.user-info {
    flex: 1;
    margin-right: 20rpx;
}

.video-title,
.user-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
}

.user-type {
    font-size: 24rpx;
    color: #999;
}

.video-stats {
    display: flex;
    gap: 20rpx;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5rpx;
}

.stat-label {
    font-size: 22rpx;
    color: #666;
}

.stat-value {
    font-size: 22rpx;
    color: #186BFF;
    font-weight: bold;
}

.user-progress {
    flex: 1;
    margin-right: 20rpx;
}

.progress-bar {
    height: 8rpx;
    background: #f0f0f0;
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 10rpx;
}

.progress-inner {
    height: 100%;
    background: linear-gradient(90deg, #52c41a, #73d13d);
    border-radius: 4rpx;
}

.progress-text {
    font-size: 22rpx;
    color: #666;
}

.user-reward {
    text-align: right;
}

.reward-value {
    font-size: 28rpx;
    font-weight: bold;
    color: #ff4d4f;
}
</style>