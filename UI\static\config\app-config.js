/**
 * 应用配置文件
 * 此文件可以在项目发布后直接修改，无需重新编译
 * 
 * 使用方法：
 * 1. 开发环境：修改此文件后刷新页面即可生效
 * 2. 生产环境：直接修改服务器上的此文件，用户刷新页面后生效
 */

window.APP_CONFIG = {
  // 项目前端访问地址
  // 开发环境示例：'http://localhost:5173'
  // 生产环境示例：'https://your-domain.com' 或 'http://your-server-ip:port'
  UIProjectUrl: 'http://localhost:5173',

  // 其他可配置项
  // API超时时间（毫秒）
  apiTimeout: 30000,

  // 是否启用调试模式
  debugMode: true,

  // 版本信息
  version: '1.0.0'
};

// 提供获取配置的方法
window.getAppConfig = function (key) {
  return window.APP_CONFIG[key];
};

// 提供设置配置的方法（用于动态修改）
window.setAppConfig = function (key, value) {
  window.APP_CONFIG[key] = value;
};
