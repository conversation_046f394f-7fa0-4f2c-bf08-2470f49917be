<template>
    <view class="generic-list-page">
        <!-- 顶部控制区域 -->
        <view class="control-section">
            <!-- 选项卡切换 -->
            <view v-if="tabs.length > 0" class="tab-header">
                <view v-for="tab in tabs" :key="tab.key" class="tab-item" :class="{ active: activeTab === tab.key }"
                    @tap="switchTab(tab.key)">
                    {{ tab.name }}
                </view>
            </view>

            <!-- 时间筛选 -->
            <TimeFilter v-if="showTimeFilter" v-model="activeTimeFilter" @change="handleTimeFilterChange" />

            <!-- 搜索栏 -->
            <view class="search-box">
                <view class="search-input">
                    <image src="/assets/images/search.png" mode="widthFix" class="search-icon"></image>
                    <input type="text" v-model="searchKeyword" :placeholder="searchPlaceholder" @input="handleSearch" />
                </view>
                <view v-if="showAddButton" class="add-btn-container">
                    <view class="add-btn" @tap="handleAdd">
                        <text class="iconfont icon-add"></text>
                        <text>{{ addButtonText }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 列表容器 -->
        <view class="list-container" :style="{ height: listHeight + 'px' }">
            <scroll-view class="scroll-list" scroll-y>
                <view v-for="item in filteredList" :key="item.id" class="list-item" @tap="handleItemClick(item)">
                    <component :is="itemComponent" :item="item" :timeFilter="activeTimeFilter"
                        :customDateRange="customDateRange" v-bind="itemComponentProps" @action="handleItemAction" />
                </view>
                <view class="list-bottom-space"></view>
            </scroll-view>
        </view>

        <!-- 加载更多 -->
        <u-loadmore v-if="showLoadMore" :status="loadMoreStatus" @loadmore="loadMore" />

        <!-- 空状态 -->
        <u-empty v-if="showEmpty" mode="data" :text="emptyText" />
    </view>
</template>

<script>
import TimeFilter from './TimeFilter.vue'
import { createSearchDebouncer } from '../utils/pagination-helper.js'

export default {
    name: 'GenericListPage',
    components: {
        TimeFilter
    },
    props: {
        // 列表数据
        list: {
            type: Array,
            default: () => []
        },

        // 选项卡配置
        tabs: {
            type: Array,
            default: () => []
        },

        // 列表项组件
        itemComponent: {
            type: String,
            required: true
        },

        // 列表项组件属性
        itemComponentProps: {
            type: Object,
            default: () => ({})
        },

        // 搜索配置
        searchPlaceholder: {
            type: String,
            default: '搜索...'
        },

        // 时间筛选
        showTimeFilter: {
            type: Boolean,
            default: true
        },

        // 添加按钮
        showAddButton: {
            type: Boolean,
            default: false
        },
        addButtonText: {
            type: String,
            default: '添加'
        },

        // 空状态
        emptyText: {
            type: String,
            default: '暂无数据'
        },

        // 加载更多
        showLoadMore: {
            type: Boolean,
            default: false
        },
        loadMoreStatus: {
            type: String,
            default: 'loadmore'
        }
    },

    data () {
        return {
            activeTab: '',
            searchKeyword: '',
            activeTimeFilter: 'today',
            customDateRange: {
                startDate: '',
                endDate: ''
            },
            listHeight: 0,
            searchDebouncer: null
        }
    },

    computed: {
        filteredList () {
            let filtered = this.list

            // 选项卡过滤
            if (this.activeTab && this.tabs.length > 0) {
                filtered = filtered.filter(item => item.type === this.activeTab)
            }

            // 搜索过滤
            if (this.searchKeyword) {
                const keyword = this.searchKeyword.toLowerCase()
                filtered = filtered.filter(item =>
                    (item.name || item.title || item.username || '').toLowerCase().includes(keyword)
                )
            }

            return filtered
        },

        showEmpty () {
            return this.filteredList.length === 0 && !this.loading
        }
    },

    created () {
        // 初始化选项卡
        if (this.tabs.length > 0) {
            this.activeTab = this.tabs[0].key
        }

        // 初始化搜索防抖
        this.searchDebouncer = createSearchDebouncer(this.handleSearchDebounced, 500)
    },

    mounted () {
        this.calculateListHeight()
    },

    methods: {
        // 切换选项卡
        switchTab (tabKey) {
            this.activeTab = tabKey
            this.$emit('tab-change', tabKey)
        },

        // 时间筛选变化
        handleTimeFilterChange (filter) {
            this.activeTimeFilter = filter
            this.$emit('time-filter-change', filter)
        },

        // 搜索处理
        handleSearch () {
            if (this.searchDebouncer) {
                this.searchDebouncer()
            }
        },

        // 搜索防抖处理
        handleSearchDebounced () {
            this.$emit('search', this.searchKeyword)
        },

        // 添加按钮点击
        handleAdd () {
            this.$emit('add')
        },

        // 列表项点击
        handleItemClick (item) {
            this.$emit('item-click', item)
        },

        // 列表项操作
        handleItemAction (action, item) {
            this.$emit('item-action', { action, item })
        },

        // 加载更多
        loadMore () {
            this.$emit('load-more')
        },

        // 计算列表高度
        calculateListHeight () {
            uni.getSystemInfo({
                success: (res) => {
                    const windowHeight = res.windowHeight
                    const controlHeight = 200 // 控制区域预估高度
                    const tabbarHeight = 50
                    const safeAreaBottom = res.safeAreaInsets?.bottom || 0

                    this.listHeight = Math.max(
                        windowHeight - controlHeight - tabbarHeight - safeAreaBottom,
                        400
                    )
                }
            })
        }
    }
}
</script>

<style scoped>
.generic-list-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.control-section {
    padding: 20rpx;
    background: #fff;
    border-bottom: 1rpx solid #eee;
}

.tab-header {
    display: flex;
    margin-bottom: 20rpx;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    border-bottom: 2rpx solid transparent;
    color: #666;
}

.tab-item.active {
    color: #186BFF;
    border-bottom-color: #186BFF;
}

.search-box {
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 20rpx;
    padding: 0 20rpx;
}

.search-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 10rpx;
}

.search-input input {
    flex: 1;
    height: 60rpx;
    font-size: 28rpx;
}

.add-btn-container {
    flex-shrink: 0;
}

.add-btn {
    display: flex;
    align-items: center;
    background: #186BFF;
    color: #fff;
    padding: 15rpx 30rpx;
    border-radius: 20rpx;
    font-size: 28rpx;
}

.list-container {
    flex: 1;
    overflow: hidden;
}

.scroll-list {
    height: 100%;
}

.list-item {
    margin-bottom: 20rpx;
}

.list-bottom-space {
    height: 100rpx;
}
</style>