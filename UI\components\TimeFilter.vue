<template>
	<view class="time-filter">
		<view class="filter-buttons">
			<!-- 正常模式显示所有按钮 -->
			<template v-if="!showCustomInputs">
				<view class="filter-item" :class="{ active: activeFilter === 'today' }" @tap="setFilter('today')">今日
				</view>
				<view class="filter-item" :class="{ active: activeFilter === 'yesterday' }"
					@tap="setFilter('yesterday')">昨日</view>
				<view class="filter-item" :class="{ active: activeFilter === 'thisWeek' }" @tap="setFilter('thisWeek')">
					本周</view>
				<view class="filter-item" :class="{ active: activeFilter === 'thisMonth' }"
					@tap="setFilter('thisMonth')">本月</view>
				<view class="filter-item" :class="{ active: activeFilter === 'custom' }" @tap="toggleCustomDatePicker">
					自定义</view>
			</template>

			<!-- 自定义输入框模式 - 只显示输入框和返回按钮 -->
			<template v-else>
				<view class="back-button" @tap="closeCustomInputs">
					<!-- <text class="back-icon">←</text> -->
					<text class="back-text">返回</text>
				</view>
				<view class="custom-inputs-container">
					<view class="date-input-item">
						<picker mode="date" :value="startDate" @change="onStartDateChange">
							<view class="date-input-inline">{{ startDate || '开始日期' }}</view>
						</picker>
					</view>
					<view class="date-input-item">
						<picker mode="date" :value="endDate" @change="onEndDateChange">
							<view class="date-input-inline">{{ endDate || '结束日期' }}</view>
						</picker>
					</view>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TimeFilter',
	props: {
		// 当前选中的筛选项
		value: {
			type: String,
			default: 'today'
		}
	},
	data () {
		return {
			activeFilter: this.value,
			showCustomInputs: false,
			startDate: '',
			endDate: ''
		}
	},
	watch: {
		value (newVal) {
			this.activeFilter = newVal;
		}
	},
	created () {
		// 初始化日期为当前日期前一个月到当前日期
		const now = new Date();
		const oneMonthAgo = new Date();
		oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

		this.startDate = this.formatDate(oneMonthAgo);
		this.endDate = this.formatDate(now);
	},
	methods: {
		// 设置筛选项
		setFilter (filter) {
			this.activeFilter = filter;
			// 如果选择其他筛选项，关闭自定义输入框
			if (filter !== 'custom') {
				this.showCustomInputs = false;
			}

			// 获取对应的时间范围
			const timeRange = this.getTimeRange(filter);

			// 统一返回时间范围对象
			const result = {
				type: filter,
				startDate: timeRange.startDate,
				endDate: timeRange.endDate
			};

			this.$emit('input', result);
			this.$emit('change', result);
		},

		// 切换到自定义日期模式
		toggleCustomDatePicker () {
			this.activeFilter = 'custom';
			this.showCustomInputs = true;
		},

		// 关闭自定义输入框
		closeCustomInputs () {
			this.showCustomInputs = false;
			this.activeFilter = 'today'; // 切换回今日
			this.setFilter('today');
		},

		// 开始日期变化 - 直接触发返回
		onStartDateChange (e) {
			this.startDate = e.detail.value;
			this.emitCustomDateRange();
		},

		// 结束日期变化 - 直接触发返回
		onEndDateChange (e) {
			this.endDate = e.detail.value;
			this.emitCustomDateRange();
		},

		// 触发自定义日期范围事件
		emitCustomDateRange () {
			// 只有当两个日期都有值时才触发
			if (this.startDate && this.endDate) {
				// 验证日期范围
				if (new Date(this.startDate) > new Date(this.endDate)) {
					uni.showToast({
						title: '开始日期不能大于结束日期',
						icon: 'none'
					});
					return;
				}

				// 统一返回时间范围对象
				const result = {
					type: 'custom',
					startDate: this.startDate,
					endDate: this.endDate
				};

				this.$emit('input', result);
				this.$emit('change', result);
			}
		},

		// 格式化日期为 YYYY-MM-DD
		formatDate (date) {
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		},

		// 获取指定筛选项的时间范围
		getTimeRange (filter = this.activeFilter) {
			switch (filter) {
				case 'today':
					return {
						startDate: this.formatDate(new Date()),
						endDate: this.formatDate(new Date())
					};
				case 'yesterday':
					const yesterday = new Date();
					yesterday.setDate(yesterday.getDate() - 1);
					return {
						startDate: this.formatDate(yesterday),
						endDate: this.formatDate(yesterday)
					};
				case 'thisWeek':
					const today = new Date();
					const dayOfWeek = today.getDay() || 7; // 如果是0（周日）则设为7
					const startOfWeek = new Date(today);
					startOfWeek.setDate(today.getDate() - dayOfWeek + 1); // 周一
					return {
						startDate: this.formatDate(startOfWeek),
						endDate: this.formatDate(today)
					};
				case 'thisMonth':
					const currentDate = new Date();
					const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
					return {
						startDate: this.formatDate(startOfMonth),
						endDate: this.formatDate(currentDate)
					};
				case 'custom':
					return {
						startDate: this.startDate,
						endDate: this.endDate
					};
				default:
					return {
						startDate: this.formatDate(new Date()),
						endDate: this.formatDate(new Date())
					};
			}
		}
	}
}
</script>

<style>
.time-filter {
	background-color: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	height: 4rem;
}

.filter-buttons {
	display: flex;
	padding: 0rem 0.5rem;
	flex-wrap: nowrap;
	align-items: center;
	gap: 12rpx;
	height: 100%;
	justify-content: space-between;
}

.filter-item {
	padding: 0.5625rem 1.1rem;
	font-size: 26rpx;
	color: #666;
	background-color: #f5f5f5;
	border-radius: 10rpx;
	white-space: nowrap;
	text-align: center;
}

.filter-item.active {
	background-color: #186BFF;
	color: #fff;
	box-shadow: 0 2rpx 6rpx rgba(24, 144, 255, 0.2);
}

/* 自定义日期输入区域样式 */
.date-inputs {
	display: flex;
	gap: 20rpx;
	background-color: #f8f9fa;
}

.date-input-item {
	flex: 1;
}

.date-input {
	width: 100%;
	padding: 10rpx 12rpx;
	background-color: #fff;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #333;
	border: 2rpx solid #e9ecef;
	box-sizing: border-box;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}

.date-input:active {
	border-color: #186BFF;
	box-shadow: 0 2rpx 12rpx rgba(24, 107, 255, 0.15);
}

/* 内联自定义输入框样式 */
.custom-inputs-inline {
	display: flex;
	align-items: center;
	gap: 8rpx;
	flex: 1;
}

.date-input-inline {
	padding: 0.5625rem 0.75rem;
	background-color: #fff;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #333;
	border: 2rpx solid #e9ecef;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	min-width: 80rpx;
}

.date-input-inline:active {
	border-color: #186BFF;
	box-shadow: 0 2rpx 12rpx rgba(24, 107, 255, 0.15);
}

.close-custom {
	padding: 8rpx 12rpx;
	background-color: #f5f5f5;
	border-radius: 50%;
	font-size: 24rpx;
	color: #999;
	text-align: center;
	transition: all 0.3s ease;
	cursor: pointer;
}

.close-custom:active {
	background-color: #e9ecef;
	color: #666;
}

/* 返回按钮样式 */
.back-button {
	display: flex;
	align-items: center;
	gap: 4rpx;
	padding: 0.5625rem 1rem;
	background-color: #186BFF;
	border-radius: 10rpx;
	box-shadow: 0 2rpx 6rpx rgba(24, 107, 255, 0.2);
}

.back-button:active {
	background-color: #1456d9;
	box-shadow: 0 2rpx 8rpx rgba(24, 107, 255, 0.3);
}

.back-icon {
	font-size: 24rpx;
	color: #fff;
}

.back-text {
	font-size: 24rpx;
	color: #fff;
}

/* 自定义输入框容器 */
.custom-inputs-container {
	display: flex;
	align-items: center;
	gap: 12rpx;
	flex: 1;
	margin-left: 12rpx;
}

.custom-inputs-container .date-input-inline {
	padding: 18rpx 16rpx;
	font-size: 26rpx;
	min-width: 120rpx;
}
</style>