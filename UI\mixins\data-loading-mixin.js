/**
 * 数据加载通用混入
 * 提供统一的数据加载、错误处理、状态管理功能
 */
import { apiCallWrapper, ErrorHandlerPresets } from "../utils/api-error-handler.js";
import { PaginationManager } from "../utils/pagination-helper.js";

export default {
  data() {
    return {
      // 加载状态
      loading: false,
      refreshing: false,
      loadingMore: false,
      
      // 错误状态
      error: null,
      errorMessage: '',
      
      // 数据状态
      dataList: [],
      hasData: false,
      isEmpty: false,
      
      // 分页状态
      paginationManager: null,
      hasMore: true,
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      
      // 搜索和筛选
      searchKeyword: '',
      filterParams: {},
      lastSearchParams: {},
    };
  },

  computed: {
    /**
     * 是否显示加载状态
     */
    showLoading() {
      return this.loading && !this.refreshing;
    },

    /**
     * 是否显示空状态
     */
    showEmpty() {
      return !this.loading && !this.error && this.isEmpty;
    },

    /**
     * 是否显示错误状态
     */
    showError() {
      return !this.loading && this.error;
    },

    /**
     * 是否显示数据列表
     */
    showDataList() {
      return !this.loading && !this.error && this.hasData;
    },

    /**
     * 获取搜索参数
     */
    searchParams() {
      return {
        ...this.filterParams,
        keyword: this.searchKeyword,
        PageIndex: this.currentPage,
        PageSize: this.pageSize
      };
    }
  },

  methods: {
    /**
     * 初始化数据加载
     * 子类需要重写此方法来设置API调用
     */
    initDataLoading() {
      throw new Error('子类必须实现 initDataLoading 方法');
    },

    /**
     * 获取API调用函数
     * 子类需要重写此方法
     */
    getApiCall() {
      throw new Error('子类必须实现 getApiCall 方法');
    },

    /**
     * 格式化数据
     * 子类可以重写此方法来格式化API返回的数据
     */
    formatData(data) {
      return data;
    },

    /**
     * 加载数据
     */
    async loadData(showLoading = true, isRefresh = false) {
      try {
        if (showLoading) {
          this.loading = true;
        }
        if (isRefresh) {
          this.refreshing = true;
        }
        
        this.error = null;
        this.errorMessage = '';

        const apiCall = this.getApiCall();
        if (!apiCall) {
          throw new Error('API调用函数未定义');
        }

        const response = await apiCallWrapper(
          () => apiCall(this.searchParams),
          {
            ...ErrorHandlerPresets.quick,
            showLoading: false, // 我们自己管理loading状态
            showError: false    // 我们自己处理错误显示
          }
        );

        if (response.success && response.data) {
          let formattedData = this.formatData(response.data);
          
          // 处理分页数据
          if (Array.isArray(formattedData)) {
            this.dataList = formattedData;
          } else if (formattedData.items && Array.isArray(formattedData.items)) {
            this.dataList = formattedData.items;
            this.totalCount = formattedData.totalCount || 0;
            this.hasMore = this.dataList.length < this.totalCount;
          } else {
            this.dataList = [];
          }

          this.hasData = this.dataList.length > 0;
          this.isEmpty = this.dataList.length === 0;
          
          // 保存搜索参数
          this.lastSearchParams = { ...this.searchParams };
          
          return response;
        } else {
          throw new Error(response.msg || '数据加载失败');
        }
      } catch (error) {
        console.error('数据加载失败:', error);
        this.error = error;
        this.errorMessage = error.message || '数据加载失败';
        this.dataList = [];
        this.hasData = false;
        this.isEmpty = true;
        
        // 显示错误提示
        uni.showToast({
          title: this.errorMessage,
          icon: 'none'
        });
        
        throw error;
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    /**
     * 刷新数据
     */
    async refreshData() {
      this.currentPage = 1;
      return await this.loadData(false, true);
    },

    /**
     * 重新加载数据
     */
    async reloadData() {
      this.currentPage = 1;
      return await this.loadData(true, false);
    },

    /**
     * 搜索数据
     */
    async searchData(keyword = '') {
      this.searchKeyword = keyword;
      this.currentPage = 1;
      return await this.loadData(true, false);
    },

    /**
     * 筛选数据
     */
    async filterData(params = {}) {
      this.filterParams = { ...params };
      this.currentPage = 1;
      return await this.loadData(true, false);
    },

    /**
     * 加载更多数据
     */
    async loadMoreData() {
      if (this.loadingMore || !this.hasMore) {
        return;
      }

      try {
        this.loadingMore = true;
        this.currentPage++;

        const apiCall = this.getApiCall();
        const response = await apiCallWrapper(
          () => apiCall(this.searchParams),
          ErrorHandlerPresets.silent
        );

        if (response.success && response.data) {
          let formattedData = this.formatData(response.data);
          let newItems = [];
          
          if (Array.isArray(formattedData)) {
            newItems = formattedData;
          } else if (formattedData.items && Array.isArray(formattedData.items)) {
            newItems = formattedData.items;
            this.totalCount = formattedData.totalCount || 0;
          }

          this.dataList = [...this.dataList, ...newItems];
          this.hasMore = this.dataList.length < this.totalCount;
          this.hasData = this.dataList.length > 0;
          this.isEmpty = this.dataList.length === 0;
        }
      } catch (error) {
        console.error('加载更多数据失败:', error);
        this.currentPage--; // 回退页码
        uni.showToast({
          title: '加载更多失败',
          icon: 'none'
        });
      } finally {
        this.loadingMore = false;
      }
    },

    /**
     * 处理下拉刷新
     */
    async handlePullDownRefresh() {
      try {
        await this.refreshData();
      } finally {
        uni.stopPullDownRefresh();
      }
    },

    /**
     * 处理上拉加载更多
     */
    handleReachBottom() {
      this.loadMoreData();
    },

    /**
     * 重试加载
     */
    async retryLoad() {
      return await this.reloadData();
    },

    /**
     * 清空数据
     */
    clearData() {
      this.dataList = [];
      this.hasData = false;
      this.isEmpty = true;
      this.error = null;
      this.errorMessage = '';
      this.currentPage = 1;
      this.hasMore = true;
      this.totalCount = 0;
    }
  },

  /**
   * 页面生命周期
   */
  onLoad() {
    // 初始化数据加载配置
    if (this.initDataLoading) {
      this.initDataLoading();
    }
  },

  onShow() {
    // 页面显示时可以选择性刷新数据
    if (this.shouldRefreshOnShow && this.shouldRefreshOnShow()) {
      this.refreshData();
    }
  },

  onPullDownRefresh() {
    this.handlePullDownRefresh();
  },

  onReachBottom() {
    this.handleReachBottom();
  }
};
