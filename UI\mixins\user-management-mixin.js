/**
 * 用户管理页面通用功能混入
 * 提供搜索、分页、列表高度计算等通用功能
 */
import { SmartCache, CACHE_TYPES } from "../utils/cache-manager.js";
import { createSearchDebouncer } from "../utils/pagination-helper.js";

export default {
  data () {
    return {
      // 搜索相关
      searchKeyword: "",
      searchDebouncer: null,

      // 分页相关
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      hasMore: true,
      loading: false,

      // 时间筛选
      activeTimeFilter: "today",
      customDateRange: {
        startDate: "",
        endDate: "",
      },

      // 列表高度
      listHeight: 0,

      // 页面配置
      hasPageHeader: false, // 是否有页面头部
      selectedManagerId: null, // 选中的管理员ID（用于员工页面）
    };
  },

  created () {
    // 初始化搜索防抖
    this.searchDebouncer = createSearchDebouncer(this.performSearch, 500);
  },

  methods: {
    /**
     * 计算列表高度
     */
    calculateListHeight () {
      const that = this;
      uni.getSystemInfo({
        success (res) {
          that.windowHeight = res.windowHeight;
          setTimeout(() => {
            const query = uni.createSelectorQuery().in(that);
            query.select(".control-section").boundingClientRect();
            query.exec((data) => {
              if (data && data[0]) {
                const controlHeight = data[0].height || 140; // 进一步减少默认控制区域高度
                const tabbarHeight = 40; // 进一步减少 tabbar 高度
                const bottomSafeArea = res.safeAreaInsets?.bottom || 0;
                // 根据页面类型调整头部高度
                const headerHeight = that.hasPageHeader ? 70 : 0; // 进一步减少头部高度
                const padding = 10; // 进一步减少内边距

                that.listHeight = Math.max(
                  that.windowHeight -
                  controlHeight -
                  tabbarHeight -
                  bottomSafeArea -
                  headerHeight -
                  padding,
                  550 // 进一步增加最小高度
                );
              } else {
                // 如果无法获取控制区域高度，使用默认计算
                that.listHeight = that.windowHeight - 200; // 进一步减少预估高度
              }
            });
          }, 500); // 增加延迟确保DOM渲染完成
        },
      });
    },

    /**
     * 修复TabBar层级
     */
    fixTabBarZIndex () {
      setTimeout(() => {
        uni.setTabBarStyle({
          zIndex: 9999,
        });
      }, 100);
    },

    /**
     * 时间筛选变化处理
     */
    handleTimeFilterChange (filter) {
      this.activeTimeFilter = filter;
    },

    /**
     * 自定义日期变化处理
     */
    handleCustomDateChange (dateRange) {
      this.customDateRange = dateRange;
    },

    /**
     * 搜索输入处理
     */
    onSearchInput () {
      if (this.searchDebouncer) {
        this.searchDebouncer();
      }
    },

    /**
     * 执行搜索 - 需要在具体页面中实现
     */
    async performSearch () {
      // 清除缓存
      SmartCache.clearType(CACHE_TYPES.EMPLOYEE_LIST);
      // 重新加载数据 - 子类需要实现 loadData 方法
      if (this.loadData) {
        await this.loadData(true);
      }
    },

    /**
     * 搜索确认处理
     */
    async handleSearch () {
      // 直接执行搜索
      await this.performSearch();
    },

    /**
     * 清除搜索
     */
    clearSearch () {
      this.searchKeyword = "";
    },

    /**
     * 加载更多数据
     */
    async loadMore () {
      if (!this.hasMore || this.loading) return;

      this.currentPage++;
      if (this.loadData) {
        await this.loadData(false);
      }
    },

    /**
     * 下拉刷新处理 - 在页面中调用
     */
    async handlePullDownRefresh () {
      if (this.loadData) {
        await this.loadData(true);
      }
      uni.stopPullDownRefresh();
    },

    /**
     * 上拉加载更多处理 - 在页面中调用
     */
    handleReachBottom () {
      this.loadMore();
    },

    /**
     * 页面显示处理 - 在页面中调用
     */
    handleShow () {
      this.fixTabBarZIndex();
      this.calculateListHeight();
    },
  },
};
