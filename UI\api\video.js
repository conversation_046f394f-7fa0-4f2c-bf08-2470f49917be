/**
 * 视频管理相关API
 * 处理视频的上传、管理、播放记录等功能
 */

import request from '../utils/request.js'

/**
 * 视频查询参数
 * @typedef {Object} VideoQueryParams
 * @property {string} [title] - 视频标题
 * @property {string} [category] - 视频分类
 * @property {string} [status] - 状态
 * @property {string} [creator] - 创建者
 * @property {string} [createTimeStart] - 创建时间开始
 * @property {string} [createTimeEnd] - 创建时间结束
 * @property {number} [minDuration] - 最小时长(秒)
 * @property {number} [maxDuration] - 最大时长(秒)
 * @property {number} page - 页码
 * @property {number} pageSize - 每页大小
 */

/**
 * 视频数据
 * @typedef {Object} VideoDto
 * @property {number} id - 视频ID
 * @property {string} title - 视频标题
 * @property {string} description - 视频描述
 * @property {string} url - 视频地址
 * @property {string} coverUrl - 封面图片地址
 * @property {number} duration - 视频时长(秒)
 * @property {string} category - 视频分类
 * @property {Array<string>} tags - 标签列表
 * @property {string} status - 状态
 * @property {number} viewCount - 观看次数
 * @property {number} likeCount - 点赞次数
 * @property {string} creator - 创建者
 * @property {string} creatorName - 创建者姓名
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 * @property {string} publishTime - 发布时间
 * @property {number} fileSize - 文件大小(字节)
 * @property {string} format - 视频格式
 * @property {string} resolution - 分辨率
 */

/**
 * 创建视频请求
 * @typedef {Object} CreateVideoDto
 * @property {string} title - 视频标题
 * @property {string} description - 视频描述
 * @property {string} url - 视频地址
 * @property {string} [coverUrl] - 封面图片地址
 * @property {number} duration - 视频时长(秒)
 * @property {string} category - 视频分类
 * @property {Array<string>} [tags] - 标签列表
 * @property {string} [status] - 状态
 * @property {number} [fileSize] - 文件大小(字节)
 * @property {string} [format] - 视频格式
 * @property {string} [resolution] - 分辨率
 */

/**
 * 更新视频请求
 * @typedef {Object} UpdateVideoDto
 * @property {string} [title] - 视频标题
 * @property {string} [description] - 视频描述
 * @property {string} [coverUrl] - 封面图片地址
 * @property {string} [category] - 视频分类
 * @property {Array<string>} [tags] - 标签列表
 * @property {string} [status] - 状态
 */

/**
 * 视频播放记录
 * @typedef {Object} VideoPlayRecord
 * @property {number} id - 记录ID
 * @property {number} videoId - 视频ID
 * @property {number} userId - 用户ID
 * @property {string} username - 用户名
 * @property {string} userName - 用户姓名
 * @property {number} playDuration - 播放时长(秒)
 * @property {number} totalDuration - 视频总时长(秒)
 * @property {number} progress - 播放进度(百分比)
 * @property {boolean} completed - 是否完成观看
 * @property {string} playTime - 播放时间
 * @property {string} deviceInfo - 设备信息
 * @property {string} ipAddress - IP地址
 */

/**
 * 视频分类
 * @typedef {Object} VideoCategory
 * @property {string} value - 分类值
 * @property {string} label - 分类名称
 * @property {string} [description] - 分类描述
 * @property {number} [sort] - 排序
 */

/**
 * 查询视频列表
 * @param {VideoQueryParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/common.js').PageResult<VideoDto>>>} 视频列表
 */
export function queryVideos (params) {
  return request.get('/Video', params)
}

/**
 * 获取视频详情
 * @param {number} videoId - 视频ID
 * @returns {Promise<import('./types/common.js').ApiResult<VideoDto>>} 视频详情
 */
export function getVideoDetail (videoId) {
  return request.get(`/Video/Get/${videoId}`)
}

/**
 * 创建视频
 * @param {CreateVideoDto} data - 视频数据
 * @returns {Promise<import('./types/common.js').ApiResult<VideoDto>>} 创建结果
 */
export function createVideo (data) {
  return request.post('/Video', data)
}

/**
 * 更新视频
 * @param {number} videoId - 视频ID
 * @param {UpdateVideoDto} data - 更新数据
 * @returns {Promise<import('./types/common.js').ApiResult<VideoDto>>} 更新结果
 */
export function updateVideo (videoId, data) {
  return request.post('/Video/update', { id: videoId, ...data })
}

/**
 * 删除视频
 * @param {number} videoId - 视频ID
 * @returns {Promise<import('./types/common.js').ApiResult<boolean>>} 删除结果
 */
export function deleteVideo (videoId) {
  return request.delete(`/Video/Delete/${videoId}`)
}

/**
 * 批量删除视频 (暂未实现，接口文档中无此API)
 * @param {Array<number>} videoIds - 视频ID列表
 * @returns {Promise<import('./types/common.js').ApiResult<boolean>>} 删除结果
 */
export function batchDeleteVideos (videoIds) {
  // 暂时使用循环调用单个删除API
  return Promise.all(videoIds.map(id => deleteVideo(id)))
}

/**
 * 发布/下架视频
 * @param {number} videoId - 视频ID
 * @param {number} status - 状态 (0=待发布, 1=已发布, 2=已下架)
 * @returns {Promise<import('./types/common.js').ApiResult<boolean>>} 更新结果
 */
export function toggleVideoStatus (videoId, status) {
  return request.post(`/Video/${videoId}/status`, { id: videoId, status })
}

/**
 * 上传视频文件
 * @param {FormData} formData - 包含视频文件的表单数据
 * @param {Function} [onProgress] - 上传进度回调
 * @returns {Promise<import('./types/common.js').ApiResult<{url: string, duration: number, fileSize: number, format: string, resolution: string}>>} 上传结果
 */
export function uploadVideo (formData, onProgress) {
  return request.post('/Video/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: onProgress
  })
}

/**
 * 完整视频上传（一次性上传视频文件并创建视频记录）
 * @param {FormData} formData - 包含视频文件和相关信息的表单数据
 * @param {Function} [onProgress] - 上传进度回调
 * @returns {Promise<import('./types/common.js').ApiResult<{videoId: number, videoUrl: string, coverUrl: string, duration: number, fileSize: number, videoFormat: string, resolution: string, success: boolean, message: string}>>} 上传结果
 */
export function uploadVideoComplete (formData, onProgress) {
  return request.post('/Video/upload-complete', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: onProgress
  })
}

/**
 * 上传视频封面 (暂未实现，接口文档中无此API)
 * @param {FormData} formData - 包含封面图片的表单数据
 * @returns {Promise<import('./types/common.js').ApiResult<{url: string}>>} 上传结果
 */
export function uploadVideoCover (formData) {
  // 暂时返回模拟数据，避免404错误
  console.log('封面上传功能暂未实现，formData:', formData)
  return Promise.resolve({
    success: true,
    data: { url: '/assets/images/video-cover.jpg' },
    msg: '封面上传功能暂未实现'
  })
}

/**
 * 获取视频分类列表 (暂未实现，接口文档中无此API)
 * @returns {Promise<import('./types/common.js').ApiResult<Array<VideoCategory>>>} 分类列表
 */
export function getVideoCategories () {
  // 返回模拟分类数据
  return Promise.resolve({
    success: true,
    data: [
      { id: 1, name: '健康知识', description: '健康相关视频' },
      { id: 2, name: '教育', description: '教育培训视频' },
      { id: 3, name: '金融', description: '金融理财视频' },
      { id: 4, name: '科技', description: '科技创新视频' },
      { id: 5, name: '职场', description: '职场技能视频' },
      { id: 6, name: '生活方式', description: '生活方式视频' }
    ],
    msg: '分类列表获取成功'
  })
}

/**
 * 获取视频标签列表 (暂未实现，接口文档中无此API)
 * @returns {Promise<import('./types/common.js').ApiResult<Array<string>>>} 标签列表
 */
export function getVideoTags () {
  // 返回模拟标签数据
  return Promise.resolve({
    success: true,
    data: ['热门', '推荐', '新品', '精选', '必看'],
    msg: '标签列表获取成功'
  })
}



/**
 * 获取用户视频观看进度 (暂未实现，接口文档中无此API)
 * @param {number} videoId - 视频ID
 * @returns {Promise<import('./types/common.js').ApiResult<{progress: number, playDuration: number, completed: boolean}>>} 观看进度
 */
export function getUserVideoProgress (videoId) {
  console.log('获取观看进度:', videoId)
  return Promise.resolve({
    success: true,
    data: {
      progress: 0,
      playDuration: 0,
      completed: false
    },
    msg: '观看进度获取成功'
  })
}

/**
 * 点赞/取消点赞视频 (暂未实现，接口文档中无此API)
 * @param {number} videoId - 视频ID
 * @param {boolean} liked - 是否点赞
 * @returns {Promise<import('./types/common.js').ApiResult<{liked: boolean, likeCount: number}>>} 点赞结果
 */
export function toggleVideoLike (videoId, liked) {
  console.log('点赞操作:', videoId, liked)
  return Promise.resolve({
    success: true,
    data: {
      liked: liked,
      likeCount: Math.floor(Math.random() * 100)
    },
    msg: liked ? '点赞成功' : '取消点赞成功'
  })
}

/**
 * 获取视频统计信息
 * @param {number} videoId - 视频ID
 * @returns {Promise<import('./types/common.js').ApiResult<VideoVideoStatisticsDto>>} 统计信息
 */
export function getVideoStats (videoId) {
  return request.get(`/Video/${videoId}/statistics`)
}

/**
 * 搜索视频
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @param {number} [params.pageIndex=1] - 页码
 * @param {number} [params.pageSize=20] - 每页大小
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/common.js').PageResult<VideoDto>>>} 搜索结果
 */
export function searchVideos (params) {
  return request.get('/Video/search', params)
}

/**
 * 获取创建者的视频列表
 * @param {number} creatorId - 创建者ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.status] - 状态筛选
 * @returns {Promise<import('./types/common.js').ApiResult<Array<VideoDto>>>} 视频列表
 */
export function getVideosByCreator (creatorId, options = {}) {
  return request.get(`/Video/creator/${creatorId}`, options)
}

/**
 * 获取我的视频列表
 * @param {Object} [options] - 查询选项
 * @param {number} [options.status] - 状态筛选
 * @returns {Promise<import('./types/common.js').ApiResult<Array<VideoDto>>>} 我的视频列表
 */
export function getMyVideos (options = {}) {
  return request.get('/Video/my-videos', options)
}

/**
 * 导出视频数据 (暂未实现，接口文档中无此API)
 * @param {VideoQueryParams} params - 查询参数
 * @returns {Promise<Blob>} 导出文件
 */
export function exportVideos (params) {
  console.log('导出视频数据:', params)
  return Promise.resolve(new Blob(['暂未实现导出功能'], { type: 'text/plain' }))
}

export default {
  queryVideos,
  getVideoDetail,
  createVideo,
  updateVideo,
  deleteVideo,
  batchDeleteVideos,
  toggleVideoStatus,
  uploadVideo,
  uploadVideoCover,
  getVideoCategories,
  getVideoTags,
  getUserVideoProgress,
  toggleVideoLike,
  getVideoStats,
  searchVideos,
  getVideosByCreator,
  getMyVideos,
  exportVideos
}
