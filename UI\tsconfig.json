{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "sourceMap": true, "baseUrl": ".", "allowJs": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "paths": {"@/*": ["./*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "vueCompilerOptions": {"experimentalDisableTemplateSupport": false, "nativeTags": ["block", "component", "template", "slot"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.vue", "**/*.js"], "exclude": ["node_modules", "unpackage", "dist"]}