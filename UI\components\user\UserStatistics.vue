<template>
    <view class="stats-section">
        <view class="stat-item">
            <view class="stat-content">
                <text class="stat-value">观看视频:{{ getViewCount() }}</text>
            </view>
        </view>
        <view class="stat-item">
            <view class="stat-content">
                <text class="stat-value">答题数量:{{ getQuizCount() }}</text>
            </view>
        </view>
        <view class="stat-item">
            <view class="stat-content">
                <text class="stat-value">{{ getRewardLabel() }}:{{ getRewardAmount() }}</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'UserStatistics',
    props: {
        userInfo: {
            type: Object,
            required: true
        },
        timeFilter: {
            type: String,
            default: 'today'
        }
    },
    methods: {
        getViewCount () {
            return this.userInfo.watchedVideos ||
                (this.userInfo.totalViews && this.userInfo.totalViews[this.timeFilter]) ||
                0;
        },

        getQuizCount () {
            return this.userInfo.completedQuizzes ||
                (this.userInfo.totalQuizzes && this.userInfo.totalQuizzes[this.timeFilter]) ||
                0;
        },

        getRewardAmount () {
            const rewards = this.userInfo.totalRewards;
            if (!rewards) return 0;

            if (typeof rewards === 'object') {
                return rewards[this.timeFilter] || 0;
            }

            return rewards;
        },

        getRewardLabel () {
            return this.userInfo.type !== "user" ? "奖励总额" : "获得奖励";
        }
    }
}
</script>

<style scoped>
.stats-section {
    display: flex;
    justify-content: space-between;
    padding: 24rpx 0 0rpx 0;
    border-top: 1rpx solid #e8f4ff;
    background: linear-gradient(135deg, rgba(232, 244, 255, 0.3), rgba(240, 248, 255, 0.5));
    margin: 16rpx -32rpx 0 -32rpx;
    padding: 20rpx 32rpx 16rpx 32rpx;
    border-radius: 0 0 20rpx 20rpx;
}

.stat-item {
    display: flex;
    align-items: center;
    flex: 1;
    text-align: center;
    position: relative;
}

.stat-item:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1rpx;
    height: 32rpx;
    background: linear-gradient(180deg, transparent, #bae7ff, transparent);
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.stat-value {
    font-size: 24rpx;
    font-weight: 600;
    color: #186BFF;
    text-shadow: 0 1rpx 2rpx rgba(24, 107, 255, 0.1);
}
</style>