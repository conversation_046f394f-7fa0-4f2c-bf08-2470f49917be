<template>
  <view class="video-container" :class="{ 'fullscreen': isFullscreen }">
    <!-- 原生 video 元素 -->
    <video
      ref="videoElement"
      :src="src"
      :poster="poster"
      class="video-element"
      :controls="true"
      :autoplay="autoplay"
      :muted="false"
      preload="metadata"
      playsinline
      webkit-playsinline
      x5-playsinline
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
      @ended="onEnded"
      @play="onPlay"
      @pause="onPause"
      @fullscreenchange="onFullscreenChange"
      @webkitfullscreenchange="onFullscreenChange"
      @mozfullscreenchange="onFullscreenChange"
      @msfullscreenchange="onFullscreenChange"
    ></video>

    <!-- 防快进遮罩层 -->
    <view 
      v-if="disableSeek" 
      class="seek-blocker"
      @touchstart="blockSeek"
      @touchmove="blockSeek"
      @touchend="blockSeek"
      @click="blockSeek"
    ></view>

    <!-- 全屏控制按钮 -->
    <view class="fullscreen-btn" @click="toggleFullscreen" v-if="!isFullscreen">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
        <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
      </svg>
    </view>

    <!-- 全屏退出按钮 -->
    <view class="exit-fullscreen-btn" @click="exitFullscreen" v-if="isFullscreen">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
        <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
      </svg>
    </view>

    <!-- 标题栏（全屏时显示） -->
    <view class="fullscreen-title" v-if="isFullscreen && title">
      <text class="title-text">{{ title }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SimpleVideoPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    poster: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    disableSeek: {
      type: Boolean,
      default: false
    },
    disableProgressDrag: {
      type: Boolean,
      default: false
    },
    showRate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isPlaying: false,
      isFullscreen: false,
      currentTime: 0,
      duration: 0,
      maxWatchedTime: 0,
      lastValidTime: 0
    }
  },
  mounted() {
    this.initPlayer()
  },
  methods: {
    initPlayer() {
      // 禁用倍速播放
      if (!this.showRate) {
        this.$nextTick(() => {
          const video = this.$refs.videoElement
          if (video) {
            // 移除倍速控制
            video.playbackRate = 1
            video.addEventListener('ratechange', () => {
              if (video.playbackRate !== 1) {
                video.playbackRate = 1
              }
            })
          }
        })
      }
    },

    // 事件处理
    onLoadedMetadata() {
      const video = this.$refs.videoElement
      if (video) {
        this.duration = video.duration || 0
        console.log('视频元数据加载完成，时长:', this.duration)
      }
    },

    onTimeUpdate() {
      const video = this.$refs.videoElement
      if (!video) return

      this.currentTime = video.currentTime || 0

      // 更新最大观看时间
      if (this.currentTime > this.maxWatchedTime) {
        this.maxWatchedTime = this.currentTime
        this.lastValidTime = this.currentTime
      }

      // 防快进检查
      if (this.disableSeek && this.currentTime > this.maxWatchedTime + 2) {
        video.currentTime = this.lastValidTime
        uni.showToast({
          title: '请完整观看视频',
          icon: 'none'
        })
        return
      }

      this.lastValidTime = this.currentTime

      this.$emit('timeupdate', {
        current: this.currentTime,
        duration: this.duration
      })
    },

    onEnded() {
      this.isPlaying = false
      this.maxWatchedTime = this.duration
      this.$emit('ended')
    },

    onPlay() {
      this.isPlaying = true
    },

    onPause() {
      this.isPlaying = false
    },

    onFullscreenChange() {
      // 检测全屏状态
      const isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      )
      
      this.isFullscreen = isFullscreen
      
      if (isFullscreen) {
        this.lockOrientation()
      } else {
        this.unlockOrientation()
      }
      
      this.$emit('fullscreenchange', isFullscreen)
    },

    // 全屏控制
    async toggleFullscreen() {
      if (this.isFullscreen) {
        this.exitFullscreen()
      } else {
        this.enterFullscreen()
      }
    },

    async enterFullscreen() {
      const video = this.$refs.videoElement
      if (!video) return

      try {
        if (video.requestFullscreen) {
          await video.requestFullscreen()
        } else if (video.webkitRequestFullscreen) {
          await video.webkitRequestFullscreen()
        } else if (video.mozRequestFullScreen) {
          await video.mozRequestFullScreen()
        } else if (video.msRequestFullscreen) {
          await video.msRequestFullscreen()
        }
      } catch (error) {
        console.error('进入全屏失败:', error)
      }
    },

    async exitFullscreen() {
      try {
        if (document.exitFullscreen) {
          await document.exitFullscreen()
        } else if (document.webkitExitFullscreen) {
          await document.webkitExitFullscreen()
        } else if (document.mozCancelFullScreen) {
          await document.mozCancelFullScreen()
        } else if (document.msExitFullscreen) {
          await document.msExitFullscreen()
        }
      } catch (error) {
        console.error('退出全屏失败:', error)
      }
    },

    // 强制横屏
    async lockOrientation() {
      if (screen.orientation && screen.orientation.lock) {
        try {
          await screen.orientation.lock('landscape')
        } catch (error) {
          console.log('横屏锁定失败:', error)
        }
      }
    },

    // 解锁屏幕方向
    unlockOrientation() {
      if (screen.orientation && screen.orientation.unlock) {
        try {
          screen.orientation.unlock()
        } catch (error) {
          console.log('屏幕方向解锁失败:', error)
        }
      }
    },

    // 阻止快进操作
    blockSeek(event) {
      if (this.disableSeek) {
        event.preventDefault()
        event.stopPropagation()
        uni.showToast({
          title: '请完整观看视频',
          icon: 'none'
        })
      }
    },

    // 公共方法
    play() {
      const video = this.$refs.videoElement
      if (video) {
        video.play()
      }
    },

    pause() {
      const video = this.$refs.videoElement
      if (video) {
        video.pause()
      }
    },

    seek(time) {
      if (time <= this.maxWatchedTime + 2) {
        const video = this.$refs.videoElement
        if (video) {
          video.currentTime = time
        }
      }
    },

    getCurrentTime() {
      return this.currentTime
    }
  }
}
</script>

<style scoped>
.video-container {
  position: relative;
  width: 100%;
  height: 422rpx;
  background: #000;
  overflow: hidden;
  border-radius: 8rpx;
}

.video-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.seek-blocker {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60rpx;
  z-index: 10;
  background: transparent;
}

.fullscreen-btn {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 11;
}

.exit-fullscreen-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 11;
}

.fullscreen-title {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  right: 120rpx;
  z-index: 11;
}

.title-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}
</style>
