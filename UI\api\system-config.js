/**
 * 系统配置管理相关API
 * 处理系统配置的增删改查、分组管理、批量操作等功能
 */

import request from '../utils/request.js'

/**
 * 系统配置创建请求参数
 * @typedef {Object} VideoSystemConfigCreateDto
 * @property {string} configKey - 配置键
 * @property {string} configValue - 配置值
 * @property {string} description - 描述
 * @property {string} groupName - 分组名称
 * @property {string} configType - 配置类型
 * @property {number} isEnabled - 是否启用 (1:启用 0:禁用)
 */

/**
 * 系统配置更新请求参数
 * @typedef {Object} VideoSystemConfigUpdateDto
 * @property {number} id - 配置ID
 * @property {string} configKey - 配置键
 * @property {string} configValue - 配置值
 * @property {string} configType - 配置类型
 * @property {string} description - 描述
 * @property {number} isEnabled - 是否启用 (1:启用 0:禁用)
 */

/**
 * 系统配置响应数据
 * @typedef {Object} VideoSystemConfigResponseDto
 * @property {number} id - 配置ID
 * @property {string} configKey - 配置键
 * @property {string} configValue - 配置值
 * @property {string} description - 描述
 * @property {string} groupName - 分组名称
 * @property {string} configType - 配置类型
 * @property {number} isEnabled - 是否启用
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 */

/**
 * 添加系统配置
 * @param {VideoSystemConfigCreateDto} data - 配置数据
 * @returns {Promise<{code: number, msg: string, success: boolean, data: number}>}
 */
export async function createSystemConfig (data) {
  return await request({
    url: '/SystemConfig',
    method: 'POST',
    data
  })
}

/**
 * 分页查询系统配置列表
 * @param {Object} params - 查询参数
 * @param {string} [params.ConfigKey] - 配置键
 * @param {string} [params.ConfigType] - 配置类型
 * @param {string} [params.GroupName] - 分组名称
 * @param {number} [params.IsEnabled] - 是否启用
 * @param {number} [params.PageIndex] - 页码
 * @param {number} [params.PageSize] - 页大小
 * @param {string} [params.OrderField] - 排序字段
 * @param {boolean} [params.IsAsc] - 是否升序
 * @returns {Promise<{code: number, msg: string, success: boolean, data: {items: VideoSystemConfigResponseDto[], totalCount: number, pageIndex: number, pageSize: number, totalPages: number, hasPreviousPage: boolean, hasNextPage: boolean}}>}
 */
export async function querySystemConfigs (params = {}) {
  return await request({
    url: '/SystemConfig',
    method: 'GET',
    params
  })
}

/**
 * 更新系统配置
 * @param {VideoSystemConfigUpdateDto} data - 更新数据
 * @returns {Promise<{code: number, msg: string, success: boolean, data: boolean}>}
 */
export async function updateSystemConfig (data) {
  return await request({
    url: '/SystemConfig/update',
    method: 'POST',
    data
  })
}

/**
 * 删除系统配置
 * @param {number} configId - 配置ID
 * @returns {Promise<{code: number, msg: string, success: boolean, data: boolean}>}
 */
export async function deleteSystemConfig (configId) {
  return await request({
    url: `/SystemConfig/${configId}`,
    method: 'DELETE'
  })
}

/**
 * 获取系统配置详情
 * @param {number} configId - 配置ID
 * @returns {Promise<{code: number, msg: string, success: boolean, data: VideoSystemConfigResponseDto}>}
 */
export async function getSystemConfigDetail (configId) {
  return await request({
    url: `/SystemConfig/${configId}`,
    method: 'GET'
  })
}

/**
 * 根据配置键获取配置
 * @param {string} configKey - 配置键
 * @returns {Promise<{code: number, msg: string, success: boolean, data: VideoSystemConfigResponseDto}>}
 */
export async function getSystemConfigByKey (configKey) {
  return await request({
    url: `/SystemConfig/key/${configKey}`,
    method: 'GET'
  })
}

/**
 * 获取配置值
 * @param {string} configKey - 配置键
 * @returns {Promise<{code: number, msg: string, success: boolean, data: string}>}
 */
export async function getSystemConfigValue (configKey) {
  return await request({
    url: `/SystemConfig/value/${configKey}`,
    method: 'GET'
  })
}

/**
 * 设置配置值
 * @param {string} configKey - 配置键
 * @param {string} configValue - 配置值
 * @returns {Promise<{code: number, msg: string, success: boolean, data: boolean}>}
 */
export async function setSystemConfigValue (configKey, configValue) {
  return await request({
    url: `/SystemConfig/value/${configKey}`,
    method: 'POST',
    data: configValue
  })
}

/**
 * 获取所有系统配置
 * @returns {Promise<{code: number, msg: string, success: boolean, data: VideoSystemConfigResponseDto[]}>}
 */
export async function getAllSystemConfigs () {
  return await request({
    url: '/SystemConfig/all',
    method: 'GET'
  })
}

/**
 * 根据分组获取配置
 * @param {string} groupName - 分组名称
 * @returns {Promise<{code: number, msg: string, success: boolean, data: VideoSystemConfigResponseDto[]}>}
 */
export async function getSystemConfigsByGroup (groupName) {
  return await request({
    url: `/SystemConfig/group/${groupName}`,
    method: 'GET'
  })
}

/**
 * 根据配置类型获取配置
 * @param {string} configType - 配置类型
 * @returns {Promise<{code: number, msg: string, success: boolean, data: VideoSystemConfigResponseDto[]}>}
 */
export async function getSystemConfigsByType (configType) {
  return await request({
    url: `/SystemConfig/type/${configType}`,
    method: 'GET'
  })
}

/**
 * 批量更新配置
 * @param {VideoSystemConfigUpdateDto[]} configs - 配置列表
 * @returns {Promise<{code: number, msg: string, success: boolean, data: boolean}>}
 */
export async function batchUpdateSystemConfigs (configs) {
  return await request({
    url: '/SystemConfig/batch-update',
    method: 'POST',
    data: configs
  })
}

/**
 * 获取所有配置分组
 * @returns {Promise<{code: number, msg: string, success: boolean, data: string[]}>}
 */
export async function getSystemConfigGroups () {
  return await request({
    url: '/SystemConfig/groups',
    method: 'GET'
  })
}

/**
 * 获取微信相关配置
 * @returns {Promise<{code: number, msg: string, success: boolean, data: Object}>}
 */
export async function getWechatConfigs () {
  return await request({
    url: '/SystemConfig/wechat',
    method: 'GET'
  })
}

/**
 * 获取红包相关配置
 * @returns {Promise<{code: number, msg: string, success: boolean, data: Object}>}
 */
export async function getRewardConfigs () {
  return await request({
    url: '/SystemConfig/reward',
    method: 'GET'
  })
}

/**
 * 获取系统相关配置
 * @returns {Promise<{code: number, msg: string, success: boolean, data: Object}>}
 */
export async function getSystemConfigs () {
  return await request({
    url: '/SystemConfig/system',
    method: 'GET'
  })
}

/**
 * 启用/禁用配置
 * @param {number} configId - 配置ID
 * @param {number} isEnabled - 是否启用 (1:启用 0:禁用)
 * @returns {Promise<{code: number, msg: string, success: boolean, data: boolean}>}
 */
export async function toggleSystemConfigStatus (configId, isEnabled) {
  return await request({
    url: `/SystemConfig/${configId}/status`,
    method: 'POST',
    data: { isEnabled }
  })
}

// 默认导出所有系统配置相关API
export default {
  createSystemConfig,
  querySystemConfigs,
  updateSystemConfig,
  deleteSystemConfig,
  getSystemConfigDetail,
  getSystemConfigByKey,
  getSystemConfigValue,
  setSystemConfigValue,
  getAllSystemConfigs,
  getSystemConfigsByGroup,
  getSystemConfigsByType,
  batchUpdateSystemConfigs,
  getSystemConfigGroups,
  getWechatConfigs,
  getRewardConfigs,
  getSystemConfigs,
  toggleSystemConfigStatus
}
