<template>
  <view class="compression-progress-container" v-if="visible">
    <!-- 遮罩层 -->
    <view class="progress-mask" @tap="onMaskTap"></view>

    <!-- 进度卡片 -->
    <view class="progress-card">
      <view class="progress-header">
        <text class="progress-title">🎬 视频压缩中</text>
        <text class="close-btn" @tap="closeProgress" v-if="canClose">✕</text>
      </view>

      <!-- 进度条 -->
      <view class="progress-section">
        <view class="progress-info">
          <text class="progress-percent">{{ progressData.progress }}%</text>
          <text class="progress-status">{{ getStatusText() }}</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" :class="{ 'progress-active': progressData.status === 'processing' }"
            :style="{ width: progressData.progress + '%' }"></view>
          <view class="progress-text">{{ progressData.progress }}%</view>
        </view>
        <!-- 压缩速度和预计时间 -->
        <view class="progress-details" v-if="progressData.status === 'processing'">
          <text class="detail-text">{{ getProgressDetails() }}</text>
        </view>
      </view>

      <!-- 文件信息 -->
      <view class="file-info" v-if="progressData.originalSize">
        <view class="info-row">
          <text class="info-label">原始大小:</text>
          <text class="info-value">{{ formatFileSize(progressData.originalSize) }}</text>
        </view>
        <view class="info-row" v-if="progressData.compressedSize">
          <text class="info-label">压缩后:</text>
          <text class="info-value">{{ formatFileSize(progressData.compressedSize) }}</text>
        </view>
        <view class="info-row" v-if="progressData.compressionRatio">
          <text class="info-label">压缩率:</text>
          <text class="info-value compression-ratio">{{ progressData.compressionRatio }}%</text>
        </view>
      </view>

      <!-- 状态信息 -->
      <view class="status-section">
        <view class="status-item" v-if="progressData.status === 'waiting'">
          <text class="status-icon">⏳</text>
          <text class="status-text">等待压缩开始...</text>
        </view>
        <view class="status-item" v-else-if="progressData.status === 'processing'">
          <text class="status-icon">⚡</text>
          <text class="status-text">正在使用FFmpeg压缩视频，请稍候...</text>
        </view>
        <view class="status-item success" v-else-if="progressData.status === 'completed'">
          <text class="status-icon">✅</text>
          <text class="status-text">压缩完成！文件大小减少了{{ Math.round((1 - progressData.compressionRatio / 100) * 100)
          }}%</text>
        </view>
        <view class="status-item error" v-else-if="progressData.status === 'failed'">
          <text class="status-icon">❌</text>
          <text class="status-text">压缩失败: {{ progressData.errorMessage || '未知错误' }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="btn btn-secondary" @tap="closeProgress" v-if="canClose">
          {{ progressData.status === 'completed' ? '完成' : '后台运行' }}
        </button>
        <button class="btn btn-primary" @tap="retryCompression" v-if="progressData.status === 'failed'">
          重试
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { formatFileSize } from "@/utils/format.js";
import request from "@/utils/request.js";

export default {
  name: 'CompressionProgress',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    fileId: {
      type: String,
      required: true
    },
    autoClose: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      progressData: {
        progress: 0,
        status: 'waiting',
        originalSize: 0,
        compressedSize: 0,
        compressionRatio: 0,
        errorMessage: '',
        startTime: null
      },
      polling: null,
      canClose: false,
      lastProgress: 0,
      lastUpdateTime: null
    }
  },
  watch: {
    visible (newVal) {
      if (newVal) {
        this.startPolling();
      } else {
        this.stopPolling();
      }
    },
    fileId () {
      if (this.visible) {
        this.startPolling();
      }
    }
  },
  methods: {
    // 开始轮询压缩进度
    startPolling () {
      this.stopPolling();
      this.fetchProgress();
      this.polling = setInterval(() => {
        this.fetchProgress();
      }, 1000); // 每1秒查询一次，让进度更流畅
    },

    // 停止轮询
    stopPolling () {
      if (this.polling) {
        clearInterval(this.polling);
        this.polling = null;
      }
    },

    // 获取压缩进度
    async fetchProgress () {
      try {
        const response = await request.get(`/Video/compression-progress/${this.fileId}`);

        if (response.success) {
          const newData = response.data;

          // 更新进度计算相关数据
          if (this.progressData.progress !== newData.progress) {
            this.lastProgress = this.progressData.progress;
            this.lastUpdateTime = Date.now();
          }

          this.progressData = newData;

          // 如果压缩完成或失败，允许关闭
          if (this.progressData.status === 'completed' || this.progressData.status === 'failed') {
            this.canClose = true;
            this.stopPolling();

            // 自动关闭
            if (this.autoClose && this.progressData.status === 'completed') {
              setTimeout(() => {
                this.closeProgress();
              }, 3000);
            }
          }
        }
      } catch (error) {
        console.error('获取压缩进度失败:', error);
      }
    },

    // 获取状态文本
    getStatusText () {
      switch (this.progressData.status) {
        case 'waiting': return '准备中';
        case 'processing': return '压缩中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        default: return '未知状态';
      }
    },

    // 格式化文件大小
    formatFileSize,

    // 获取进度详情
    getProgressDetails () {
      if (this.progressData.status !== 'processing') return '';

      const currentTime = Date.now();
      const startTime = new Date(this.progressData.startTime).getTime();
      const elapsedSeconds = Math.floor((currentTime - startTime) / 1000);

      if (this.progressData.progress > 0 && elapsedSeconds > 0) {
        // 计算预计剩余时间
        const progressRate = this.progressData.progress / elapsedSeconds; // 每秒进度
        const remainingProgress = 100 - this.progressData.progress;
        const estimatedRemainingSeconds = Math.floor(remainingProgress / progressRate);

        // 格式化时间
        const formatTime = (seconds) => {
          if (seconds < 60) return `${seconds}秒`;
          const minutes = Math.floor(seconds / 60);
          const remainingSeconds = seconds % 60;
          return `${minutes}分${remainingSeconds}秒`;
        };

        return `已用时: ${formatTime(elapsedSeconds)} | 预计剩余: ${formatTime(estimatedRemainingSeconds)}`;
      }

      return '正在计算预计时间...';
    },

    // 关闭进度窗口
    closeProgress () {
      this.$emit('close');
    },

    // 点击遮罩
    onMaskTap () {
      if (this.canClose) {
        this.closeProgress();
      }
    },

    // 重试压缩
    retryCompression () {
      this.$emit('retry', this.fileId);
      this.progressData.status = 'waiting';
      this.progressData.progress = 0;
      this.canClose = false;
      this.startPolling();
    }
  },
  beforeDestroy () {
    this.stopPolling();
  }
}
</script>

<style lang="scss" scoped>
.compression-progress-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.progress-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
}

.progress-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 24rpx 64rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid rgba(24, 107, 255, 0.2);
  width: 85%;
  max-width: 600rpx;
  z-index: 10000;
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.progress-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #186BFF;
}

.close-btn {
  font-size: 32rpx;
  color: #64748b;
  padding: 8rpx;
  border-radius: 50%;
  background: #f1f5f9;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-section {
  margin-bottom: 32rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-percent {
  font-size: 48rpx;
  font-weight: bold;
  color: #186BFF;
}

.progress-status {
  font-size: 24rpx;
  color: #64748b;
}

.progress-bar {
  height: 32rpx;
  background: #e2e8f0;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #186BFF 0%, #4f9eff 100%);
  border-radius: 16rpx;
  transition: width 0.5s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill.progress-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.progress-details {
  text-align: center;
  margin-top: 8rpx;
}

.detail-text {
  font-size: 22rpx;
  color: #64748b;
  line-height: 1.4;
}

.file-info {
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #64748b;
}

.info-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #1e293b;
}

.compression-ratio {
  color: #059669;
}

.status-section {
  margin-bottom: 32rpx;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-radius: 12rpx;
  background: #f1f5f9;
}

.status-item.success {
  background: #dcfce7;
}

.status-item.error {
  background: #fef2f2;
}

.status-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.status-text {
  font-size: 26rpx;
  color: #374151;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.btn {
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #186BFF;
  color: white;
}

.btn-secondary {
  background: #f1f5f9;
  color: #64748b;
}
</style>
