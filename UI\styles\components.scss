/* ===== 通用组件样式库 ===== */
@import './variables.scss';

/* === 卡片组件 === */
.card {
  background: $bg-primary;
  border-radius: $border-radius-md;
  box-shadow: $shadow-base;
  border: 1rpx solid $border-secondary;
  overflow: hidden;
  margin-bottom: $spacing-base;
}

.card-header {
  padding: $card-padding;
  border-bottom: 1rpx solid $border-secondary;
  background-color: $bg-primary;
}

.card-body {
  padding: $card-padding;
}

.card-footer {
  padding: $card-padding;
  border-top: 1rpx solid $border-secondary;
  background-color: $bg-secondary;
}

.card-title {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0 0 $spacing-base 0;
}

.card-subtitle {
  font-size: $font-size-base;
  color: $text-secondary;
  margin: 0 0 $spacing-sm 0;
}

.card-text {
  font-size: $font-size-base;
  color: $text-primary;
  line-height: $line-height-relaxed;
  margin: 0;
}

/* === 用户信息卡片 === */
.user-card {
  display: flex;
  align-items: center;
  // padding: $spacing-lg;
  background: $bg-primary;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  margin-bottom: $spacing-base;
  transition: all $transition-base ease;
}

.user-card:active {
  transform: scale(0.98);
  box-shadow: $shadow-base;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: $border-radius-round;
  margin-right: $spacing-lg;
  background-color: $bg-tertiary;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-lg;
  color: $text-secondary;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-primary;
  margin-bottom: $spacing-xs;
}

.user-role {
  font-size: $font-size-sm;
  color: $text-secondary;
}

.user-status {
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-xs;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
}

.user-status.active {
  background-color: rgba(82, 196, 26, 0.1);
  color: $success-color;
}

.user-status.inactive {
  background-color: rgba(140, 140, 140, 0.1);
  color: $text-tertiary;
}

/* === 标签组件 === */
.tag {
  display: inline-flex;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-xs;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  line-height: 1;
}

.tag-primary {
  background-color: rgba(24, 107, 255, 0.1);
  color: $primary-color;
}

.tag-success {
  background-color: rgba(82, 196, 26, 0.1);
  color: $success-color;
}

.tag-warning {
  background-color: rgba(250, 173, 20, 0.1);
  color: $warning-color;
}

.tag-danger {
  background-color: rgba(255, 77, 79, 0.1);
  color: $error-color;
}

.tag-default {
  background-color: $bg-tertiary;
  color: $text-secondary;
}

/* === 徽章组件 === */
.badge {
  position: relative;
  display: inline-block;
}

.badge-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 12rpx;
  height: 12rpx;
  background-color: $error-color;
  border-radius: $border-radius-round;
  border: 2rpx solid $bg-primary;
}

.badge-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: $error-color;
  color: $text-white;
  border-radius: $border-radius-round;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 $spacing-xs;
  border: 2rpx solid $bg-primary;
}

/* === 分割线组件 === */
.divider {
  height: 1rpx;
  background-color: $border-secondary;
  margin: $spacing-lg 0;
}

.divider-text {
  position: relative;
  text-align: center;
  margin: $spacing-lg 0;
}

.divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: $border-secondary;
}

.divider-text span {
  background-color: $bg-primary;
  padding: 0 $spacing-lg;
  color: $text-secondary;
  font-size: $font-size-sm;
}

/* === 空状态组件 === */
.empty-state {
  text-align: center;
  padding: $spacing-xxxl $spacing-lg;
  color: $text-tertiary;
}

.empty-icon {
  font-size: 120rpx;
  color: $text-disabled;
  margin-bottom: $spacing-lg;
}

.empty-text {
  font-size: $font-size-base;
  color: $text-secondary;
  margin-bottom: $spacing-lg;
}

.empty-action {
  margin-top: $spacing-lg;
}

/* === 加载状态组件 === */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxxl;
  color: $text-tertiary;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid $border-light;
  border-top: 4rpx solid $primary-color;
  border-radius: $border-radius-round;
  animation: spin 1s linear infinite;
  margin-bottom: $spacing-lg;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: $font-size-base;
  color: $text-secondary;
}

/* === 操作栏组件 === */
.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-lg $page-padding;
  background-color: $bg-primary;
  border-bottom: 1rpx solid $border-secondary;
}

.action-bar-left {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
}

.action-bar-right {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.action-bar-title {
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $text-primary;
}

/* === 筛选器组件 === */
.filter-tabs {
  display: flex;
  background-color: $bg-primary;
  border-bottom: 1rpx solid $border-secondary;
  padding: 0 $page-padding;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: $spacing-lg 0;
  font-size: $font-size-base;
  color: $text-secondary;
  border-bottom: 4rpx solid transparent;
  transition: all $transition-base ease;
}

.filter-tab.active {
  color: $primary-color;
  border-bottom-color: $primary-color;
  font-weight: $font-weight-medium;
}

/* === 统计卡片组件 === */
.stat-card {
  background: $bg-primary;
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  text-align: center;
  box-shadow: $shadow-sm;
}

.stat-number {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: $spacing-xs;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-secondary;
}

/* === 操作按钮组件 === */
.action-buttons {
  display: flex;
  padding: $spacing-lg $page-padding;
  background-color: $bg-primary;
  gap: $spacing-lg;
  border-bottom: 1rpx solid $border-secondary;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  color: $text-white;
  box-shadow: $shadow-md;
  transition: all $transition-base ease;
}

.action-btn:active {
  transform: translateY(2rpx);
  box-shadow: $shadow-base;
}

.action-btn .btn-icon {
  margin-right: $spacing-sm;
  font-size: $font-size-lg;
}

.copy-btn {
  background: linear-gradient(135deg, $primary-color, #0056CC);
}

.data-btn {
  background: linear-gradient(135deg, $success-color, #28A745);
}

/* === 状态标签页组件 === */
.status-tabs {
  display: flex;
  background-color: $bg-primary;
  padding: 0;
  border-bottom: 1rpx solid $border-secondary;
  box-shadow: $shadow-sm;
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: $font-size-base;
  color: $text-secondary;
  padding: $spacing-lg 0;
  position: relative;
  transition: all $transition-base ease;
}

.tab-item.active {
  color: $primary-color;
  font-weight: $font-weight-semibold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: $primary-color;
  border-radius: $border-radius-xs;
  transition: all $transition-base ease;
}

.tab-item .badge {
  display: inline-block;
  font-size: $font-size-xs;
  background-color: $error-color;
  color: $text-white;
  border-radius: $border-radius-round;
  padding: 2rpx $spacing-sm;
  margin-left: $spacing-xs;
  min-width: 32rpx;
  text-align: center;
  box-sizing: border-box;
}

/* === 进度条组件 === */
.progress-container {
  margin-top: $spacing-sm;
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 10rpx;
  background-color: $bg-tertiary;
  border-radius: $border-radius-xs;
  overflow: hidden;
  margin-right: $spacing-sm;
}

.progress-fill {
  height: 100%;
  background-color: $primary-color;
  border-radius: $border-radius-xs;
}

.progress-text {
  font-size: $font-size-xs;
  color: $text-secondary;
  min-width: 50rpx;
  text-align: right;
}

/* === 完播状态组件 === */
.completion-status {
  font-size: $font-size-xs;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-xs;
  margin-bottom: $spacing-xs;
  text-align: center;
}

.completion-status.completed {
  background-color: $success-color;
  color: $text-white;
}

.completion-status.incomplete {
  background-color: $warning-color;
  color: $text-white;
}

/* === 固定底部按钮组件 === */
.fixed-bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: $bg-primary;
  padding: $spacing-lg $page-padding;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.bottom-button {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: $font-size-base;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 $spacing-sm;
  border: none;
}

.delete-button {
  background-color: $error-color;
  color: $text-white;
}

/* === 视频播放器组件 === */
.video-player {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 0;
}

.video-element {
  width: 100%;
  height: 420rpx;
  display: block;
}

.video-info {
  padding: $spacing-lg $page-padding;
  border-bottom: 1rpx solid $border-secondary;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-base;
}

.video-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  flex: 1;
  margin-right: $spacing-lg;
  line-height: $line-height-relaxed;
}

/* === 用户列表组件 === */
.users-list {
  background-color: $bg-secondary;
}

.user-item {
  display: flex;
  align-items: center;
  padding: $spacing-lg $page-padding;
  background-color: $bg-primary;
  margin-bottom: 2rpx;
  transition: background-color $transition-base ease;
}

.user-item:active {
  background-color: $bg-secondary;
}

.user-content {
  flex: 1;
}

.user-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-sm;
}

.user-name {
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $text-primary;
}

.progress-info {
  font-size: $font-size-sm;
  color: $text-secondary;
}

.completed-text {
  color: $success-color;
}

.incomplete-text {
  color: $warning-color;
}

.watch-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-sm;
}

.watch-time {
  font-size: $font-size-sm;
  color: $text-tertiary;
}

.user-reward {
  font-size: $font-size-sm;
  color: $warning-color;
  font-weight: $font-weight-medium;
}

.user-right {
  margin-left: $spacing-lg;
}

.arrow-right {
  font-size: 40rpx;
  color: $text-disabled;
}

/* === 状态徽章组件 === */
.status-badge {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  border: 2rpx solid $bg-primary;
  display: inline-block;
}

.status-badge-normal {
  background: $success-color;
  color: $text-white;
}

.status-badge-disabled {
  background: $error-color;
  color: $text-white;
}

.status-badge-dismissed {
  background: $secondary-color;
  color: $text-white;
}

.status-tag {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  box-shadow: $shadow-base;
}

.status-tag-dismissed {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  color: $text-white;
}

/* === 底部操作弹出层组件 === */
.action-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $bg-mask;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: $z-index-modal;
}

.action-sheet {
  width: 100%;
  background-color: $bg-primary;
  border-radius: $border-radius-lg $border-radius-lg 0 0;
  overflow: hidden;
  box-shadow: $shadow-lg;
  animation: slideUp $transition-base $ease-out;
}

.action-sheet-content {
  padding: $spacing-lg 0;
}

.action-sheet-item {
  padding: $spacing-xl $spacing-xxl;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid $border-secondary;
  cursor: pointer;
  transition: background-color $transition-fast ease;
}

.action-sheet-item:last-child {
  border-bottom: none;
}

.action-sheet-item:active {
  background-color: $bg-secondary;
}

.action-sheet-text {
  font-size: $font-size-md;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

/* === 密码提示组件 === */
.password-tips {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1rpx solid #bae6fd;
  border-radius: $border-radius-base;
  padding: $spacing-lg;
  margin-bottom: $spacing-xl;
}

.tip-title {
  display: block;
  font-size: $font-size-base;
  color: #0369a1;
  font-weight: 600;
  margin-bottom: $spacing-base;
}

.tip-item {
  display: block;
  font-size: $font-size-sm;
  color: #0284c7;
  line-height: 1.8;
  margin-bottom: $spacing-xs;
  padding-left: $spacing-xs;
}

/* === 警告提示组件 === */
.warning-notice {
  display: flex;
  align-items: center;
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: $border-radius-base;
  padding: $spacing-md;
  margin-bottom: $spacing-lg;
}

.warning-icon {
  font-size: $font-size-lg;
  margin-right: $spacing-sm;
}

.warning-text {
  font-size: $font-size-sm;
  color: #d46b08;
  line-height: 1.4;
}

/* === 媒体列表组件 === */
.media-list-container {
  padding: $spacing-base;
  background-color: $bg-secondary;
}

.media-item {
  display: flex;
  align-items: center;
  padding: $spacing-lg;
  margin-bottom: $spacing-base;
  background-color: $bg-primary;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  transition: all $transition-fast ease;
  cursor: pointer;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    transform: scale(0.98);
    box-shadow: $shadow-base;
  }
}

.media-thumbnail {
  position: relative;
  flex-shrink: 0;
  border-radius: $border-radius-base;
  overflow: hidden;
  margin-right: $spacing-lg;
  background-color: $bg-tertiary;
}

.media-thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-content {
  flex: 1;
  min-width: 0; // 防止文本溢出
}

.media-title {
  font-size: $font-size-md;
  color: $text-primary;
  font-weight: $font-weight-medium;
  line-height: $line-height-normal;
  margin-bottom: $spacing-sm;

  // 限制显示行数，超出显示省略号
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-meta {
  display: flex;
  align-items: center;
  font-size: $font-size-base;
  color: $text-tertiary;
  line-height: 1.2;
}

.media-meta-item {
  color: $text-tertiary;
}

.media-meta-separator {
  margin: 0 $spacing-sm;
  color: $text-disabled;
}