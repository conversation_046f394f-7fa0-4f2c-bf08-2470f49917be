/**
 * 视频用户相关API
 * 处理微信用户登录、用户管理、转移等功能
 */

import request from '../utils/request.js'

/**
 * 微信登录请求参数
 * @typedef {Object} VideoWechatLoginDto
 * @property {string} code - 微信授权码
 * @property {string} state - 状态参数
 * @property {string} employeeId - 员工ID
 * @property {number} batchId - 批次ID
 */

/**
 * 登录响应数据
 * @typedef {Object} VideoLoginResponseDto
 * @property {string} token - 访问令牌
 * @property {Object} userInfo - 用户信息
 */

/**
 * 用户响应数据
 * @typedef {Object} VideoUserResponseDto
 * @property {number} id - 用户ID
 * @property {string} openId - 微信OpenID
 * @property {string} unionId - 微信UnionID
 * @property {string} nickname - 用户昵称
 * @property {string} avatar - 用户头像
 * @property {string} employeeId - 员工ID
 * @property {string} employeeName - 员工姓名
 * @property {string} lastLogin - 最后登录时间
 * @property {string} createTime - 创建时间
 */

/**
 * 用户查询参数
 * @typedef {Object} VideoUserQueryDto
 * @property {string} [Nickname] - 用户昵称
 * @property {string} [EmployeeId] - 员工ID
 * @property {string} [StartTime] - 开始时间
 * @property {string} [EndTime] - 结束时间
 * @property {number} [PageIndex] - 页码
 * @property {number} [PageSize] - 每页大小
 * @property {string} [OrderField] - 排序字段
 * @property {boolean} [IsAsc] - 是否升序
 */

/**
 * 用户转移请求参数
 * @typedef {Object} VideoUserTransferDto
 * @property {Array<number>} userIds - 用户ID列表
 * @property {string} toEmployeeId - 目标员工ID
 * @property {string} reason - 转移原因
 */

/**
 * 访问推广链接请求参数
 * @typedef {Object} VideoAccessPromotionDto
 * @property {number} batchId - 批次ID
 * @property {number} employeeId - 员工ID
 * @property {number} adminId - 管理员ID
 * @property {string} code - 访问码
 */

/**
 * 访问推广链接响应数据
 * @typedef {Object} VideoAccessPromotionResponseDto
 * @property {boolean} needAudit - 是否需要审核
 * @property {number} auditStatus - 审核状态
 * @property {Object} batch - 批次信息
 * @property {Object} user - 用户信息
 */

/**
 * 分页结果数据
 * @typedef {Object} PagedResultOfVideoUserResponseDto
 * @property {Array<VideoUserResponseDto>} items - 数据列表
 * @property {number} totalCount - 总记录数
 * @property {number} pageIndex - 当前页码
 * @property {number} pageSize - 每页大小
 * @property {number} totalPages - 总页数
 * @property {boolean} hasPreviousPage - 是否有上一页
 * @property {boolean} hasNextPage - 是否有下一页
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 简单用户注册
 * @param {Object} data - 注册数据
 * @param {string} data.nickname - 用户昵称
 * @param {string} [data.avatar] - 用户头像
 * @param {string} [data.mobile] - 手机号
 * @returns {Promise<ApiResult<VideoLoginResponseDto>>} 注册结果
 */
export function simpleRegister (data) {
  return request.post('/User/register', data)
}

/**
 * 简单用户登录
 * @param {Object} data - 登录数据
 * @param {string} data.nickname - 用户昵称
 * @param {string} [data.mobile] - 手机号
 * @returns {Promise<ApiResult<VideoLoginResponseDto>>} 登录结果
 */
export function simpleLogin (data) {
  return request.post('/User/simple-login', data)
}

/**
 * 微信登录/注册（自动注册新用户）
 * @param {VideoWechatLoginDto} data - 登录数据
 * @returns {Promise<ApiResult<VideoLoginResponseDto>>} 登录结果
 */
export function wechatLogin (data) {
  return request.post('/User/login', data)
}

/**
 * 获取当前用户信息
 * @returns {Promise<ApiResult<VideoUserResponseDto>>} 当前用户信息
 */
export function getUserProfile () {
  return request.get('/User/profile')
}

/**
 * 获取用户详情
 * @param {number} userId - 用户ID
 * @returns {Promise<ApiResult<VideoUserResponseDto>>} 用户详情
 */
export function getVideoUserDetail (userId) {
  return request.get(`/User/${userId}`)
}

/**
 * 分页查询用户列表
 * @param {VideoUserQueryDto} params - 查询参数
 * @returns {Promise<ApiResult<PagedResultOfVideoUserResponseDto>>} 分页查询结果
 */
export function queryVideoUsers (params) {
  return request.get('/User', params)
}

/**
 * 转移用户到新员工
 * @param {VideoUserTransferDto} data - 转移数据
 * @returns {Promise<ApiResult<boolean>>} 转移结果
 */
export function transferUsers (data) {
  return request.post('/User/transfer', data)
}

/**
 * 访问推广链接
 * @param {VideoAccessPromotionDto} data - 访问数据
 * @returns {Promise<ApiResult<VideoAccessPromotionResponseDto>>} 访问结果
 */
export function accessPromotion (data) {
  return request.post('/User/access-promotion', data)
}

/**
 * 根据员工ID查询用户
 * @param {string} employeeId - 员工ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.PageIndex=1] - 页码
 * @param {number} [options.PageSize=20] - 每页大小
 * @returns {Promise<ApiResult<PagedResultOfVideoUserResponseDto>>} 查询结果
 */
export function queryUsersByEmployee (employeeId, options = {}) {
  return queryVideoUsers({
    EmployeeId: employeeId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据昵称搜索用户
 * @param {string} nickname - 用户昵称
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoUserResponseDto>>} 查询结果
 */
export function searchUsersByNickname (nickname, options = {}) {
  return queryVideoUsers({
    Nickname: nickname,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据时间范围查询用户
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoUserResponseDto>>} 查询结果
 */
export function queryUsersByTimeRange (startTime, endTime, options = {}) {
  return queryVideoUsers({
    StartTime: startTime,
    EndTime: endTime,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 批量转移用户
 * @param {Array<number>} userIds - 用户ID列表
 * @param {string} toEmployeeId - 目标员工ID
 * @param {string} [reason] - 转移原因
 * @returns {Promise<ApiResult<boolean>>} 转移结果
 */
export function batchTransferUsers (userIds, toEmployeeId, reason = '') {
  return transferUsers({
    userIds,
    toEmployeeId,
    reason
  })
}

/**
 * 获取排序字段选项
 * @returns {Array<{value: string, label: string}>} 排序字段选项
 */
export function getUserSortFieldOptions () {
  return [
    { value: 'createTime', label: '创建时间' },
    { value: 'lastLogin', label: '最后登录时间' },
    { value: 'nickname', label: '用户昵称' },
    { value: 'employeeName', label: '员工姓名' }
  ]
}

/**
 * 获取审核状态选项
 * @returns {Array<{value: number, label: string}>} 审核状态选项
 */
export function getAuditStatusOptions () {
  return [
    { value: 0, label: '待审核' },
    { value: 1, label: '审核通过' },
    { value: 2, label: '审核拒绝' }
  ]
}

/**
 * 验证微信登录数据
 * @param {VideoWechatLoginDto} data - 登录数据
 * @returns {Object} 验证结果 {valid: boolean, errors: Array<string>}
 */
export function validateWechatLoginData (data) {
  const errors = []

  if (!data.code || data.code.trim() === '') {
    errors.push('微信授权码不能为空')
  }

  if (!data.employeeId || data.employeeId.trim() === '') {
    errors.push('员工ID不能为空')
  }

  if (!data.batchId || typeof data.batchId !== 'number') {
    errors.push('批次ID不能为空且必须为数字')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 验证用户转移数据
 * @param {VideoUserTransferDto} data - 转移数据
 * @returns {Object} 验证结果 {valid: boolean, errors: Array<string>}
 */
export function validateUserTransferData (data) {
  const errors = []

  if (!data.userIds || !Array.isArray(data.userIds) || data.userIds.length === 0) {
    errors.push('用户ID列表不能为空')
  }

  if (!data.toEmployeeId || data.toEmployeeId.trim() === '') {
    errors.push('目标员工ID不能为空')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 格式化用户数据
 * @param {VideoUserResponseDto} user - 用户数据
 * @returns {Object} 格式化后的用户数据
 */
export function formatUserData (user) {
  if (!user) return {}

  return {
    ...user,
    // 格式化时间
    lastLogin: user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '从未登录',
    createTime: user.createTime ? new Date(user.createTime).toLocaleString() : '',

    // 处理头像
    avatar: user.avatar,

    // 处理昵称
    nickname: user.nickname || '未设置昵称',

    // 处理员工信息
    employeeName: user.employeeName || '未绑定员工'
  }
}

// 默认导出所有视频用户相关API
export default {
  simpleRegister,
  simpleLogin,
  wechatLogin,
  getUserProfile,
  getVideoUserDetail,
  queryVideoUsers,
  transferUsers,
  accessPromotion,
  queryUsersByEmployee,
  searchUsersByNickname,
  queryUsersByTimeRange,
  batchTransferUsers,
  getUserSortFieldOptions,
  getAuditStatusOptions,
  validateWechatLoginData,
  validateUserTransferData,
  formatUserData
}
