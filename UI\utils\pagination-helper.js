/**
 * 分页加载工具
 * 提供列表页面的分页加载功能
 */
import { showError } from './toast-manager.js';

/**
 * 分页管理器类
 */
export class PaginationManager {
  constructor(options = {}) {
    this.pageSize = options.pageSize || 20;
    this.currentPage = 1;
    this.totalPages = 0;
    this.totalCount = 0;
    this.hasMore = true;
    this.loading = false;
    this.data = [];
    this.apiCall = options.apiCall;
    this.onDataUpdate = options.onDataUpdate;
    this.onError = options.onError;
    this.cacheKey = options.cacheKey;
  }

  /**
   * 重置分页状态
   */
  reset () {
    this.currentPage = 1;
    this.totalPages = 0;
    this.totalCount = 0;
    this.hasMore = true;
    this.loading = false;
    this.data = [];
  }

  /**
   * 加载第一页数据
   * @param {Object} params - 查询参数
   * @returns {Promise} 加载结果
   */
  async loadFirstPage (params = {}) {
    this.reset();
    return await this.loadNextPage(params);
  }

  /**
   * 加载下一页数据
   * @param {Object} params - 查询参数
   * @returns {Promise} 加载结果
   */
  async loadNextPage (params = {}) {
    if (this.loading || !this.hasMore) {
      return { success: false, msg: '正在加载或没有更多数据' };
    }

    this.loading = true;

    try {
      const requestParams = {
        ...params,
        PageIndex: this.currentPage,
        PageSize: this.pageSize
      };

      const response = await this.apiCall(requestParams);

      if (response.success && response.data) {
        const { items = [], totalCount = 0, totalPages = 0 } = response.data;

        // 更新分页信息
        this.totalCount = totalCount;
        this.totalPages = totalPages || Math.ceil(totalCount / this.pageSize);
        this.hasMore = this.currentPage < this.totalPages;

        // 合并数据
        if (this.currentPage === 1) {
          this.data = items;
        } else {
          this.data = [...this.data, ...items];
        }

        this.currentPage++;

        // 触发数据更新回调
        if (this.onDataUpdate) {
          this.onDataUpdate(this.data, {
            currentPage: this.currentPage - 1,
            totalPages: this.totalPages,
            totalCount: this.totalCount,
            hasMore: this.hasMore
          });
        }

        return {
          success: true,
          data: this.data,
          pagination: {
            currentPage: this.currentPage - 1,
            totalPages: this.totalPages,
            totalCount: this.totalCount,
            hasMore: this.hasMore
          }
        };
      } else {
        throw new Error(response.msg || '加载数据失败');
      }
    } catch (error) {
      console.error('分页加载失败:', error);

      if (this.onError) {
        this.onError(error);
      }

      return {
        success: false,
        msg: error.message || '加载数据失败'
      };
    } finally {
      this.loading = false;
    }
  }

  /**
   * 刷新当前页面数据
   * @param {Object} params - 查询参数
   * @returns {Promise} 刷新结果
   */
  async refresh (params = {}) {
    return await this.loadFirstPage(params);
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getState () {
    return {
      data: this.data,
      currentPage: this.currentPage,
      totalPages: this.totalPages,
      totalCount: this.totalCount,
      hasMore: this.hasMore,
      loading: this.loading,
      pageSize: this.pageSize
    };
  }
}

/**
 * 创建分页管理器
 * @param {Object} options - 配置选项
 * @returns {PaginationManager} 分页管理器实例
 */
export function createPaginationManager (options) {
  return new PaginationManager(options);
}

/**
 * 下拉刷新处理器
 * @param {PaginationManager} paginationManager - 分页管理器
 * @param {Object} params - 查询参数
 * @returns {Function} 下拉刷新处理函数
 */
export function createPullRefreshHandler (paginationManager, params = {}) {
  return async function () {
    try {
      await paginationManager.refresh(params);
      uni.stopPullDownRefresh();
    } catch (error) {
      console.error('下拉刷新失败:', error);
      uni.stopPullDownRefresh();
      uni.showToast({
        title: '刷新失败',
        icon: 'none'
      });
    }
  };
}

/**
 * 上拉加载更多处理器
 * @param {PaginationManager} paginationManager - 分页管理器
 * @param {Object} params - 查询参数
 * @returns {Function} 上拉加载处理函数
 */
export function createLoadMoreHandler (paginationManager, params = {}) {
  return async function () {
    try {
      const result = await paginationManager.loadNextPage(params);

      if (!result.success) {
        showError(result.msg || '加载失败');
      }
    } catch (error) {
      console.error('加载更多失败:', error);
      showError('加载失败');
    }
  };
}

/**
 * 搜索防抖处理器
 * @param {Function} searchFunction - 搜索函数
 * @param {number} delay - 防抖延迟时间（毫秒）
 * @returns {Function} 防抖搜索函数
 */
export function createSearchDebouncer (searchFunction, delay = 500) {
  let timer = null;

  return function (...args) {
    if (timer) {
      clearTimeout(timer);
    }

    timer = setTimeout(() => {
      searchFunction.apply(this, args);
    }, delay);
  };
}

/**
 * 列表页面混入对象
 * 提供通用的分页功能
 */
export const PaginationMixin = {
  data () {
    return {
      paginationManager: null,
      listData: [],
      listLoading: false,
      listError: null,
      searchKeyword: '',
      searchTimer: null
    };
  },

  onLoad () {
    this.initPagination();
    this.loadFirstPage();
  },

  onPullDownRefresh () {
    this.handlePullRefresh();
  },

  onReachBottom () {
    this.handleLoadMore();
  },

  methods: {
    /**
     * 初始化分页管理器
     * 子类需要重写此方法
     */
    initPagination () {
      throw new Error('子类必须实现 initPagination 方法');
    },

    /**
     * 加载第一页数据
     */
    async loadFirstPage () {
      if (!this.paginationManager) {
        console.error('分页管理器未初始化');
        return;
      }

      this.listLoading = true;
      this.listError = null;

      try {
        const params = this.getSearchParams();
        await this.paginationManager.loadFirstPage(params);
      } catch (error) {
        this.listError = error.message;
      } finally {
        this.listLoading = false;
      }
    },

    /**
     * 处理下拉刷新
     */
    async handlePullRefresh () {
      const handler = createPullRefreshHandler(
        this.paginationManager,
        this.getSearchParams()
      );
      await handler();
    },

    /**
     * 处理上拉加载更多
     */
    async handleLoadMore () {
      const handler = createLoadMoreHandler(
        this.paginationManager,
        this.getSearchParams()
      );
      await handler();
    },

    /**
     * 处理搜索输入
     */
    onSearchInput () {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      this.searchTimer = setTimeout(() => {
        this.loadFirstPage();
      }, 500);
    },

    /**
     * 获取搜索参数
     * 子类可以重写此方法
     */
    getSearchParams () {
      const params = {};

      if (this.searchKeyword) {
        params.keyword = this.searchKeyword;
      }

      return params;
    },

    /**
     * 数据更新回调
     */
    onDataUpdate (data, pagination) {
      this.listData = data;
    },

    /**
     * 错误处理回调
     */
    onListError (error) {
      this.listError = error.message;
      showError('加载失败');
    }
  }
};
