<template>
  <view class="search-container">
    <!-- uview-plus 搜索框 -->
    <u-search v-if="type === 'uview'" v-model="searchValue" :placeholder="placeholder" @search="handleSearch"
      @custom="handleSearch" shape="round" :showAction="false" bgColor="#f8f9fa" height="35" />

    <!-- 原生搜索框 -->
    <view v-else class="search-box">
      <view class="search-input">
        <text class="search-icon">🔍</text>
        <input type="text" v-model="searchValue" :placeholder="placeholder" confirm-type="search"
          @confirm="handleSearch" @input="handleInput" />
        <text class="clear-icon" v-if="searchValue" @tap="clearSearch">×</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SearchBox',
  props: {
    // 搜索框类型：'uview' 或 'native'
    type: {
      type: String,
      default: 'uview',
      validator: (value) => ['uview', 'native'].includes(value)
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '请输入搜索关键词'
    },
    // 搜索值
    modelValue: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'search', 'clear'],
  computed: {
    searchValue: {
      get () {
        return this.modelValue;
      },
      set (value) {
        this.$emit('update:modelValue', value);
      }
    }
  },
  methods: {
    handleSearch () {
      this.$emit('search', this.searchValue);
    },
    handleInput () {
      // 实时搜索，可以根据需要启用防抖
      this.$emit('search', this.searchValue);
    },
    clearSearch () {
      this.searchValue = '';
      this.$emit('clear');
      this.$emit('search', '');
    }
  }
}
</script>

<style lang="scss" scoped>
.search-container {
  margin-top: 16rpx;
}

/* 原生搜索框样式 */
.search-box {
  padding: 16rpx 24rpx;
  margin: 16rpx 0;
}

.search-input {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 50rpx;
  padding: 24rpx 32rpx;
  position: relative;
  height: 70rpx;
  box-sizing: border-box;
}

.search-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
  color: #999;
}

.search-input input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  height: 100%;
  line-height: 1.4;
}

.search-input input::placeholder {
  color: #999;
}

.clear-icon {
  font-size: 40rpx;
  color: #999;
  margin-left: 16rpx;
  padding: 8rpx;
  line-height: 1;
}
</style>
