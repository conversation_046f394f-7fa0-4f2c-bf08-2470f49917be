/**
 * 表单处理通用混入
 * 提供统一的表单验证、提交、重置等功能
 */
import { apiCallWrapper, ErrorHandlerPresets } from "../utils/api-error-handler.js";
import { showSuccess, showError } from '../utils/toast-manager.js';

export default {
  data () {
    return {
      // 表单状态
      formLoading: false,
      formSubmitting: false,
      formValid: false,
      formErrors: {},

      // 表单数据
      formData: {},
      originalFormData: {},

      // 验证规则
      formRules: {},

      // 模态框状态
      showModal: false,
      modalTitle: '',
      modalMode: 'add', // 'add' | 'edit' | 'view'
    };
  },

  computed: {
    /**
     * 表单是否已修改
     */
    isFormDirty () {
      return JSON.stringify(this.formData) !== JSON.stringify(this.originalFormData);
    },

    /**
     * 是否可以提交表单
     */
    canSubmitForm () {
      return this.formValid && !this.formSubmitting && this.isFormDirty;
    },

    /**
     * 表单模式文本
     */
    formModeText () {
      const modeMap = {
        add: '添加',
        edit: '编辑',
        view: '查看'
      };
      return modeMap[this.modalMode] || '操作';
    }
  },

  methods: {
    /**
     * 初始化表单数据
     * 子类需要重写此方法
     */
    initFormData () {
      return {};
    },

    /**
     * 获取表单验证规则
     * 子类需要重写此方法
     */
    getFormRules () {
      return {};
    },

    /**
     * 获取提交API调用函数
     * 子类需要重写此方法
     */
    getSubmitApiCall () {
      throw new Error('子类必须实现 getSubmitApiCall 方法');
    },

    /**
     * 格式化提交数据
     * 子类可以重写此方法来格式化提交的数据
     */
    formatSubmitData (data) {
      return data;
    },

    /**
     * 重置表单
     */
    resetForm () {
      this.formData = this.initFormData();
      this.originalFormData = { ...this.formData };
      this.formErrors = {};
      this.formValid = false;

      // 重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields();
        }
      });
    },

    /**
     * 设置表单数据
     */
    setFormData (data) {
      this.formData = { ...this.initFormData(), ...data };
      this.originalFormData = { ...this.formData };
      this.formErrors = {};

      // 触发表单验证
      this.$nextTick(() => {
        this.validateForm();
      });
    },

    /**
     * 验证表单
     */
    async validateForm () {
      try {
        if (this.$refs.form && this.$refs.form.validate) {
          const valid = await this.$refs.form.validate();
          this.formValid = valid;
          return valid;
        }

        // 如果没有表单组件，进行简单验证
        const rules = this.getFormRules();
        const errors = {};

        for (const field in rules) {
          const fieldRules = rules[field];
          const value = this.formData[field];

          for (const rule of fieldRules) {
            if (rule.required && (!value || value.toString().trim() === '')) {
              errors[field] = rule.message || `${field}不能为空`;
              break;
            }

            if (rule.min && value && value.toString().length < rule.min) {
              errors[field] = rule.message || `${field}长度不能少于${rule.min}个字符`;
              break;
            }

            if (rule.max && value && value.toString().length > rule.max) {
              errors[field] = rule.message || `${field}长度不能超过${rule.max}个字符`;
              break;
            }

            if (rule.pattern && value && !rule.pattern.test(value)) {
              errors[field] = rule.message || `${field}格式不正确`;
              break;
            }
          }
        }

        this.formErrors = errors;
        this.formValid = Object.keys(errors).length === 0;
        return this.formValid;
      } catch (error) {
        console.error('表单验证失败:', error);
        this.formValid = false;
        return false;
      }
    },

    /**
     * 提交表单
     */
    async submitForm () {
      try {
        // 验证表单
        const isValid = await this.validateForm();
        if (!isValid) {
          showError('请检查表单输入');
          return false;
        }

        this.formSubmitting = true;

        // 格式化提交数据
        const submitData = this.formatSubmitData(this.formData);

        // 获取API调用函数
        const apiCall = this.getSubmitApiCall();
        if (!apiCall) {
          throw new Error('提交API调用函数未定义');
        }

        // 执行API调用
        const response = await apiCallWrapper(
          () => apiCall(submitData),
          {
            ...ErrorHandlerPresets.important,
            loadingTitle: `${this.formModeText}中...`,
            errorTitle: `${this.formModeText}失败`
          }
        );

        if (response.success) {
          showSuccess(`${this.formModeText}成功`);

          // 触发成功回调
          if (this.onFormSubmitSuccess) {
            this.onFormSubmitSuccess(response.data);
          }

          // 关闭模态框
          this.closeModal();

          return true;
        } else {
          throw new Error(response.msg || `${this.formModeText}失败`);
        }
      } catch (error) {
        console.error('表单提交失败:', error);

        // 触发失败回调
        if (this.onFormSubmitError) {
          this.onFormSubmitError(error);
        }

        return false;
      } finally {
        this.formSubmitting = false;
      }
    },

    /**
     * 打开添加模态框
     */
    openAddModal () {
      this.modalMode = 'add';
      this.modalTitle = `${this.formModeText}${this.getEntityName()}`;
      this.resetForm();
      this.showModal = true;
    },

    /**
     * 打开编辑模态框
     */
    openEditModal (data) {
      this.modalMode = 'edit';
      this.modalTitle = `${this.formModeText}${this.getEntityName()}`;
      this.setFormData(data);
      this.showModal = true;
    },

    /**
     * 打开查看模态框
     */
    openViewModal (data) {
      this.modalMode = 'view';
      this.modalTitle = `${this.formModeText}${this.getEntityName()}`;
      this.setFormData(data);
      this.showModal = true;
    },

    /**
     * 关闭模态框
     */
    closeModal () {
      this.showModal = false;

      // 延迟重置表单，避免关闭动画时看到表单重置
      setTimeout(() => {
        this.resetForm();
      }, 300);
    },

    /**
     * 获取实体名称
     * 子类可以重写此方法
     */
    getEntityName () {
      return '项目';
    },

    /**
     * 表单字段变化处理
     */
    onFormFieldChange (field, value) {
      this.formData[field] = value;

      // 清除该字段的错误信息
      if (this.formErrors[field]) {
        this.$delete(this.formErrors, field);
      }

      // 延迟验证，避免频繁验证
      if (this.validateTimer) {
        clearTimeout(this.validateTimer);
      }
      this.validateTimer = setTimeout(() => {
        this.validateForm();
      }, 500);
    },

    /**
     * 确认离开表单
     */
    async confirmLeaveForm () {
      if (!this.isFormDirty) {
        return true;
      }

      return new Promise((resolve) => {
        uni.showModal({
          title: '确认离开',
          content: '表单内容已修改，确定要离开吗？',
          success: (res) => {
            resolve(res.confirm);
          }
        });
      });
    },

    /**
     * 表单提交成功回调
     * 子类可以重写此方法
     */
    onFormSubmitSuccess (data) {
      // 默认实现：刷新列表数据
      if (this.refreshData) {
        this.refreshData();
      }
    },

    /**
     * 表单提交失败回调
     * 子类可以重写此方法
     */
    onFormSubmitError (error) {
      // 默认实现：显示错误信息
      console.error('表单提交失败:', error);
    }
  },

  created () {
    // 初始化表单
    this.formRules = this.getFormRules();
    this.resetForm();
  },

  beforeDestroy () {
    // 清理定时器
    if (this.validateTimer) {
      clearTimeout(this.validateTimer);
    }
  }
};
