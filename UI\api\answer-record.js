/**
 * 答题记录相关API
 * 处理视频答题记录的提交、查询、统计等功能
 * 支持基于角色的权限控制：超管查看所有、管理查看员工、员工查看自己
 *
 * @deprecated 此API文件已废弃，请使用 user-batch-record.js 中的新API
 * 新API提供更完整的功能和更好的性能
 *
 * 迁移指南：
 * - submitAnswerRecord -> submitAnswer
 * - getAnswerStatistics -> getBatchStatistics
 * - queryAnswerRecords -> getBatchRecords (包含答题信息)
 */

import request from '../utils/request.js'

/**
 * 答题提交单个答案数据
 * @typedef {Object} VideoAnswerDto
 * @property {number} questionOrderNum - 题目序号
 * @property {string} questionText - 题目文本
 * @property {number} selectedOptionOrderNum - 选择的选项序号
 * @property {string} selectedOptionText - 选择的选项文本
 * @property {boolean} isCorrect - 是否正确
 */

/**
 * 答题提交请求参数
 * @typedef {Object} VideoAnswerSubmitDto
 * @property {number} batchId - 批次ID
 * @property {Array<VideoAnswerDto>} answers - 答题数据列表
 */

/**
 * 答题记录响应数据
 * @typedef {Object} VideoAnswerRecordResponseDto
 * @property {number} id - 记录ID
 * @property {number} userId - 用户ID
 * @property {string} userNickname - 用户昵称
 * @property {string} userAvatar - 用户头像
 * @property {string} employeeId - 员工ID
 * @property {string} employeeName - 员工姓名
 * @property {number} batchId - 批次ID
 * @property {string} batchName - 批次名称
 * @property {number} videoId - 视频ID
 * @property {string} videoTitle - 视频标题
 * @property {Array<VideoAnswerDto>} answers - 答题详情
 * @property {number} totalQuestions - 总题目数
 * @property {number} correctAnswers - 正确答题数
 * @property {number} accuracyRate - 正确率
 * @property {string} answerTime - 答题时间
 * @property {string} createTime - 创建时间
 */

/**
 * 答题记录统一查询请求参数
 * @typedef {Object} VideoAnswerRecordUnifiedQueryDto
 * @property {number} pageIndex - 页码
 * @property {number} pageSize - 每页大小
 * @property {string} [orderField] - 排序字段
 * @property {boolean} [isAsc] - 是否升序
 * @property {number} [batchId] - 批次ID
 * @property {number} [userId] - 用户ID
 * @property {string} [employeeId] - 员工ID
 * @property {number} [isCorrect] - 是否正确 (1:正确 0:错误)
 * @property {number} [minCorrectAnswers] - 最小正确答题数
 * @property {number} [maxCorrectAnswers] - 最大正确答题数
 * @property {string} [startTime] - 开始时间
 * @property {string} [endTime] - 结束时间
 * @property {boolean} [includeUserInfo] - 是否包含用户信息
 * @property {boolean} [includeBatchInfo] - 是否包含批次信息
 */

/**
 * 答题统计数据
 * @typedef {Object} VideoAnswerStatisticsDto
 * @property {number} answerUserCount - 答题用户数
 * @property {number} correctUserCount - 答对用户数
 * @property {number} correctRate - 正确率
 * @property {number} totalAnswerCount - 总答题次数
 * @property {number} correctAnswerCount - 正确答题次数
 * @property {number} averageAnswerTime - 平均答题时间(秒)
 * @property {string} timeRange - 时间范围
 * @property {Object} compare - 对比数据
 */

/**
 * 分页结果数据
 * @typedef {Object} PagedResultOfVideoAnswerRecordResponseDto
 * @property {Array<VideoAnswerRecordResponseDto>} items - 数据列表
 * @property {number} totalCount - 总记录数
 * @property {number} pageIndex - 当前页码
 * @property {number} pageSize - 每页大小
 * @property {number} totalPages - 总页数
 * @property {boolean} hasPreviousPage - 是否有上一页
 * @property {boolean} hasNextPage - 是否有下一页
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 提交答题记录
 * @param {VideoAnswerSubmitDto} data - 答题提交数据
 * @returns {Promise<ApiResult<number>>} 提交结果，返回记录ID
 */
export function submitAnswerRecord (data) {
  return request.post('/AnswerRecord/submit', data)
}

/**
 * 获取答题记录详情（支持权限控制）
 * @param {number} recordId - 记录ID
 * @returns {Promise<ApiResult<VideoAnswerRecordResponseDto>>} 答题记录详情
 */
export function getAnswerRecordDetail (recordId) {
  return request.get(`/AnswerRecord/${recordId}`)
}

/**
 * 统一查询答题记录（超级API）
 * 支持基于批次的查询和完整的权限控制
 * - 超级管理员：可查看所有答题记录
 * - 管理员：可查看所有员工及其用户的答题记录
 * - 员工：只能查看自己绑定用户的答题记录
 * @param {VideoAnswerRecordUnifiedQueryDto} params - 查询参数
 * @returns {Promise<ApiResult<PagedResultOfVideoAnswerRecordResponseDto>>} 分页查询结果
 */
export function queryAnswerRecords (params) {
  return request.post('/AnswerRecord/query', params)
}

/**
 * 获取答题统计信息（支持权限控制）
 * @param {number} batchId - 批次ID
 * @returns {Promise<ApiResult<VideoAnswerStatisticsDto>>} 答题统计信息
 */
export function getAnswerStatistics (batchId) {
  return request.get(`/AnswerRecord/statistics/${batchId}`)
}

/**
 * 批量查询多个批次的答题记录
 * @param {Array<number>} batchIds - 批次ID列表
 * @param {Object} [queryParams] - 额外查询参数
 * @returns {Promise<Array<ApiResult<PagedResultOfVideoAnswerRecordResponseDto>>>} 批量查询结果
 */
export function batchQueryAnswerRecords (batchIds, queryParams = {}) {
  const promises = batchIds.map(batchId =>
    queryAnswerRecords({
      ...queryParams,
      batchId,
      pageIndex: queryParams.pageIndex || 1,
      pageSize: queryParams.pageSize || 20
    })
  )
  return Promise.all(promises)
}

/**
 * 批量获取多个批次的统计信息
 * @param {Array<number>} batchIds - 批次ID列表
 * @returns {Promise<Array<ApiResult<VideoAnswerStatisticsDto>>>} 批量统计结果
 */
export function batchGetAnswerStatistics (batchIds) {
  const promises = batchIds.map(batchId => getAnswerStatistics(batchId))
  return Promise.all(promises)
}

/**
 * 根据用户ID查询答题记录
 * @param {number} userId - 用户ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.pageIndex=1] - 页码
 * @param {number} [options.pageSize=20] - 每页大小
 * @param {string} [options.startTime] - 开始时间
 * @param {string} [options.endTime] - 结束时间
 * @returns {Promise<ApiResult<PagedResultOfVideoAnswerRecordResponseDto>>} 查询结果
 */
export function queryAnswerRecordsByUser (userId, options = {}) {
  return queryAnswerRecords({
    userId,
    pageIndex: options.pageIndex || 1,
    pageSize: options.pageSize || 20,
    startTime: options.startTime,
    endTime: options.endTime,
    includeUserInfo: true,
    includeBatchInfo: true
  })
}

/**
 * 根据员工ID查询答题记录
 * @param {string} employeeId - 员工ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.pageIndex=1] - 页码
 * @param {number} [options.pageSize=20] - 每页大小
 * @param {string} [options.startTime] - 开始时间
 * @param {string} [options.endTime] - 结束时间
 * @returns {Promise<ApiResult<PagedResultOfVideoAnswerRecordResponseDto>>} 查询结果
 */
export function queryAnswerRecordsByEmployee (employeeId, options = {}) {
  return queryAnswerRecords({
    employeeId,
    pageIndex: options.pageIndex || 1,
    pageSize: options.pageSize || 20,
    startTime: options.startTime,
    endTime: options.endTime,
    includeUserInfo: true,
    includeBatchInfo: true
  })
}

/**
 * 根据批次ID查询答题记录
 * @param {number} batchId - 批次ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.pageIndex=1] - 页码
 * @param {number} [options.pageSize=20] - 每页大小
 * @param {number} [options.isCorrect] - 是否正确筛选
 * @param {string} [options.orderField] - 排序字段
 * @param {boolean} [options.isAsc] - 是否升序
 * @returns {Promise<ApiResult<PagedResultOfVideoAnswerRecordResponseDto>>} 查询结果
 */
export function queryAnswerRecordsByBatch (batchId, options = {}) {
  return queryAnswerRecords({
    batchId,
    pageIndex: options.pageIndex || 1,
    pageSize: options.pageSize || 20,
    isCorrect: options.isCorrect,
    orderField: options.orderField,
    isAsc: options.isAsc,
    includeUserInfo: true,
    includeBatchInfo: true
  })
}

/**
 * 查询正确率范围内的答题记录
 * @param {number} minCorrectAnswers - 最小正确答题数
 * @param {number} maxCorrectAnswers - 最大正确答题数
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoAnswerRecordResponseDto>>} 查询结果
 */
export function queryAnswerRecordsByAccuracy (minCorrectAnswers, maxCorrectAnswers, options = {}) {
  return queryAnswerRecords({
    minCorrectAnswers,
    maxCorrectAnswers,
    pageIndex: options.pageIndex || 1,
    pageSize: options.pageSize || 20,
    includeUserInfo: true,
    includeBatchInfo: true,
    ...options
  })
}

/**
 * 获取答题记录的正确率选项
 * @returns {Array<{value: number, label: string}>} 正确率选项
 */
export function getAccuracyOptions () {
  return [
    { value: 1, label: '正确' },
    { value: 0, label: '错误' }
  ]
}

/**
 * 获取排序字段选项
 * @returns {Array<{value: string, label: string}>} 排序字段选项
 */
export function getSortFieldOptions () {
  return [
    { value: 'createTime', label: '创建时间' },
    { value: 'answerTime', label: '答题时间' },
    { value: 'accuracyRate', label: '正确率' },
    { value: 'correctAnswers', label: '正确答题数' },
    { value: 'totalQuestions', label: '总题目数' }
  ]
}

/**
 * 验证答题提交数据
 * @param {VideoAnswerSubmitDto} data - 答题提交数据
 * @returns {Object} 验证结果 {valid: boolean, errors: Array<string>}
 */
export function validateAnswerSubmitData (data) {
  const errors = []

  if (!data.batchId || typeof data.batchId !== 'number') {
    errors.push('批次ID不能为空且必须为数字')
  }

  if (!data.answers || !Array.isArray(data.answers) || data.answers.length === 0) {
    errors.push('答题数据不能为空')
  } else {
    data.answers.forEach((answer, index) => {
      if (typeof answer.questionOrderNum !== 'number') {
        errors.push(`第${index + 1}题的题目序号必须为数字`)
      }
      if (!answer.questionText || typeof answer.questionText !== 'string') {
        errors.push(`第${index + 1}题的题目文本不能为空`)
      }
      if (typeof answer.selectedOptionOrderNum !== 'number') {
        errors.push(`第${index + 1}题的选项序号必须为数字`)
      }
      if (!answer.selectedOptionText || typeof answer.selectedOptionText !== 'string') {
        errors.push(`第${index + 1}题的选项文本不能为空`)
      }
      if (typeof answer.isCorrect !== 'boolean') {
        errors.push(`第${index + 1}题的正确性标识必须为布尔值`)
      }
    })
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// 默认导出所有答题记录相关API
export default {
  submitAnswerRecord,
  getAnswerRecordDetail,
  queryAnswerRecords,
  getAnswerStatistics,
  batchQueryAnswerRecords,
  batchGetAnswerStatistics,
  queryAnswerRecordsByUser,
  queryAnswerRecordsByEmployee,
  queryAnswerRecordsByBatch,
  queryAnswerRecordsByAccuracy,
  getAccuracyOptions,
  getSortFieldOptions,
  validateAnswerSubmitData
}
