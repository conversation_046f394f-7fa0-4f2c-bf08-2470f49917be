import request from '@/utils/request'

/**
 * 用户批次记录API
 * 整合观看、答题、红包功能的统一API
 */

// ==================== 记录管理 ====================

/**
 * 创建或获取用户批次记录
 * 用户进入视频页面时调用
 * @param {Object} data - 创建记录数据
 * @param {number} data.userId - 用户ID
 * @param {number} data.batchId - 批次ID
 * @param {string} [data.promotionLink] - 推广链接
 * @returns {Promise} 用户批次记录
 */
export function createOrGetRecord(data) {
  return request.post('/UserBatchRecord/create-or-get', data)
}

/**
 * 获取用户批次记录
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 用户批次记录
 */
export function getUserBatchRecord(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}`)
}

/**
 * 获取用户的所有批次记录
 * @param {number} userId - 用户ID
 * @returns {Promise} 用户批次记录列表
 */
export function getUserRecords(userId) {
  return request.get(`/UserBatchRecord/user/${userId}`)
}

// ==================== 观看进度管理 ====================

/**
 * 更新观看进度
 * @param {number} userId - 用户ID
 * @param {Object} data - 观看进度数据
 * @param {number} data.batchId - 批次ID
 * @param {number} data.viewDuration - 观看时长(秒)
 * @param {number} data.watchProgress - 观看进度(0-1)
 * @param {boolean} data.isCompleted - 是否完播
 * @returns {Promise} 是否成功
 */
export function updateWatchProgress(userId, data) {
  return request.post(`/UserBatchRecord/${userId}/watch-progress`, data)
}

/**
 * 获取用户观看状态
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 观看状态信息
 */
export function getWatchStatus(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/watch-status`)
}

/**
 * 开始观看（记录开始时间）
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 是否成功
 */
export function startWatching(userId, batchId) {
  return request.post(`/UserBatchRecord/${userId}/${batchId}/start-watching`)
}

/**
 * 批量更新观看进度
 * @param {Array} progressUpdates - 进度更新列表
 * @returns {Promise} 成功更新的数量
 */
export function batchUpdateProgress(progressUpdates) {
  return request.post('/UserBatchRecord/batch-update-progress', progressUpdates)
}

/**
 * 检查用户是否已观看
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 是否已观看
 */
export function hasWatched(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/has-watched`)
}

/**
 * 检查用户是否完播
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 是否完播
 */
export function hasCompleted(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/has-completed`)
}

// ==================== 答题管理 ====================

/**
 * 提交答题结果
 * @param {number} userId - 用户ID
 * @param {Object} data - 答题数据
 * @param {number} data.batchId - 批次ID
 * @param {number} data.totalQuestions - 总题目数
 * @param {number} data.correctAnswers - 正确答案数
 * @param {string} data.answerDetails - 答题详情JSON
 * @returns {Promise} 是否成功
 */
export function submitAnswer(userId, data) {
  return request.post(`/UserBatchRecord/${userId}/submit-answer`, data)
}

/**
 * 获取用户答题状态
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 答题状态信息
 */
export function getAnswerStatus(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/answer-status`)
}

/**
 * 获取用户答题详情
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 答题详情
 */
export function getAnswerDetail(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/answer-detail`)
}

/**
 * 验证答题资格
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 验证结果
 */
export function checkAnswerEligibility(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/answer-eligibility`)
}

/**
 * 检查用户是否已答题
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 是否已答题
 */
export function hasAnswered(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/has-answered`)
}

// ==================== 红包管理 ====================

/**
 * 发放红包
 * @param {number} userId - 用户ID
 * @param {Object} data - 红包数据
 * @param {number} data.batchId - 批次ID
 * @param {number} data.rewardAmount - 红包金额
 * @param {string} [data.transactionId] - 微信支付交易号
 * @param {string} [data.outTradeNo] - 微信支付订单号
 * @returns {Promise} 是否成功
 */
export function grantReward(userId, data) {
  return request.post(`/UserBatchRecord/${userId}/grant-reward`, data)
}

/**
 * 更新红包状态
 * @param {number} userId - 用户ID
 * @param {Object} data - 红包状态数据
 * @param {number} data.batchId - 批次ID
 * @param {number} data.rewardStatus - 红包状态(0:未发放,1:发放成功,2:发放失败)
 * @param {string} [data.failReason] - 失败原因
 * @param {string} [data.transactionId] - 微信支付交易号
 * @param {string} [data.outTradeNo] - 微信支付订单号
 * @returns {Promise} 是否成功
 */
export function updateRewardStatus(userId, data) {
  return request.post(`/UserBatchRecord/${userId}/update-reward-status`, data)
}

/**
 * 获取用户红包状态
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 红包状态信息
 */
export function getRewardStatus(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/reward-status`)
}

/**
 * 验证红包发放资格
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 验证结果
 */
export function checkRewardEligibility(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/reward-eligibility`)
}

/**
 * 批量发放红包
 * @param {number} batchId - 批次ID
 * @param {number} rewardAmount - 红包金额
 * @returns {Promise} 发放结果
 */
export function batchGrantRewards(batchId, rewardAmount) {
  return request.post(`/UserBatchRecord/batch/${batchId}/grant-rewards`, rewardAmount)
}

/**
 * 检查用户是否已获得红包
 * @param {number} userId - 用户ID
 * @param {number} batchId - 批次ID
 * @returns {Promise} 是否已获得红包
 */
export function hasReward(userId, batchId) {
  return request.get(`/UserBatchRecord/${userId}/${batchId}/has-reward`)
}

// ==================== 统计和查询 ====================

/**
 * 获取批次统计数据
 * 支持基于用户权限的数据过滤
 * @param {number} batchId - 批次ID
 * @returns {Promise} 批次统计数据
 */
export function getBatchStatistics(batchId) {
  return request.get(`/UserBatchRecord/batch/${batchId}/statistics`)
}

/**
 * 获取批次记录列表
 * 支持基于用户权限的数据过滤
 * @param {number} batchId - 批次ID
 * @returns {Promise} 批次记录列表
 */
export function getBatchRecords(batchId) {
  return request.get(`/UserBatchRecord/batch/${batchId}/records`)
}
