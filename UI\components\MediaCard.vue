<template>
    <view class="media-card" @click="handleClick">
        <view class="media-card-content">
            <view class="media-thumbnail">
                <image v-if="item.thumbnail" :src="item.thumbnail" mode="aspectFill" class="media-thumbnail-image">
                </image>
                <view v-else class="no-cover-placeholder">
                    <u-icon name="photo" size="32" color="#ccc"></u-icon>
                    <text style="font-size: 20rpx; color: #999; margin-top: 8rpx;">无封面</text>
                </view>
                <text class="media-duration" v-if="item.duration">{{ item.duration }}</text>
                <u-tag :text="item.statusText" :type="item.statusType" size="mini" class="media-status"></u-tag>
            </view>

            <view class="media-info">
                <text class="media-title">{{ item.title }}</text>
                <text class="media-description" v-if="item.description">{{ item.description }}</text>
                <view class="media-meta">
                    <view class="meta-item" v-for="(meta, index) in item.metaItems" :key="index">
                        <text class="meta-icon">{{ meta.icon }}</text>
                        <text>{{ meta.text }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'MediaCard',
    props: {
        item: {
            type: Object,
            required: true,
            default: () => ({
                id: '',
                title: '',
                description: '',
                thumbnail: '',
                duration: '',
                statusText: '',
                statusType: 'info',
                metaItems: []
            })
        }
    },
    methods: {
        handleClick (event) {
            // 阻止事件冒泡
            if (event) {
                event.stopPropagation();
                event.preventDefault();
            }

            this.$emit('click', this.item);
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/index.scss';

/* === 媒体卡片样式 === */
.media-card {
    background: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.media-card:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.media-card-content {
    display: flex;
    padding: 20rpx;
    gap: 20rpx;
}

.media-thumbnail {
    position: relative;
    width: 240rpx;
    height: 135rpx;
    border-radius: 12rpx;
    overflow: hidden;
    flex-shrink: 0;
}

.media-thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-duration {
    position: absolute;
    bottom: 8rpx;
    right: 8rpx;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
    font-size: 20rpx;
    font-weight: 500;
}

.media-status {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
}

.media-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.media-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.media-description {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.media-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
}

.meta-item {
    font-size: 24rpx;
    color: #999;
    display: flex;
    align-items: center;
    gap: 4rpx;
}

.meta-icon {
    font-size: 24rpx;
    opacity: 0.7;
}

/* 无封面占位符 */
.no-cover-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .media-card-content {
        flex-direction: column;
        gap: 16rpx;
    }

    .media-thumbnail {
        width: 100%;
        height: 200rpx;
    }
}
</style>
