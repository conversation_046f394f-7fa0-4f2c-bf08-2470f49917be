/**
 * 视频上传工具类
 * 处理视频文件上传、FormData构建、文件类型检测等功能
 */

import { getApiBaseURL } from "@/utils/config.js";
import adminAuthService from "@/utils/adminAuthService.js";

/**
 * 视频上传类
 */
export class VideoUploader {
  constructor() {
    this.uploadProgress = 0;
    this.isUploading = false;
  }

  /**
   * 从文件路径获取File对象
   * @param {string} filePath - 文件路径
   * @param {string} fileName - 文件名（可选）
   * @returns {Promise<File>} File对象
   */
  async getFileFromPath (filePath, fileName = null) {
    return new Promise((resolve, reject) => {
      // 生成合适的文件名
      let finalFileName = fileName;
      if (!finalFileName) {
        if (filePath.startsWith('blob:')) {
          finalFileName = 'file';
        } else {
          finalFileName = filePath.split('/').pop() || filePath.split('\\').pop() || 'file';
        }
      }

      // 在H5环境中，需要特殊处理
      if (filePath.startsWith('blob:')) {
        fetch(filePath)
          .then(response => response.blob())
          .then(blob => {
            // 根据MIME类型确定文件扩展名
            let extension = this.getExtensionFromMimeType(blob.type);

            // 如果文件名没有扩展名，添加扩展名
            if (finalFileName === 'file' || !finalFileName.includes('.')) {
              finalFileName = finalFileName + extension;
            }

            console.log(`创建File对象: 文件名=${finalFileName}, 类型=${blob.type}, 大小=${blob.size}`);
            const file = new File([blob], finalFileName, { type: blob.type });
            resolve(file);
          })
          .catch(reject);
      } else {
        // 其他情况，尝试使用uni.getFileSystemManager
        const fs = uni.getFileSystemManager();
        fs.readFile({
          filePath: filePath,
          success: (res) => {
            // 根据文件路径确定MIME类型
            const extension = finalFileName.toLowerCase().split('.').pop();
            let mimeType = this.getMimeTypeFromExtension(extension);

            console.log(`创建File对象: 文件名=${finalFileName}, 类型=${mimeType}, 路径=${filePath}`);
            const blob = new Blob([res.data], { type: mimeType });
            const file = new File([blob], finalFileName, { type: mimeType });
            resolve(file);
          },
          fail: reject
        });
      }
    });
  }

  /**
   * 根据MIME类型获取文件扩展名
   * @param {string} mimeType - MIME类型
   * @returns {string} 文件扩展名
   */
  getExtensionFromMimeType (mimeType) {
    if (mimeType.startsWith('video/')) {
      if (mimeType.includes('mp4')) return '.mp4';
      if (mimeType.includes('avi')) return '.avi';
      if (mimeType.includes('mov')) return '.mov';
      if (mimeType.includes('mkv')) return '.mkv';
      if (mimeType.includes('wmv')) return '.wmv';
      if (mimeType.includes('flv')) return '.flv';
      if (mimeType.includes('webm')) return '.webm';
      return '.mp4'; // 默认
    } else if (mimeType.startsWith('image/')) {
      if (mimeType.includes('jpeg') || mimeType.includes('jpg')) return '.jpg';
      if (mimeType.includes('png')) return '.png';
      if (mimeType.includes('gif')) return '.gif';
      if (mimeType.includes('webp')) return '.webp';
      return '.jpg'; // 默认
    }
    return '';
  }

  /**
   * 根据文件扩展名获取MIME类型
   * @param {string} extension - 文件扩展名
   * @returns {string} MIME类型
   */
  getMimeTypeFromExtension (extension) {
    const mimeTypeMap = {
      // 视频格式
      'mp4': 'video/mp4',
      'avi': 'video/avi',
      'mov': 'video/mov',
      'mkv': 'video/mkv',
      'wmv': 'video/wmv',
      'flv': 'video/flv',
      'webm': 'video/webm',
      // 图片格式
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp'
    };

    return mimeTypeMap[extension] || 'application/octet-stream';
  }

  /**
   * 构建问题数据为API格式
   * @param {Array} questions - 问题数组
   * @returns {Array} API格式的问题数组
   */
  buildQuestionsForApi (questions) {
    console.log('构建问题数据，原始数据:', questions);

    const result = questions.map((question, questionIndex) => {
      if (!question.content || !question.options) {
        console.error('问题数据不完整:', question);
        throw new Error(`问题${questionIndex + 1}数据不完整`);
      }

      return {
        questionText: question.content.trim(),
        orderNum: questionIndex,
        options: question.options.map((option, optionIndex) => ({
          optionText: option.text.trim(),
          isCorrect: optionIndex === question.correctAnswer,
          orderNum: optionIndex
        }))
      };
    });

    console.log('构建后的问题数据:', result);
    return result;
  }

  /**
   * 获取认证Token
   * @returns {string} 认证Token
   */
  getAuthToken () {
    const loginInfo = adminAuthService.getLoginInfo();
    if (loginInfo && loginInfo.accessToken) {
      return `Bearer ${loginInfo.accessToken}`;
    }
    return '';
  }

  /**
   * 同时上传视频和封面
   * @param {object} videoInfo - 视频信息
   * @param {function} onProgress - 进度回调
   * @returns {Promise} 上传结果
   */
  async uploadVideoAndCover (videoInfo, onProgress = null) {
    return new Promise(async (resolve, reject) => {
      try {
        console.log('开始准备FormData上传...');
        console.log('视频文件路径:', videoInfo.path);
        console.log('封面文件路径:', videoInfo.thumbnail);

        // 创建FormData
        const formData = new FormData();

        // 添加视频文件
        const videoFile = await this.getFileFromPath(videoInfo.path, 'video.mp4');
        formData.append('VideoFile', videoFile);
        console.log('已添加视频文件到FormData，文件名:', videoFile.name, '类型:', videoFile.type);

        // 添加封面文件
        const coverFile = await this.getFileFromPath(videoInfo.thumbnail, 'cover.jpg');
        formData.append('CoverFile', coverFile);
        console.log('已添加封面文件到FormData，文件名:', coverFile.name, '类型:', coverFile.type);

        // 添加其他数据
        const title = videoInfo.title.trim();
        const description = videoInfo.description || '';
        const rewardAmount = parseFloat(videoInfo.totalReward).toString();
        const questionsJson = JSON.stringify(this.buildQuestionsForApi(videoInfo.questions));

        formData.append('Title', title);
        formData.append('Description', description);
        formData.append('RewardAmount', rewardAmount);
        formData.append('QuestionsJson', questionsJson);

        // 压缩设置
        const enableCompression = videoInfo.enableCompression !== false; // 默认启用
        const compressionQuality = videoInfo.compressionQuality || 7; // 默认质量7
        formData.append('EnableCompression', enableCompression.toString());
        formData.append('CompressionQuality', compressionQuality.toString());

        console.log('FormData准备完成，数据详情:');
        console.log('Title:', title);
        console.log('Description:', description);
        console.log('RewardAmount:', rewardAmount);
        console.log('QuestionsJson:', questionsJson);
        console.log('开始上传...');

        // 使用XMLHttpRequest上传
        const xhr = new XMLHttpRequest();

        // 监听上传进度
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            this.uploadProgress = Math.round((event.loaded / event.total) * 100);
            console.log('上传进度:', this.uploadProgress + '%');
            if (onProgress) {
              onProgress(this.uploadProgress);
            }
          }
        });

        // 监听上传完成
        xhr.addEventListener('load', () => {
          console.log('上传完成，状态码:', xhr.status);
          console.log('响应数据:', xhr.responseText);

          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              console.log('解析后的响应:', response);

              if (response.success && response.data) {
                this.uploadProgress = 100;
                resolve(response.data);
              } else {
                reject(new Error(response.msg || response.message || '上传失败'));
              }
            } catch (parseError) {
              console.error('响应解析失败:', parseError);
              reject(new Error(`服务器返回了无效的数据格式: ${xhr.responseText}`));
            }
          } else {
            reject(new Error(`服务器返回错误状态码: ${xhr.status}`));
          }
        });

        // 监听上传错误
        xhr.addEventListener('error', () => {
          console.error('上传请求失败');
          reject(new Error('网络请求失败'));
        });

        // 发送请求
        xhr.open('POST', getApiBaseURL() + '/Video/upload-complete');
        xhr.setRequestHeader('Authorization', this.getAuthToken());
        xhr.send(formData);

      } catch (error) {
        console.error('FormData准备失败:', error);
        reject(error);
      }
    });
  }
}

// 创建单例实例
export const videoUploader = new VideoUploader();

// 默认导出
export default VideoUploader;
