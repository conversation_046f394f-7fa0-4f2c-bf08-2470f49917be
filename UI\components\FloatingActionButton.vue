<template>
    <view class="floating-btn-wrapper" :style="{ left: btnPosition.left + 'px', top: btnPosition.top + 'px' }"
        @touchstart="touchStart" @touchmove.stop.prevent="moveFloatingBtn" @touchend="snapToEdge">
        <view class="floating-action-btn" :class="[`btn-${type}`]" @tap="handleClick">
            <image v-if="icon" :src="icon" mode="aspectFit" class="btn-icon"></image>
            <text class="iconfont" v-if="iconClass" :class="iconClass"></text>
            <text class="btn-text" v-if="text">{{ text }}</text>
        </view>
    </view>
</template>

<script>
export default {
    name: 'FloatingActionButton',
    props: {
        // 按钮类型：primary, success, warning, danger
        type: {
            type: String,
            default: 'primary'
        },
        // 按钮文本
        text: {
            type: String,
            default: ''
        },
        // 图标路径
        icon: {
            type: String,
            default: ''
        },
        // 图标类名（iconfont）
        iconClass: {
            type: String,
            default: ''
        },
        // 初始位置
        initialPosition: {
            type: Object,
            default: () => ({
                right: 20,
                bottom: 120
            })
        }
    },
    data () {
        return {
            btnPosition: {
                left: 0,
                top: 0
            },
            startTouch: {
                x: 0,
                y: 0
            }
        }
    },
    mounted () {
        // 初始化按钮位置
        this.initPosition();
    },
    methods: {
        initPosition () {
            uni.getSystemInfo({
                success: (res) => {
                    const btnSize = 40; // 80rpx 转换为 px 大约是 40px
                    // 根据 initialPosition 计算实际位置
                    if (this.initialPosition.right !== undefined) {
                        this.btnPosition.left = res.windowWidth - this.initialPosition.right - btnSize;
                    } else if (this.initialPosition.left !== undefined) {
                        this.btnPosition.left = this.initialPosition.left;
                    } else {
                        this.btnPosition.left = res.windowWidth - 60;
                    }

                    if (this.initialPosition.bottom !== undefined) {
                        this.btnPosition.top = res.windowHeight - this.initialPosition.bottom - btnSize;
                    } else if (this.initialPosition.top !== undefined) {
                        this.btnPosition.top = this.initialPosition.top;
                    } else {
                        this.btnPosition.top = res.windowHeight - 160;
                    }
                }
            });
        },
        touchStart (e) {
            // 记录初始触摸位置
            this.startTouch = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        },
        moveFloatingBtn (e) {
            const touch = e.touches[0];
            const btnSize = 40; // 80rpx 转换为 px 大约是 40px
            this.btnPosition.left = touch.clientX - btnSize / 2;
            this.btnPosition.top = touch.clientY - btnSize / 2;
        },
        snapToEdge () {
            // 获取屏幕宽度和高度
            const screenWidth = uni.getSystemInfoSync().windowWidth;
            const screenHeight = uni.getSystemInfoSync().windowHeight;
            const btnSize = 40; // 80rpx 转换为 px 大约是 40px
            const edgeMargin = 15; // 边距

            // 如果距离右边更近，则贴右边
            if (this.btnPosition.left > screenWidth / 2) {
                this.btnPosition.left = screenWidth - btnSize - edgeMargin;
            } else {
                this.btnPosition.left = edgeMargin; // 贴左边
            }

            // 确保按钮不会超出屏幕上下边界
            if (this.btnPosition.top < 100) {
                this.btnPosition.top = 100; // 顶部留出一些空间
            } else if (this.btnPosition.top > screenHeight - 150) {
                this.btnPosition.top = screenHeight - 150; // 底部留出一些空间
            }
        },
        handleClick () {
            this.$emit('click');
        }
    }
}
</script>

<style>
.floating-btn-wrapper {
    position: fixed;
    z-index: 999;
}

.floating-action-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.25);
    position: relative;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007AFF, #0056CC);
}

.btn-success {
    background-color: #34C759;
}

.btn-warning {
    background-color: #FF9500;
}

.btn-danger {
    background-color: #FF3B30;
}

.btn-icon {
    width: 32rpx;
    height: 32rpx;
    margin-bottom: 2rpx;
}

.floating-action-btn .iconfont {
    color: #fff;
    font-size: 32rpx;
    margin-bottom: 2rpx;
}

.btn-text {
    color: #fff;
    font-size: 18rpx;
    font-weight: 500;
}

.floating-action-btn:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}
</style>