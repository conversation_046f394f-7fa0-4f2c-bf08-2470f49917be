<template>
    <view class="data-page">
        <!-- 数据展示组件 -->
        <BatchDataDisplay :batchId="batchId" :batchData="batchData" />
    </view>
</template>

<script>
import { getBatchDetail } from "@/api/batch.js";
import BatchDataDisplay from "@/components/BatchDataDisplay.vue";

export default {
    name: 'BatchDataPage',
    components: {
        BatchDataDisplay
    },
    data () {
        return {
            batchId: '',
            batchData: {
                id: '',
                title: '',
                totalViews: 0,
                totalReward: 0,
                totalStudents: 0
            }
        }
    },
    async onLoad (options) {
        if (options && options.id) {
            this.batchId = options.id;
            await this.loadBatchData();
        } else {
            uni.showToast({
                title: '批次ID缺失',
                icon: 'none'
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        }
    },
    methods: {
        async loadBatchData () {
            try {
                uni.showLoading({
                    title: "加载中...",
                });

                const response = await getBatchDetail(this.batchId);

                if (response.success && response.data) {
                    const batch = response.data;
                    this.batchData = {
                        id: batch.id,
                        title: batch.name || batch.title,
                        totalViews: batch.currentParticipants || 0,
                        totalReward: batch.rewardAmount || 0,
                        totalStudents: batch.totalStudents || batch.currentParticipants || 0,
                        redPacketAmount: batch.redPacketAmount || 0,
                        // 传递完整的统计数据
                        statistics: batch.statistics || null
                    };

                    console.log('批次数据加载完成:', this.batchData);
                } else {
                    throw new Error(response.msg || '获取批次数据失败');
                }

                uni.hideLoading();
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: "加载失败",
                    icon: "none",
                });
            }
        },
        goBack () {
            uni.navigateBack();
        }
    }
}
</script>

<style lang="scss">
@import '@/styles/index.scss';

.data-page {
    width: 100%;
    min-height: 100vh;
    background: #f8faff;
}
</style>
