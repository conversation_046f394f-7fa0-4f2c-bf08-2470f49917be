/**
 * 微信用户认证服务
 * 专门处理微信用户的登录认证和用户信息管理
 */

// 导入API模块
import { wechatLogin as wechatLoginApi } from '../api/video-user.js'

// 存储键名 - 与系统管理员完全分离
const WECHAT_STORAGE_KEYS = {
  USER_INFO: 'wechatUserInfo',
  USER_TOKEN: 'wechatUserToken'
}

/**
 * 微信用户认证服务类
 */
class WechatUserService {
  /**
   * 微信用户登录
   * @param {Object} loginData - 微信登录数据
   * @param {string} loginData.code - 微信授权码
   * @param {string} loginData.employeeId - 员工ID（可选）
   * @param {number} loginData.batchId - 批次ID（可选）
   * @returns {Promise<Object>} 登录结果
   */
  async wechatLogin (loginData) {
    try {
      const result = await wechatLoginApi(loginData)

      if (result.success && result.data) {
        // 保存微信用户信息和token
        this.saveUserInfo(result.data.userInfo)
        this.saveUserToken(result.data.token)

        return {
          success: true,
          message: '微信登录成功',
          data: result.data
        }
      } else {
        throw new Error(result.message || '微信登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      return {
        success: false,
        message: error.message || '微信登录失败，请重试'
      }
    }
  }

  /**
   * 尝试微信自动登录
   * @param {Object} options - 页面参数
   * @returns {Promise<Object>} 登录结果
   */
  async tryWechatAutoLogin (options) {
    try {
      // #ifdef MP-WEIXIN
      const loginRes = await uni.login({
        provider: 'weixin'
      })

      if (!loginRes.code) {
        throw new Error('获取微信授权码失败')
      }

      const loginData = {
        code: loginRes.code,
        employeeId: options.employeeId || null,
        batchId: options.batchId ? parseInt(options.batchId) : null
      }

      return await this.wechatLogin(loginData)
      // #endif

      // #ifndef MP-WEIXIN
      throw new Error('微信登录仅支持在微信小程序中使用')
      // #endif
    } catch (error) {
      console.error('微信自动登录失败:', error)
      throw error
    }
  }

  /**
   * 保存微信用户信息到存储
   * @param {Object} userInfo - 用户信息
   */
  saveUserInfo (userInfo) {
    try {
      uni.setStorageSync(WECHAT_STORAGE_KEYS.USER_INFO, userInfo)
    } catch (error) {
      console.error('Error saving wechat user info:', error)
    }
  }

  /**
   * 保存微信用户token到存储
   * @param {string} token - 用户token
   */
  saveUserToken (token) {
    try {
      uni.setStorageSync(WECHAT_STORAGE_KEYS.USER_TOKEN, token)
    } catch (error) {
      console.error('Error saving wechat user token:', error)
    }
  }

  /**
   * 获取当前微信用户信息
   * @returns {Object|null}
   */
  getUserInfo () {
    try {
      return uni.getStorageSync(WECHAT_STORAGE_KEYS.USER_INFO) || null
    } catch (error) {
      console.error('Error getting wechat user info:', error)
      return null
    }
  }

  /**
   * 获取当前微信用户token
   * @returns {string|null}
   */
  getUserToken () {
    try {
      return uni.getStorageSync(WECHAT_STORAGE_KEYS.USER_TOKEN) || null
    } catch (error) {
      console.error('Error getting wechat user token:', error)
      return null
    }
  }

  /**
   * 检查微信用户是否已登录
   * @returns {boolean}
   */
  isLoggedIn () {
    const userInfo = this.getUserInfo()
    const token = this.getUserToken()
    return !!(userInfo && token)
  }

  /**
   * 获取微信用户ID
   * @returns {string|null}
   */
  getUserId () {
    const userInfo = this.getUserInfo()
    return userInfo ? userInfo.id : null
  }

  /**
   * 获取微信用户昵称
   * @returns {string|null}
   */
  getNickname () {
    const userInfo = this.getUserInfo()
    return userInfo ? userInfo.nickname : null
  }

  /**
   * 获取微信用户头像
   * @returns {string|null}
   */
  getAvatar () {
    const userInfo = this.getUserInfo()
    return userInfo ? userInfo.avatar : null
  }

  /**
   * 获取微信用户OpenID
   * @returns {string|null}
   */
  getOpenId () {
    const userInfo = this.getUserInfo()
    return userInfo ? userInfo.openId : null
  }

  /**
   * 获取绑定的员工ID
   * @returns {string|null}
   */
  getEmployeeId () {
    const userInfo = this.getUserInfo()
    return userInfo ? userInfo.employeeId : null
  }

  /**
   * 微信用户登出
   */
  logout () {
    try {
      // 清理本地存储
      uni.removeStorageSync(WECHAT_STORAGE_KEYS.USER_INFO)
      uni.removeStorageSync(WECHAT_STORAGE_KEYS.USER_TOKEN)
      
      console.log('微信用户已登出')
      return true
    } catch (error) {
      console.error('Error during wechat user logout:', error)
      return false
    }
  }

  /**
   * 获取微信用户显示信息
   * @returns {Object}
   */
  getDisplayInfo () {
    const userInfo = this.getUserInfo()
    
    if (!userInfo) {
      return {
        nickname: '未登录',
        avatar: '/static/logo.png',
        userType: 'guest'
      }
    }

    return {
      id: userInfo.id,
      nickname: userInfo.nickname || '微信用户',
      avatar: userInfo.avatar || '/static/logo.png',
      openId: userInfo.openId,
      employeeId: userInfo.employeeId,
      userType: 'wechat_user',
      createTime: userInfo.createTime,
      lastLogin: userInfo.lastLogin
    }
  }

  /**
   * 初始化模拟微信用户（开发测试用）
   */
  initMockWechatUser () {
    const mockUser = {
      id: 'mock_wechat_user_001',
      nickname: '微信用户',
      avatar: '/static/logo.png',
      openId: 'mock_openid_001',
      employeeId: null,
      createTime: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    }

    const mockToken = 'mock_wechat_token_001'

    this.saveUserInfo(mockUser)
    this.saveUserToken(mockToken)

    console.log('模拟微信用户初始化完成:', mockUser.nickname)
    return { userInfo: mockUser, token: mockToken }
  }

  /**
   * 跳转到微信用户登录页面（如果需要）
   * @param {string} returnUrl - 登录成功后返回的URL
   */
  redirectToLogin (returnUrl = '') {
    const url = returnUrl 
      ? `/pages/user-login/index?returnUrl=${encodeURIComponent(returnUrl)}`
      : '/pages/user-login/index'
    
    uni.redirectTo({ url })
  }

  /**
   * 检查是否需要微信用户认证
   * @param {Object} options - 页面参数
   * @returns {boolean} 是否需要认证
   */
  requireAuth (options = {}) {
    if (!this.isLoggedIn()) {
      console.log('微信用户未登录，需要认证')
      return true
    }
    return false
  }
}

// 创建单例实例
const wechatUserService = new WechatUserService()

// 导出服务实例和工具函数
export default wechatUserService
export { WechatUserService, WECHAT_STORAGE_KEYS }

// 导出常用函数以便使用
export const getWechatUserInfo = () => wechatUserService.getUserInfo()
export const getWechatUserToken = () => wechatUserService.getUserToken()
export const isWechatUserLoggedIn = () => wechatUserService.isLoggedIn()
export const getWechatUserId = () => wechatUserService.getUserId()
export const getWechatUserNickname = () => wechatUserService.getNickname()
