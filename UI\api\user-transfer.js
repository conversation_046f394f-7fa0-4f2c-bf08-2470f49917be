/**
 * 用户转移相关API
 * 处理用户转移到新员工、查询转移记录等功能
 */

import request from '../utils/request.js'

/**
 * 用户转移请求参数
 * @typedef {Object} VideoUserTransferDto
 * @property {Array<number>} userIds - 用户ID列表
 * @property {string} toEmployeeId - 目标员工ID
 * @property {string} reason - 转移原因
 */

/**
 * 用户转移查询参数
 * @typedef {Object} VideoUserTransferQueryDto
 * @property {number} [UserId] - 用户ID
 * @property {string} [FromEmployeeId] - 原员工ID
 * @property {string} [ToEmployeeId] - 目标员工ID
 * @property {string} [OperatorId] - 操作员ID
 * @property {string} [OperatorName] - 操作员姓名
 * @property {string} [Reason] - 转移原因
 * @property {string} [StartTime] - 开始时间
 * @property {string} [EndTime] - 结束时间
 * @property {number} [PageIndex] - 页码
 * @property {number} [PageSize] - 每页大小
 * @property {string} [OrderField] - 排序字段
 * @property {boolean} [IsAsc] - 是否升序
 */

/**
 * 用户转移响应数据
 * @typedef {Object} VideoUserTransferResponseDto
 * @property {number} id - 转移记录ID
 * @property {number} userId - 用户ID
 * @property {string} userNickname - 用户昵称
 * @property {string} fromEmployeeId - 原员工ID
 * @property {string} fromEmployeeName - 原员工姓名
 * @property {string} toEmployeeId - 目标员工ID
 * @property {string} toEmployeeName - 目标员工姓名
 * @property {string} reason - 转移原因
 * @property {string} operatorId - 操作员ID
 * @property {string} operatorName - 操作员姓名
 * @property {string} transferTime - 转移时间
 */

/**
 * 分页结果数据
 * @typedef {Object} PagedResultOfVideoUserTransferResponseDto
 * @property {Array<VideoUserTransferResponseDto>} items - 数据列表
 * @property {number} totalCount - 总记录数
 * @property {number} pageIndex - 当前页码
 * @property {number} pageSize - 每页大小
 * @property {number} totalPages - 总页数
 * @property {boolean} hasPreviousPage - 是否有上一页
 * @property {boolean} hasNextPage - 是否有下一页
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 转移用户到新员工
 * @param {VideoUserTransferDto} data - 转移数据
 * @returns {Promise<ApiResult<boolean>>} 转移结果
 */
export function transferUsers (data) {
  return request.post('/UserTransfer', data)
}

/**
 * 分页查询用户转移记录列表
 * @param {VideoUserTransferQueryDto} params - 查询参数
 * @returns {Promise<ApiResult<PagedResultOfVideoUserTransferResponseDto>>} 分页查询结果
 */
export function queryUserTransferRecords (params) {
  return request.get('/UserTransfer', params)
}

/**
 * 根据用户ID查询转移记录
 * @param {number} userId - 用户ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.PageIndex=1] - 页码
 * @param {number} [options.PageSize=20] - 每页大小
 * @returns {Promise<ApiResult<PagedResultOfVideoUserTransferResponseDto>>} 查询结果
 */
export function queryTransferRecordsByUser (userId, options = {}) {
  return queryUserTransferRecords({
    UserId: userId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据员工ID查询转移记录（作为原员工）
 * @param {string} employeeId - 员工ID
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoUserTransferResponseDto>>} 查询结果
 */
export function queryTransferRecordsByFromEmployee (employeeId, options = {}) {
  return queryUserTransferRecords({
    FromEmployeeId: employeeId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据员工ID查询转移记录（作为目标员工）
 * @param {string} employeeId - 员工ID
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoUserTransferResponseDto>>} 查询结果
 */
export function queryTransferRecordsByToEmployee (employeeId, options = {}) {
  return queryUserTransferRecords({
    ToEmployeeId: employeeId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据操作员查询转移记录
 * @param {string} operatorId - 操作员ID
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoUserTransferResponseDto>>} 查询结果
 */
export function queryTransferRecordsByOperator (operatorId, options = {}) {
  return queryUserTransferRecords({
    OperatorId: operatorId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据时间范围查询转移记录
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoUserTransferResponseDto>>} 查询结果
 */
export function queryTransferRecordsByTimeRange (startTime, endTime, options = {}) {
  return queryUserTransferRecords({
    StartTime: startTime,
    EndTime: endTime,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 单个用户转移
 * @param {number} userId - 用户ID
 * @param {string} toEmployeeId - 目标员工ID
 * @param {string} [reason] - 转移原因
 * @returns {Promise<ApiResult<boolean>>} 转移结果
 */
export function transferSingleUser (userId, toEmployeeId, reason = '') {
  return transferUsers({
    userIds: [userId],
    toEmployeeId,
    reason
  })
}

/**
 * 批量转移用户
 * @param {Array<number>} userIds - 用户ID列表
 * @param {string} toEmployeeId - 目标员工ID
 * @param {string} [reason] - 转移原因
 * @returns {Promise<ApiResult<boolean>>} 转移结果
 */
export function batchTransferUsers (userIds, toEmployeeId, reason = '') {
  return transferUsers({
    userIds,
    toEmployeeId,
    reason
  })
}

/**
 * 获取排序字段选项
 * @returns {Array<{value: string, label: string}>} 排序字段选项
 */
export function getTransferSortFieldOptions () {
  return [
    { value: 'transferTime', label: '转移时间' },
    { value: 'userNickname', label: '用户昵称' },
    { value: 'fromEmployeeName', label: '原员工' },
    { value: 'toEmployeeName', label: '目标员工' },
    { value: 'operatorName', label: '操作员' }
  ]
}

/**
 * 获取常用转移原因
 * @returns {Array<string>} 常用转移原因列表
 */
export function getCommonTransferReasons () {
  return [
    '员工离职',
    '工作调整',
    '业务重组',
    '用户要求',
    '管理需要',
    '其他原因'
  ]
}

/**
 * 验证转移数据
 * @param {VideoUserTransferDto} data - 转移数据
 * @returns {Object} 验证结果 {valid: boolean, errors: Array<string>}
 */
export function validateTransferData (data) {
  const errors = []

  if (!data.userIds || !Array.isArray(data.userIds) || data.userIds.length === 0) {
    errors.push('用户ID列表不能为空')
  }

  if (!data.toEmployeeId || data.toEmployeeId.trim() === '') {
    errors.push('目标员工ID不能为空')
  }

  if (!data.reason || data.reason.trim() === '') {
    errors.push('转移原因不能为空')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 格式化转移记录数据
 * @param {Array<VideoUserTransferResponseDto>} records - 转移记录列表
 * @returns {Array<Object>} 格式化后的记录列表
 */
export function formatTransferRecords (records) {
  if (!Array.isArray(records)) return []

  return records.map(record => ({
    ...record,
    // 格式化时间
    transferTime: record.transferTime ? new Date(record.transferTime).toLocaleString() : '',

    // 处理用户昵称
    userNickname: record.userNickname || '未设置昵称',

    // 处理员工姓名
    fromEmployeeName: record.fromEmployeeName || '未知员工',
    toEmployeeName: record.toEmployeeName || '未知员工',

    // 处理操作员
    operatorName: record.operatorName || '系统操作',

    // 处理转移原因
    reason: record.reason || '无'
  }))
}

// 默认导出所有用户转移相关API
export default {
  transferUsers,
  queryUserTransferRecords,
  queryTransferRecordsByUser,
  queryTransferRecordsByFromEmployee,
  queryTransferRecordsByToEmployee,
  queryTransferRecordsByOperator,
  queryTransferRecordsByTimeRange,
  transferSingleUser,
  batchTransferUsers,
  getTransferSortFieldOptions,
  getCommonTransferReasons,
  validateTransferData,
  formatTransferRecords
}
