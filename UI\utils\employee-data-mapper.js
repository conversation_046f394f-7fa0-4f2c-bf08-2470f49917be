/**
 * 员工数据格式映射工具
 * 处理API数据与页面数据格式的转换
 * 支持新的SysUser API数据结构
 */

/**
 * 员工状态映射
 */
export const EMPLOYEE_STATUS_MAP = {
  1: 'active',    // 在职/启用
  0: 'disabled',  // 停职/禁用
  2: 'dismissed'  // 离职
};

/**
 * 员工级别映射（对应userType）
 */
export const EMPLOYEE_LEVEL_MAP = {
  1: 'admin',     // 超管
  2: 'manager',   // 管理
  3: 'employee'   // 员工
};

/**
 * 用户类型映射
 */
export const USER_TYPE_MAP = {
  1: '超管',
  2: '管理',
  3: '员工'
};

/**
 * 反向状态映射（页面状态到API状态）
 */
export const REVERSE_STATUS_MAP = {
  'active': 1,
  'dismissed': 2,
  'disabled': 3
};

/**
 * 反向级别映射（页面角色到API级别）
 */
export const REVERSE_LEVEL_MAP = {
  'employee': 1,
  'manager': 2,
  'admin': 3
};

/**
 * 格式化SysUser数据（新API数据转页面数据）
 * @param {Object} apiSysUser - API返回的SysUserWithStatisticsDto数据
 * @returns {Object} 页面使用的用户数据格式
 */
export function formatSysUserData (apiSysUser) {
  if (!apiSysUser) return null;

  return {
    id: apiSysUser.id,
    username: apiSysUser.userName,
    realName: apiSysUser.realName,
    avatar: apiSysUser.avatar,
    phone: apiSysUser.mobile || '',
    email: apiSysUser.email || '',
    role: EMPLOYEE_LEVEL_MAP[apiSysUser.userType] || 'employee',
    level: apiSysUser.userType,
    userType: apiSysUser.userType,
    userTypeName: apiSysUser.userTypeName || USER_TYPE_MAP[apiSysUser.userType] || '',
    parentUserId: apiSysUser.parentUserId,
    parentUserName: apiSysUser.parentUserName || '',
    managerId: apiSysUser.parentUserId, // 为了兼容现有代码，将 parentUserId 映射为 managerId
    managerName: apiSysUser.parentUserName || '',
    roleName: apiSysUser.roleName,
    roleCode: apiSysUser.roleCode,
    department: '默认部门', // SysUser API没有部门字段，使用默认值
    position: apiSysUser.userTypeName || USER_TYPE_MAP[apiSysUser.userType] || '员工',
    registerTime: apiSysUser.createTime || new Date().toISOString(),
    lastLoginTime: apiSysUser.lastLoginTime || '',
    lastLoginIp: apiSysUser.lastLoginIp || '',
    status: EMPLOYEE_STATUS_MAP[apiSysUser.status] || 'active',
    statusCode: apiSysUser.status,
    disabled: apiSysUser.status !== 1, // 1表示启用
    dismissed: apiSysUser.status === 0, // 0表示禁用
    type: EMPLOYEE_LEVEL_MAP[apiSysUser.userType] || 'employee',

    // 层级关系统计数据
    employeeCount: apiSysUser.directSubordinateCount || 0,
    userCount: apiSysUser.totalSubordinateUserCount || 0,
    directSubordinateCount: apiSysUser.directSubordinateCount || 0,
    totalSubordinateUserCount: apiSysUser.totalSubordinateUserCount || 0,

    // 统计数据（从statistics对象中提取）
    statistics: apiSysUser.statistics || {},

    // 时间维度的统计数据（从statistics中提取或使用默认值）
    totalViews: extractTimeStats(apiSysUser.statistics, 'views'),
    totalQuizzes: extractTimeStats(apiSysUser.statistics, 'quizzes'),
    totalRewards: extractTimeStats(apiSysUser.statistics, 'rewards'),

    // 其他字段
    remark: apiSysUser.remark || '',
    createTime: apiSysUser.createTime,
    updateTime: apiSysUser.updateTime
  };
}

/**
 * 从统计数据中提取时间维度统计
 * @param {Object} statistics - 统计数据对象
 * @param {string} type - 统计类型 (views, quizzes, rewards)
 * @returns {Object} 时间维度统计数据
 */
function extractTimeStats (statistics, type) {
  if (!statistics || !statistics[type]) {
    return {
      today: 0,
      yesterday: 0,
      thisWeek: 0,
      thisMonth: 0
    };
  }

  const stats = statistics[type];
  return {
    today: stats.today || 0,
    yesterday: stats.yesterday || 0,
    thisWeek: stats.thisWeek || 0,
    thisMonth: stats.thisMonth || 0
  };
}

/**
 * 格式化员工数据（API数据转页面数据）
 * @param {Object} apiEmployee - API返回的员工数据
 * @returns {Object} 页面使用的员工数据格式
 */
export function formatEmployeeData (apiEmployee) {
  if (!apiEmployee) return null;

  // 如果是新的SysUser数据结构，使用新的格式化函数
  if (apiEmployee.userName && apiEmployee.userType !== undefined) {
    return formatSysUserData(apiEmployee);
  }

  // 兼容旧的员工数据结构
  return {
    id: apiEmployee.employeeId || apiEmployee.id,
    username: apiEmployee.name || apiEmployee.username,
    realName: apiEmployee.realName || apiEmployee.name,
    avatar: apiEmployee.avatar,
    phone: apiEmployee.phone || '',
    email: apiEmployee.email || '',
    role: EMPLOYEE_LEVEL_MAP[apiEmployee.level] || 'employee',
    level: apiEmployee.level,
    managerId: apiEmployee.managerId,
    managerName: apiEmployee.managerName || '',
    department: apiEmployee.department || '默认部门',
    position: apiEmployee.position || '员工',
    registerTime: apiEmployee.createTime || new Date().toISOString(),
    lastLoginTime: apiEmployee.lastLoginTime || '',
    status: EMPLOYEE_STATUS_MAP[apiEmployee.status] || 'active',
    statusCode: apiEmployee.status,
    disabled: apiEmployee.status !== 1, // 1表示在职
    dismissed: apiEmployee.status === 2, // 2表示离职
    type: EMPLOYEE_LEVEL_MAP[apiEmployee.level] || 'employee',

    // 统计数据，如果API没有提供则使用默认值
    employeeCount: apiEmployee.employeeCount || 0,
    userCount: apiEmployee.userCount || 0,

    // 时间维度的统计数据
    totalViews: {
      today: apiEmployee.todayViews || 0,
      yesterday: apiEmployee.yesterdayViews || 0,
      thisWeek: apiEmployee.thisWeekViews || 0,
      thisMonth: apiEmployee.thisMonthViews || 0
    },
    totalQuizzes: {
      today: apiEmployee.todayQuizzes || 0,
      yesterday: apiEmployee.yesterdayQuizzes || 0,
      thisWeek: apiEmployee.thisWeekQuizzes || 0,
      thisMonth: apiEmployee.thisMonthQuizzes || 0
    },
    totalRewards: {
      today: apiEmployee.todayRewards || 0,
      yesterday: apiEmployee.yesterdayRewards || 0,
      thisWeek: apiEmployee.thisWeekRewards || 0,
      thisMonth: apiEmployee.thisMonthRewards || 0
    },

    // 其他可能的字段
    remark: apiEmployee.remark || '',
    createTime: apiEmployee.createTime,
    updateTime: apiEmployee.updateTime
  };
}

/**
 * 格式化管理数据（API数据转页面数据）
 * @param {Object} apiManager - API返回的管理数据
 * @returns {Object} 页面使用的管理数据格式
 */
export function formatManagerData (apiManager) {
  const baseData = formatEmployeeData(apiManager);

  return {
    ...baseData,
    role: 'manager',
    type: 'manager',
    // 管理特有的字段
    members: apiManager.subordinateCount || 0,
    parent: apiManager.parentManagerName || '系统管理员'
  };
}

/**
 * 格式化用户数据（API数据转页面数据）
 * @param {Object} apiUser - API返回的用户数据
 * @returns {Object} 页面使用的用户数据格式
 */
export function formatUserData (apiUser) {
  if (!apiUser) return null;

  return {
    id: apiUser.id || apiUser.userId,
    // 优先使用 nickname，然后是 username 或 name
    username: apiUser.nickname || apiUser.username || apiUser.name || '未设置',
    realName: apiUser.realName || apiUser.nickname || apiUser.name || '未设置',
    avatar: apiUser.avatar,
    phone: apiUser.phone || apiUser.mobile || '',
    email: apiUser.email || '',
    role: 'user',
    type: 'user', // 设置用户类型
    employeeId: apiUser.employeeId,
    employeeName: apiUser.employeeName || '',
    // 处理不同的时间字段名称
    registerTime: apiUser.createTime || apiUser.registerTime || new Date().toISOString(),
    lastLoginTime: apiUser.lastLogin || apiUser.lastLoginTime || '',
    // 如果没有 status 字段，默认为正常状态
    status: apiUser.status !== undefined ? EMPLOYEE_STATUS_MAP[apiUser.status] || 'active' : 'active',
    statusCode: apiUser.status || 1,
    disabled: apiUser.status !== undefined ? apiUser.status !== 1 : false,

    // 微信相关字段
    openId: apiUser.openId || '',
    unionId: apiUser.unionId || '',
    nickname: apiUser.nickname || '',

    // 用户统计数据 - 如果接口没有返回统计数据，使用默认值
    watchedVideos: apiUser.watchedVideos || 0,
    totalViews: apiUser.totalViews || {
      today: apiUser.todayViews || 0,
      yesterday: apiUser.yesterdayViews || 0,
      thisWeek: apiUser.thisWeekViews || 0,
      thisMonth: apiUser.thisMonthViews || 0
    },
    totalQuizzes: apiUser.totalQuizzes || {
      today: apiUser.todayQuizzes || 0,
      yesterday: apiUser.yesterdayQuizzes || 0,
      thisWeek: apiUser.thisWeekQuizzes || 0,
      thisMonth: apiUser.thisMonthQuizzes || 0
    },
    totalRewards: apiUser.totalRewards || {
      today: apiUser.todayRewards || 0,
      yesterday: apiUser.yesterdayRewards || 0,
      thisWeek: apiUser.thisWeekRewards || 0,
      thisMonth: apiUser.thisMonthRewards || 0
    }
  };
}

/**
 * 转换员工创建数据（页面数据转API数据）
 * @param {Object} formData - 表单数据
 * @param {string} role - 角色类型 ('employee' | 'manager')
 * @returns {Object} API需要的创建数据格式
 */
export function formatCreateEmployeeData (formData, role = 'employee') {
  return {
    employeeId: formData.userName || formData.username,
    name: formData.userName || formData.username,
    phone: formData.phone || '',
    email: formData.email || '',
    department: formData.department || '默认部门',
    position: formData.position || (role === 'manager' ? '管理' : '员工'),
    level: REVERSE_LEVEL_MAP[role] || 1,
    managerId: formData.managerId || null,
    status: 1, // 默认在职状态
    remark: formData.remark || `通过管理后台创建的${role === 'manager' ? '管理' : '员工'}`
  };
}

/**
 * 转换员工更新数据（页面数据转API数据）
 * @param {Object} formData - 表单数据
 * @returns {Object} API需要的更新数据格式
 */
export function formatUpdateEmployeeData (formData) {
  const updateData = {};

  if (formData.name !== undefined) updateData.name = formData.name;
  if (formData.phone !== undefined) updateData.phone = formData.phone;
  if (formData.email !== undefined) updateData.email = formData.email;
  if (formData.department !== undefined) updateData.department = formData.department;
  if (formData.position !== undefined) updateData.position = formData.position;
  if (formData.managerId !== undefined) updateData.managerId = formData.managerId;
  if (formData.status !== undefined) updateData.status = REVERSE_STATUS_MAP[formData.status] || formData.status;
  if (formData.remark !== undefined) updateData.remark = formData.remark;

  return updateData;
}

/**
 * 批量格式化SysUser数据
 * @param {Array} apiSysUsers - API返回的SysUser数据数组
 * @returns {Array} 格式化后的用户数据数组
 */
export function formatSysUserList (apiSysUsers) {
  if (!Array.isArray(apiSysUsers)) return [];

  return apiSysUsers.map(user => formatSysUserData(user)).filter(Boolean);
}

/**
 * 批量格式化员工数据
 * @param {Array} apiEmployees - API返回的员工数据数组
 * @returns {Array} 格式化后的员工数据数组
 */
export function formatEmployeeList (apiEmployees) {
  if (!Array.isArray(apiEmployees)) return [];

  return apiEmployees.map(employee => formatEmployeeData(employee)).filter(Boolean);
}

/**
 * 批量格式化管理数据
 * @param {Array} apiManagers - API返回的管理数据数组
 * @returns {Array} 格式化后的管理数据数组
 */
export function formatManagerList (apiManagers) {
  if (!Array.isArray(apiManagers)) return [];

  return apiManagers.map(manager => formatManagerData(manager)).filter(Boolean);
}

/**
 * 批量格式化用户数据
 * @param {Array} apiUsers - API返回的用户数据数组
 * @returns {Array} 格式化后的用户数据数组
 */
export function formatUserList (apiUsers) {
  if (!Array.isArray(apiUsers)) return [];

  return apiUsers.map(user => formatUserData(user)).filter(Boolean);
}

/**
 * 验证员工数据完整性
 * @param {Object} employeeData - 员工数据
 * @returns {Object} 验证结果
 */
export function validateEmployeeData (employeeData) {
  const errors = [];

  if (!employeeData.id && !employeeData.employeeId) {
    errors.push('缺少员工ID');
  }

  if (!employeeData.username && !employeeData.name) {
    errors.push('缺少员工姓名');
  }

  if (employeeData.level && ![1, 2, 3].includes(employeeData.level)) {
    errors.push('员工级别无效');
  }

  if (employeeData.status && ![1, 2, 3].includes(employeeData.status)) {
    errors.push('员工状态无效');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 获取状态显示文本
 * @param {number} statusCode - 状态码
 * @returns {string} 状态显示文本
 */
export function getStatusText (statusCode) {
  const statusTextMap = {
    1: '在职',
    2: '离职',
    3: '停职'
  };

  return statusTextMap[statusCode] || '未知';
}

/**
 * 获取角色显示文本
 * @param {number} level - 级别码
 * @returns {string} 角色显示文本
 */
export function getRoleText (level) {
  const roleTextMap = {
    1: '员工',
    2: '管理',
    3: '管理员'
  };

  return roleTextMap[level] || '未知';
}

/**
 * 处理时间格式
 * @param {string} timeString - 时间字符串
 * @returns {string} 格式化后的时间
 */
export function formatTime (timeString) {
  if (!timeString) return '';

  try {
    const date = new Date(timeString);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  } catch (error) {
    console.error('时间格式化失败:', error);
    return timeString;
  }
}
