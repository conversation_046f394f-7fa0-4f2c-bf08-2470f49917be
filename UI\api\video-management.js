/**
 * 视频管理相关API
 * 处理视频的创建、更新、删除、上传、统计等功能
 */

import request from '../utils/request.js'

/**
 * 视频题目数据
 * @typedef {Object} VideoQuestionDto
 * @property {string} questionText - 题目文本
 * @property {number} orderNum - 题目序号
 * @property {Array} options - 选项列表
 */

/**
 * 视频创建请求参数
 * @typedef {Object} VideoVideoCreateDto
 * @property {string} title - 视频标题
 * @property {string} description - 视频描述
 * @property {string} coverUrl - 封面URL
 * @property {string} videoUrl - 视频URL
 * @property {number} duration - 视频时长
 * @property {number} rewardAmount - 奖励金额
 * @property {number} viewCount - 观看次数
 * @property {Array<VideoQuestionDto>} questions - 题目列表
 * @property {number} creatorId - 创建者ID
 */

/**
 * 视频更新请求参数
 * @typedef {Object} VideoVideoUpdateDto
 * @property {number} id - 视频ID
 * @property {string} title - 视频标题
 * @property {string} description - 视频描述
 * @property {string} coverUrl - 封面URL
 * @property {string} videoUrl - 视频URL
 * @property {number} duration - 视频时长
 * @property {number} rewardAmount - 奖励金额
 * @property {number} viewCount - 观看次数
 * @property {Array<VideoQuestionDto>} questions - 题目列表
 */

/**
 * 视频状态更新请求参数
 * @typedef {Object} VideoVideoStatusUpdateDto
 * @property {number} id - 视频ID
 * @property {number} status - 状态
 */

/**
 * 视频查询参数
 * @typedef {Object} VideoVideoQueryDto
 * @property {string} [Title] - 视频标题
 * @property {number} [CreatorId] - 创建者ID
 * @property {number} [Status] - 状态
 * @property {string} [StartTime] - 开始时间
 * @property {string} [EndTime] - 结束时间
 * @property {number} [PageIndex] - 页码
 * @property {number} [PageSize] - 每页大小
 * @property {string} [OrderField] - 排序字段
 * @property {boolean} [IsAsc] - 是否升序
 */

/**
 * 视频响应数据
 * @typedef {Object} VideoVideoResponseDto
 * @property {number} id - 视频ID
 * @property {string} title - 视频标题
 * @property {string} description - 视频描述
 * @property {string} coverUrl - 封面URL
 * @property {string} videoUrl - 视频URL
 * @property {number} duration - 视频时长
 * @property {number} creatorId - 创建者ID
 * @property {number} rewardAmount - 奖励金额
 * @property {number} viewCount - 观看次数
 * @property {Array<VideoQuestionDto>} questions - 题目列表
 * @property {number} status - 状态
 * @property {string} createTime - 创建时间
 * @property {string} creatorName - 创建者姓名
 */

/**
 * 视频上传响应数据
 * @typedef {Object} VideoVideoUploadResponseDto
 * @property {string} originalVideoUrl - 原始视频URL
 * @property {string} compressedVideoUrl - 压缩视频URL
 * @property {string} thumbnailUrl - 缩略图URL
 * @property {number} duration - 视频时长
 * @property {number} originalFileSize - 原始文件大小
 * @property {number} compressedFileSize - 压缩文件大小
 * @property {string} videoFormat - 视频格式
 * @property {string} resolution - 分辨率
 * @property {boolean} compressionInProgress - 压缩进行中
 * @property {number} compressionProgress - 压缩进度
 * @property {string} fileId - 文件ID
 */

/**
 * 视频统计数据
 * @typedef {Object} VideoVideoStatisticsDto
 * @property {number} totalViewCount - 总观看次数
 * @property {number} completeViewCount - 完播次数
 * @property {number} completeRate - 完播率
 * @property {number} averageViewDuration - 平均观看时长
 * @property {number} todayViewCount - 今日观看次数
 * @property {number} weekViewCount - 本周观看次数
 * @property {number} monthViewCount - 本月观看次数
 * @property {number} uniqueViewerCount - 独立观看用户数
 * @property {number} repeatViewCount - 重复观看次数
 * @property {number} shareCount - 分享次数
 * @property {number} totalCount - 总视频数
 * @property {number} publishedCount - 已发布数
 * @property {number} pendingCount - 待审核数
 * @property {number} offlineCount - 已下线数
 * @property {number} totalDuration - 总时长
 * @property {string} lastViewTime - 最后观看时间
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 创建视频
 * @param {VideoVideoCreateDto} data - 视频创建数据
 * @returns {Promise<ApiResult<number>>} 创建结果，返回视频ID
 */
export function createVideo (data) {
  return request.post('/Video', data)
}

/**
 * 分页查询视频列表
 * @param {VideoVideoQueryDto} params - 查询参数
 * @returns {Promise<ApiResult>} 分页查询结果
 */
export function queryVideos (params) {
  return request.get('/Video', params)
}

/**
 * 上传视频文件
 * @param {FormData} formData - 包含视频文件的表单数据
 * @returns {Promise<ApiResult<VideoVideoUploadResponseDto>>} 上传结果
 */
export function uploadVideo (formData) {
  return request.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 简单视频上传（兼容性接口）
 * @param {FormData} formData - 包含视频文件的表单数据
 * @returns {Promise<ApiResult<string>>} 上传结果，返回视频URL
 */
export function uploadVideoSimple (formData) {
  return request.post('/upload-simple', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 更新视频信息
 * @param {VideoVideoUpdateDto} data - 视频更新数据
 * @returns {Promise<ApiResult<boolean>>} 更新结果
 */
export function updateVideo (data) {
  return request.post('/update', data)
}

/**
 * 删除视频
 * @param {number} videoId - 视频ID
 * @returns {Promise<ApiResult<boolean>>} 删除结果
 */
export function deleteVideo (videoId) {
  return request.delete(`/Delete/${videoId}`)
}

/**
 * 获取视频详情
 * @param {number} videoId - 视频ID
 * @returns {Promise<ApiResult<VideoVideoResponseDto>>} 视频详情
 */
export function getVideoDetail (videoId) {
  return request.get(`/Get/${videoId}`)
}

/**
 * 获取创建者的视频列表
 * @param {number} creatorId - 创建者ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.status] - 状态筛选
 * @returns {Promise<ApiResult<Array<VideoVideoResponseDto>>>} 视频列表
 */
export function getVideosByCreator (creatorId, options = {}) {
  return request.get(`/creator/${creatorId}`, options)
}

/**
 * 获取我的视频列表
 * @param {Object} [options] - 查询选项
 * @param {number} [options.status] - 状态筛选
 * @returns {Promise<ApiResult<Array<VideoVideoResponseDto>>>} 我的视频列表
 */
export function getMyVideos (options = {}) {
  return request.get('/my-videos', options)
}

/**
 * 更新视频状态
 * @param {number} videoId - 视频ID
 * @param {VideoVideoStatusUpdateDto} data - 状态更新数据
 * @returns {Promise<ApiResult<boolean>>} 更新结果
 */
export function updateVideoStatus (videoId, data) {
  return request.post(`/${videoId}/status`, data)
}

/**
 * 搜索视频
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @param {number} [params.pageIndex=1] - 页码
 * @param {number} [params.pageSize=20] - 每页大小
 * @returns {Promise<ApiResult>} 搜索结果
 */
export function searchVideos (params) {
  return request.get('/search', params)
}

/**
 * 获取视频统计信息
 * @param {number} videoId - 视频ID
 * @returns {Promise<ApiResult<VideoVideoStatisticsDto>>} 视频统计信息
 */
export function getVideoStatistics (videoId) {
  return request.get(`/${videoId}/statistics`)
}

/**
 * 获取视频状态选项
 * @returns {Array<{value: number, label: string}>} 状态选项
 */
export function getVideoStatusOptions () {
  return [
    { value: 0, label: '草稿' },
    { value: 1, label: '已发布' },
    { value: 2, label: '已下线' },
    { value: 3, label: '审核中' }
  ]
}

/**
 * 获取排序字段选项
 * @returns {Array<{value: string, label: string}>} 排序字段选项
 */
export function getVideoSortFieldOptions () {
  return [
    { value: 'createTime', label: '创建时间' },
    { value: 'title', label: '视频标题' },
    { value: 'viewCount', label: '观看次数' },
    { value: 'duration', label: '视频时长' },
    { value: 'rewardAmount', label: '奖励金额' }
  ]
}

// 默认导出所有视频管理相关API
export default {
  createVideo,
  queryVideos,
  uploadVideo,
  uploadVideoSimple,
  updateVideo,
  deleteVideo,
  getVideoDetail,
  getVideosByCreator,
  getMyVideos,
  updateVideoStatus,
  searchVideos,
  getVideoStatistics,
  getVideoStatusOptions,
  getVideoSortFieldOptions
}
