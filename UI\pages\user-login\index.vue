<template>
  <view class="login-container">
    <view class="login-content">
      <!-- Header section -->
      <view class="header-section">
        <text class="app-title">用户登录</text>
        <text class="app-subtitle">登录您的账号继续观看</text>
      </view>

      <!-- Login form -->
      <view class="form-container">
        <!-- Nickname input -->
        <view class="input-group">
          <text class="input-label">昵称</text>
          <u-input v-model="loginForm.nickname" placeholder="请输入您的昵称" border="surround" clearable
            :error="!!errors.nickname" @blur="validateNickname" @input="clearError('nickname')" class="login-input" />
          <text v-if="errors.nickname" class="error-message">{{ errors.nickname }}</text>
        </view>

        <!-- Mobile input (optional) -->
        <view class="input-group">
          <text class="input-label">手机号（可选）</text>
          <u-input v-model="loginForm.mobile" placeholder="请输入手机号" border="surround" clearable :error="!!errors.mobile"
            @blur="validateMobile" @input="clearError('mobile')" class="login-input" />
          <text v-if="errors.mobile" class="error-message">{{ errors.mobile }}</text>
        </view>

        <!-- Login button -->
        <u-button type="primary" :text="isLoading ? '登录中...' : '登录'" :loading="isLoading"
          :disabled="!canSubmit || isLoading" @click="handleLogin" class="login-btn" />

        <!-- Register link -->
        <view class="register-link-container">
          <text class="register-link-text">还没有账号？</text>
          <text class="register-link" @click="goToRegister">立即注册</text>
        </view>

        <!-- WeChat login section -->
        <view class="divider">
          <text class="divider-text">或</text>
        </view>

        <u-button type="success" text="微信登录" @click="wechatLogin" class="wechat-btn" :disabled="isLoading"
          icon="weixin" />
      </view>

      <!-- Footer -->
      <view class="footer">
        <text class="footer-text">© 2024 视频分享系统</text>
        <text class="footer-version">Version 1.0.0</text>
      </view>
    </view>

    <!-- Toast -->
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import { simpleLogin, wechatLogin as wechatLoginApi } from '@/api/video-user.js'

export default {
  data () {
    return {
      loginForm: {
        nickname: '',
        mobile: ''
      },
      isLoading: false,
      errors: {
        nickname: '',
        mobile: ''
      }
    }
  },
  computed: {
    canSubmit () {
      return this.loginForm.nickname.trim() &&
        !this.isLoading
    }
  },
  onLoad (options) {
    // 检查是否已经登录
    this.checkExistingLogin()

    // 如果有返回页面参数，保存起来
    if (options.returnUrl) {
      this.returnUrl = decodeURIComponent(options.returnUrl)
    }
  },
  methods: {
    // 验证昵称
    validateNickname () {
      const nickname = this.loginForm.nickname.trim()
      if (!nickname) {
        this.errors.nickname = '昵称不能为空'
        return false
      }
      this.errors.nickname = ''
      return true
    },

    // 验证手机号
    validateMobile () {
      const mobile = this.loginForm.mobile.trim()
      if (mobile && !/^1[3-9]\d{9}$/.test(mobile)) {
        this.errors.mobile = '请输入正确的手机号'
        return false
      }
      this.errors.mobile = ''
      return true
    },

    // 清除错误信息
    clearError (field) {
      this.errors[field] = ''
    },

    // 验证整个表单
    validateForm () {
      const nicknameValid = this.validateNickname()
      const mobileValid = this.validateMobile()
      return nicknameValid && mobileValid
    },

    // 处理登录
    async handleLogin () {
      if (!this.validateForm()) {
        this.showToastMessage('请检查输入信息', 'error')
        return
      }

      if (!this.canSubmit || this.isLoading) return

      this.isLoading = true

      try {
        const { nickname, mobile } = this.loginForm
        const response = await simpleLogin({
          nickname: nickname.trim(),
          mobile: mobile.trim() || undefined
        })

        if (response.success && response.data) {
          // 保存用户信息到本地存储
          uni.setStorageSync('userInfo', response.data.userInfo)
          uni.setStorageSync('token', response.data.token)

          this.showToastMessage('登录成功！', 'success')
          await this.delay(1500)

          // 跳转到返回页面或主页
          this.redirectAfterLogin()
        } else {
          this.showToastMessage(response.msg || '登录失败', 'error')
        }
      } catch (error) {
        console.error('Login error:', error)
        this.showToastMessage('登录失败，请重试', 'error')
      } finally {
        this.isLoading = false
      }
    },

    // 微信登录
    async wechatLogin () {
      try {
        this.isLoading = true

        // 检查是否在微信小程序环境
        // #ifdef MP-WEIXIN
        const loginRes = await uni.login({
          provider: 'weixin'
        })

        if (!loginRes.code) {
          throw new Error('获取微信授权码失败')
        }

        // 获取URL参数中的员工ID和批次ID
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        const options = currentPage.options || {}

        const loginData = {
          code: loginRes.code,
          employeeId: options.employeeId || null,
          batchId: options.batchId ? parseInt(options.batchId) : null
        }

        // 调用后端微信登录接口
        const result = await wechatLoginApi(loginData)

        if (result.success && result.data) {
          // 保存用户信息和token
          uni.setStorageSync('userToken', result.data.token)
          uni.setStorageSync('userInfo', result.data.userInfo)

          this.showToastMessage('微信登录成功！', 'success')

          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            // 如果有批次ID，跳转到视频页面
            if (options.batchId && options.videoId) {
              uni.redirectTo({
                url: `/pages/video/index?videoId=${options.videoId}&batchId=${options.batchId}&sharerId=${options.sharerId || ''}`
              })
            } else {
              // 否则跳转到首页或用户中心
              uni.switchTab({
                url: '/pages/index/index'
              })
            }
          }, 1500)
        } else {
          throw new Error(result.message || '微信登录失败')
        }
        // #endif

        // #ifndef MP-WEIXIN
        this.showToastMessage('微信登录仅支持在微信小程序中使用', 'warning')
        // #endif

      } catch (error) {
        console.error('微信登录失败:', error)
        this.showToastMessage(error.message || '微信登录失败，请重试', 'error')
      } finally {
        this.isLoading = false
      }
    },

    // 跳转到注册页面
    goToRegister () {
      uni.navigateTo({
        url: '/pages/register/index'
      })
    },

    // 检查现有登录状态
    checkExistingLogin () {
      const userInfo = uni.getStorageSync('userInfo')
      const token = uni.getStorageSync('token')

      if (userInfo && token) {
        // 已经登录，直接跳转
        this.redirectAfterLogin()
      }
    },

    // 登录后跳转
    redirectAfterLogin () {
      if (this.returnUrl) {
        uni.redirectTo({
          url: this.returnUrl
        })
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    },

    showToastMessage (message, type = 'success') {
      this.$refs.uToast.show({
        message: message,
        type: type,
        duration: 3000
      })
    },

    delay (ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

.login-content {
  width: 100%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.form-container {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 48rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.login-input {
  width: 100%;
}

.error-message {
  display: block;
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 8rpx;
  line-height: 1.4;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  margin-top: 20rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.register-link-container {
  text-align: center;
  margin-top: 32rpx;
}

.register-link-text {
  font-size: 28rpx;
  color: #666;
}

.register-link {
  font-size: 28rpx;
  color: #1976D2;
  margin-left: 8rpx;
  text-decoration: underline;
}

.divider {
  text-align: center;
  margin: 40rpx 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: #e0e0e0;
}

.divider-text {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  position: relative;
  z-index: 1;
}

.wechat-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.footer {
  text-align: center;
  margin-top: 60rpx;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8rpx;
}

.footer-version {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
}
</style>
