/**
 * 视频观看记录相关API
 *
 * @deprecated 此API文件已废弃，请使用 user-batch-record.js 中的新API
 * 新API提供更完整的功能和更好的性能
 *
 * 迁移指南：
 * - createViewRecord -> createOrGetRecord
 * - updateViewProgress -> updateWatchProgress
 * - getBatchViewRecords -> getBatchRecords
 * - getViewStatistics -> getBatchStatistics
 */

import request from '../utils/request.js'

/**
 * 观看记录创建请求参数
 * @typedef {Object} VideoViewRecordCreateDto
 * @property {number} batchId - 批次ID
 * @property {number} userId - 用户ID
 * @property {string} promotionLink - 推广链接
 */

/**
 * 观看进度更新请求参数
 * @typedef {Object} VideoViewProgressUpdateDto
 * @property {number} batchId - 批次ID
 * @property {number} viewDuration - 观看时长
 * @property {number} watchProgress - 观看进度
 * @property {boolean} isCompleted - 是否完成
 */

/**
 * 观看记录查询参数
 * @typedef {Object} VideoViewRecordQueryDto
 * @property {number} [UserId] - 用户ID
 * @property {number} [BatchId] - 批次ID
 * @property {boolean} [IsCompleted] - 是否完成
 * @property {string} [StartTime] - 开始时间
 * @property {string} [EndTime] - 结束时间
 * @property {number} [PageIndex] - 页码
 * @property {number} [PageSize] - 每页大小
 * @property {string} [OrderField] - 排序字段
 * @property {boolean} [IsAsc] - 是否升序
 */

/**
 * 观看记录响应数据
 * @typedef {Object} VideoViewRecordResponseDto
 * @property {number} id - 记录ID
 * @property {number} userId - 用户ID
 * @property {string} userNickname - 用户昵称
 * @property {string} userRealName - 用户真实姓名
 * @property {number} batchId - 批次ID
 * @property {string} batchName - 批次名称
 * @property {number} videoId - 视频ID
 * @property {string} videoTitle - 视频标题
 * @property {number} viewDuration - 观看时长
 * @property {string} promotionLink - 推广链接
 * @property {string} startTime - 开始时间
 * @property {string} endTime - 结束时间
 * @property {number} watchProgress - 观看进度
 * @property {boolean} isCompleted - 是否完成
 * @property {string} createTime - 创建时间
 */

/**
 * 每日观看统计数据
 * @typedef {Object} VideoDailyViewStatisticsDto
 * @property {string} date - 日期
 * @property {number} viewCount - 观看次数
 * @property {number} completeViewCount - 完播次数
 * @property {number} completeRate - 完播率
 * @property {number} viewerCount - 观看人数
 * @property {number} newViewerCount - 新观看人数
 * @property {number} averageViewDuration - 平均观看时长
 * @property {number} totalViewDuration - 总观看时长
 * @property {number} totalViews - 总观看次数
 * @property {number} completedViews - 完播次数
 * @property {number} uniqueUsers - 独立用户数
 * @property {number} averageProgress - 平均进度
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

export function createViewRecord (data) {
  return request.post('/ViewRecord', data)
}

export function queryViewRecords (params) {
  return request.get('/ViewRecord', params)
}

export function updateViewProgress (data) {
  return request.post('/ViewRecord/update-progress', data)
}

export function completeView (recordId) {
  return request.post(`/ViewRecord/${recordId}/complete`)
}

export function getViewRecordDetail (recordId) {
  return request.get(`/ViewRecord/${recordId}`)
}

export function getUserViewRecords (userId, options = {}) {
  return request.get(`/ViewRecord/user/${userId}`, options)
}

export function getMyViewRecords (options = {}) {
  return request.get('/ViewRecord/my-records', options)
}

export function getVideoViewRecords (videoId, options = {}) {
  return request.get(`/ViewRecord/video/${videoId}`, options)
}

export function getBatchViewRecords (batchId, options = {}) {
  return request.get(`/ViewRecord/batch/${batchId}`, options)
}

export function getUserViewProgress (userId, videoId) {
  return request.get(`/ViewRecord/progress/${userId}/${videoId}`)
}

export function getMyViewProgress (videoId) {
  return request.get(`/ViewRecord/my-progress/${videoId}`)
}

export function getViewStatistics (params = {}) {
  return request.get('/ViewRecord/statistics', params)
}

export function getViewTrend (params = {}) {
  return request.get('/ViewRecord/trend', params)
}

export function startWatching (batchId, userId, promotionLink = '') {
  return createViewRecord({
    batchId,
    userId,
    promotionLink
  })
}

export function updateWatchDuration (batchId, viewDuration, watchProgress = 0) {
  return updateViewProgress({
    batchId,
    viewDuration,
    watchProgress,
    isCompleted: watchProgress >= 1.0
  })
}

export function getViewRecordSortFieldOptions () {
  return [
    { value: 'createTime', label: '创建时间' },
    { value: 'startTime', label: '开始时间' },
    { value: 'endTime', label: '结束时间' },
    { value: 'viewDuration', label: '观看时长' },
    { value: 'watchProgress', label: '观看进度' }
  ]
}

export function formatDuration (duration) {
  if (!duration || duration < 0) return '0秒'
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  const seconds = duration % 60
  if (hours > 0) {
    return `${hours}小时${minutes}分${seconds}秒`
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  } else {
    return `${seconds}秒`
  }
}

export default {
  createViewRecord,
  queryViewRecords,
  updateViewProgress,
  completeView,
  getViewRecordDetail,
  getUserViewRecords,
  getMyViewRecords,
  getVideoViewRecords,
  getBatchViewRecords,
  getUserViewProgress,
  getMyViewProgress,
  getViewStatistics,
  getViewTrend,
  startWatching,
  updateWatchDuration,
  getViewRecordSortFieldOptions,
  formatDuration
}
