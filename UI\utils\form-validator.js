/**
 * 表单验证工具类
 * 提供通用的表单验证方法
 */

/**
 * 视频表单验证器
 */
export class VideoFormValidator {
  /**
   * 验证视频基本信息
   * @param {object} videoInfo - 视频信息
   * @param {boolean} isEditMode - 是否为编辑模式
   * @returns {object} 验证结果 {valid: boolean, message: string}
   */
  validateBasicInfo(videoInfo, isEditMode = false) {
    // 非编辑模式需要检查视频文件
    if (!isEditMode && !videoInfo.path) {
      return { valid: false, message: '请选择视频文件' };
    }

    // 检查视频标题
    if (!videoInfo.title || !videoInfo.title.trim()) {
      return { valid: false, message: '请输入视频标题' };
    }

    if (videoInfo.title.trim().length < 2) {
      return { valid: false, message: '视频标题至少需要2个字符' };
    }

    if (videoInfo.title.trim().length > 100) {
      return { valid: false, message: '视频标题不能超过100个字符' };
    }

    // 检查封面（必填）
    if (!videoInfo.thumbnail) {
      return { valid: false, message: '请选择封面图片' };
    }

    // 检查描述长度
    if (videoInfo.description && videoInfo.description.length > 500) {
      return { valid: false, message: '视频描述不能超过500个字符' };
    }

    return { valid: true, message: '' };
  }

  /**
   * 验证问题信息
   * @param {Array} questions - 问题数组
   * @returns {object} 验证结果 {valid: boolean, message: string}
   */
  validateQuestions(questions) {
    if (!questions || questions.length === 0) {
      return { valid: false, message: '至少需要添加一个问题' };
    }

    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];

      // 验证问题内容
      if (!question.content || !question.content.trim()) {
        return { valid: false, message: `问题${i + 1}：请输入问题内容` };
      }

      if (question.content.trim().length < 5) {
        return { valid: false, message: `问题${i + 1}：问题内容至少需要5个字符` };
      }

      if (question.content.trim().length > 200) {
        return { valid: false, message: `问题${i + 1}：问题内容不能超过200个字符` };
      }

      // 验证选项
      if (!question.options || question.options.length < 2) {
        return { valid: false, message: `问题${i + 1}：至少需要2个选项` };
      }

      if (question.options.length > 6) {
        return { valid: false, message: `问题${i + 1}：选项不能超过6个` };
      }

      // 验证每个选项
      for (let j = 0; j < question.options.length; j++) {
        const option = question.options[j];
        if (!option.text || !option.text.trim()) {
          return { valid: false, message: `问题${i + 1}选项${j + 1}：请输入选项内容` };
        }

        if (option.text.trim().length > 100) {
          return { valid: false, message: `问题${i + 1}选项${j + 1}：选项内容不能超过100个字符` };
        }
      }

      // 验证正确答案
      if (question.correctAnswer === undefined || 
          question.correctAnswer === null || 
          question.correctAnswer >= question.options.length ||
          question.correctAnswer < 0) {
        return { valid: false, message: `问题${i + 1}：请选择正确答案` };
      }
    }

    return { valid: true, message: '' };
  }

  /**
   * 验证奖励金额
   * @param {string|number} totalReward - 奖励金额
   * @returns {object} 验证结果 {valid: boolean, message: string}
   */
  validateReward(totalReward) {
    if (!totalReward) {
      return { valid: false, message: '请输入红包金额' };
    }

    const amount = parseFloat(totalReward);
    if (isNaN(amount) || amount <= 0) {
      return { valid: false, message: '请输入有效的红包金额' };
    }

    if (amount < 0.01) {
      return { valid: false, message: '红包金额不能少于0.01元' };
    }

    if (amount > 999999.99) {
      return { valid: false, message: '红包金额不能超过999999.99元' };
    }

    return { valid: true, message: '' };
  }

  /**
   * 验证完整的视频表单
   * @param {object} videoInfo - 视频信息
   * @param {boolean} isEditMode - 是否为编辑模式
   * @returns {object} 验证结果 {valid: boolean, message: string}
   */
  validateVideoForm(videoInfo, isEditMode = false) {
    // 验证基本信息
    const basicResult = this.validateBasicInfo(videoInfo, isEditMode);
    if (!basicResult.valid) {
      return basicResult;
    }

    // 验证问题
    const questionsResult = this.validateQuestions(videoInfo.questions);
    if (!questionsResult.valid) {
      return questionsResult;
    }

    // 验证奖励金额
    const rewardResult = this.validateReward(videoInfo.totalReward);
    if (!rewardResult.valid) {
      return rewardResult;
    }

    return { valid: true, message: '验证通过' };
  }
}

/**
 * 批次表单验证器
 */
export class BatchFormValidator {
  /**
   * 验证批次基本信息
   * @param {object} batchInfo - 批次信息
   * @returns {object} 验证结果 {valid: boolean, message: string}
   */
  validateBasicInfo(batchInfo) {
    // 检查标题
    if (!batchInfo.batchTitle || !batchInfo.batchTitle.trim()) {
      return { valid: false, message: '请输入批次标题' };
    }

    if (batchInfo.batchTitle.trim().length < 2) {
      return { valid: false, message: '批次标题至少需要2个字符' };
    }

    if (batchInfo.batchTitle.trim().length > 100) {
      return { valid: false, message: '批次标题不能超过100个字符' };
    }

    // 检查描述长度
    if (batchInfo.batchDescription && batchInfo.batchDescription.length > 500) {
      return { valid: false, message: '批次描述不能超过500个字符' };
    }

    return { valid: true, message: '' };
  }

  /**
   * 验证时间设置
   * @param {object} timeInfo - 时间信息
   * @returns {object} 验证结果 {valid: boolean, message: string}
   */
  validateTimeSettings(timeInfo) {
    const { startDate, startTime, endDate, endTime } = timeInfo;

    // 检查必填字段
    if (!startDate || !startTime || !endDate || !endTime) {
      return { valid: false, message: '请完整设置开始和结束时间' };
    }

    // 验证时间逻辑
    const startDateTime = new Date(`${startDate} ${startTime}`);
    const endDateTime = new Date(`${endDate} ${endTime}`);

    if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
      return { valid: false, message: '时间格式不正确' };
    }

    if (startDateTime >= endDateTime) {
      return { valid: false, message: '开始时间必须早于结束时间' };
    }

    // 检查开始时间不能早于当前时间
    const now = new Date();
    if (startDateTime < now) {
      return { valid: false, message: '开始时间不能早于当前时间' };
    }

    return { valid: true, message: '' };
  }

  /**
   * 验证完整的批次表单
   * @param {object} batchInfo - 批次信息
   * @returns {object} 验证结果 {valid: boolean, message: string}
   */
  validateBatchForm(batchInfo) {
    // 验证基本信息
    const basicResult = this.validateBasicInfo(batchInfo);
    if (!basicResult.valid) {
      return basicResult;
    }

    // 验证时间设置
    const timeResult = this.validateTimeSettings(batchInfo);
    if (!timeResult.valid) {
      return timeResult;
    }

    return { valid: true, message: '验证通过' };
  }
}

// 创建单例实例
export const videoFormValidator = new VideoFormValidator();
export const batchFormValidator = new BatchFormValidator();

// 默认导出
export default {
  VideoFormValidator,
  BatchFormValidator,
  videoFormValidator,
  batchFormValidator
};
