/* 字体文件暂未提供，使用系统默认字体 */
/* @font-face {
  font-family: 'iconfont';
  src: url('/assets/fonts/iconfont.ttf') format('truetype');
} */

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 导航和基础图标 */
.icon-arrow-left:before {
  content: "\e6db";
}

.icon-arrow:before {
  content: "\e6dc";
}

.icon-user:before {
  content: "\e6dd";
}

.icon-home:before {
  content: "\e6de";
}

.icon-data:before {
  content: "\e6df";
}

/* 功能图标 */
.icon-correct:before {
  content: "\e6e0";
}

.icon-wrong:before {
  content: "\e6e1";
}

.icon-reward:before {
  content: "\e6e2";
}

.icon-video:before {
  content: "\e6e3";
}

.icon-quiz:before {
  content: "\e6e4";
}

.icon-edit:before {
  content: "\e6e5";
}

.icon-delete:before {
  content: "\e6e6";
}

.icon-upload:before {
  content: "\e6e7";
}

.icon-search:before {
  content: "\e6e8";
}

.icon-play:before {
  content: "\e6e9";
}

.icon-pause:before {
  content: "\e6ea";
}

.icon-more:before {
  content: "\e6eb";
}

.icon-close:before {
  content: "\e6ec";
}

.icon-info:before {
  content: "\e6ed";
}

.icon-chart:before {
  content: "\e6ee";
}

.icon-link:before {
  content: "\e6ef";
}

.icon-time:before {
  content: "\e6f0";
}

.icon-calendar:before {
  content: "\e6f1";
}

.icon-file:before {
  content: "\e6f2";
}

.icon-money:before {
  content: "\e6f3";
}

.icon-question:before {
  content: "\e6f4";
}

.icon-video-off:before {
  content: "\e6f5";
}

.icon-start:before {
  content: "\e6f6";
}

.icon-end:before {
  content: "\e6f7";
}

.icon-add:before {
  content: "\e6f8";
}