<template>
  <view class="dplayer-container">
    <view :id="playerId" class="dplayer-wrapper"></view>
  </view>
</template>

<script>
export default {
  name: 'DPlayerVideo',
  props: {
    src: {
      type: String,
      required: true
    },
    poster: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    disableSeek: {
      type: Boolean,
      default: false
    },
    disableProgressDrag: {
      type: <PERSON>olean,
      default: false
    },
    showRate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      player: null,
      playerId: 'dplayer-' + Date.now(),
      currentTime: 0,
      duration: 0,
      maxWatchedTime: 0,
      isFullscreen: false
    }
  },
  mounted() {
    this.initPlayer()
  },
  beforeUnmount() {
    if (this.player) {
      this.player.destroy()
    }
  },
  methods: {
    async initPlayer() {
      // 动态加载 DPlayer
      await this.loadDPlayer()
      
      const options = {
        container: document.getElementById(this.playerId),
        video: {
          url: this.src,
          pic: this.poster,
          type: 'auto'
        },
        autoplay: this.autoplay,
        theme: '#39BFFD',
        loop: false,
        lang: 'zh-cn',
        screenshot: false,
        hotkey: false,
        preload: 'metadata',
        volume: 1,
        mutex: true,
        preventClickToggle: this.disableSeek,
        contextmenu: [],
        highlight: []
      }

      // 如果禁用倍速播放，移除设置菜单
      if (!this.showRate) {
        options.setting = false
      }

      this.player = new window.DPlayer(options)

      // 绑定事件
      this.bindEvents()
    },

    async loadDPlayer() {
      return new Promise((resolve, reject) => {
        if (window.DPlayer) {
          resolve()
          return
        }

        // 加载 CSS
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = 'https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.css'
        document.head.appendChild(link)

        // 加载 JS
        const script = document.createElement('script')
        script.src = 'https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.js'
        script.onload = () => resolve()
        script.onerror = () => reject(new Error('Failed to load DPlayer'))
        document.head.appendChild(script)
      })
    },

    bindEvents() {
      if (!this.player) return

      // 播放事件
      this.player.on('play', () => {
        console.log('视频开始播放')
      })

      // 暂停事件
      this.player.on('pause', () => {
        console.log('视频暂停')
      })

      // 时间更新事件
      this.player.on('timeupdate', () => {
        this.currentTime = this.player.video.currentTime
        this.duration = this.player.video.duration || 0

        // 更新最大观看时间
        if (this.currentTime > this.maxWatchedTime) {
          this.maxWatchedTime = this.currentTime
        }

        // 防快进检查
        if (this.disableSeek && this.currentTime > this.maxWatchedTime + 2) {
          this.player.seek(this.maxWatchedTime)
          uni.showToast({
            title: '请完整观看视频',
            icon: 'none'
          })
        }

        // 发送时间更新事件
        this.$emit('timeupdate', {
          current: this.currentTime,
          duration: this.duration
        })
      })

      // 视频结束事件
      this.player.on('ended', () => {
        this.maxWatchedTime = this.duration
        this.$emit('ended')
      })

      // 全屏事件
      this.player.on('fullscreen', () => {
        this.isFullscreen = true
        this.lockOrientation()
        this.$emit('fullscreenchange', true)
      })

      this.player.on('fullscreen_cancel', () => {
        this.isFullscreen = false
        this.unlockOrientation()
        this.$emit('fullscreenchange', false)
      })

      // 加载完成事件
      this.player.on('loadedmetadata', () => {
        this.duration = this.player.video.duration || 0
        console.log('视频元数据加载完成，时长:', this.duration)
      })

      // 禁用进度条拖拽
      if (this.disableProgressDrag) {
        this.player.on('seeking', () => {
          if (this.player.video.currentTime > this.maxWatchedTime + 2) {
            this.player.seek(this.maxWatchedTime)
            uni.showToast({
              title: '请完整观看视频',
              icon: 'none'
            })
          }
        })
      }
    },

    // 强制横屏
    async lockOrientation() {
      if (screen.orientation && screen.orientation.lock) {
        try {
          await screen.orientation.lock('landscape')
        } catch (error) {
          console.log('横屏锁定失败:', error)
        }
      }
    },

    // 解锁屏幕方向
    unlockOrientation() {
      if (screen.orientation && screen.orientation.unlock) {
        try {
          screen.orientation.unlock()
        } catch (error) {
          console.log('屏幕方向解锁失败:', error)
        }
      }
    },

    // 公共方法
    play() {
      if (this.player) {
        this.player.play()
      }
    },

    pause() {
      if (this.player) {
        this.player.pause()
      }
    },

    seek(time) {
      if (this.player && time <= this.maxWatchedTime + 2) {
        this.player.seek(time)
      }
    },

    getCurrentTime() {
      return this.currentTime
    },

    toggleFullscreen() {
      if (this.player) {
        if (this.isFullscreen) {
          this.player.fullScreen.cancel()
        } else {
          this.player.fullScreen.request()
        }
      }
    }
  }
}
</script>

<style scoped>
.dplayer-container {
  width: 100%;
  height: 422rpx;
  position: relative;
}

.dplayer-wrapper {
  width: 100%;
  height: 100%;
}

/* 自定义 DPlayer 样式 */
:deep(.dplayer) {
  border-radius: 8rpx;
  overflow: hidden;
}

/* 隐藏不需要的控件 */
:deep(.dplayer-setting) {
  display: none !important;
}

/* 全屏时的样式调整 */
:deep(.dplayer-full .dplayer-controller) {
  opacity: 1;
}

:deep(.dplayer-full .dplayer-controller .dplayer-icons) {
  opacity: 1;
}
</style>
