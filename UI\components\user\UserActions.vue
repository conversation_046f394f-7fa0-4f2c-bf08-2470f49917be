<template>
    <view class="footer-btns">
        <view class="footer-btn detail-btn" v-if="showDetailBtn" @tap="$emit('view-detail')">
            <text class="iconfont icon-detail"></text>
            <text>查看详情</text>
        </view>

        <view class="footer-btn users-btn" v-if="userInfo.type === 'employee' && showUsersBtn"
            @tap="$emit('view-users')">
            <text class="iconfont icon-users"></text>
            <text>查看用户</text>
        </view>

        <view class="footer-btn employees-btn" v-if="userInfo.type === 'agent' && showEmployeesBtn"
            @tap="$emit('view-employees')">
            <text class="iconfont icon-users"></text>
            <text>查看员工</text>
        </view>

        <view class="footer-btn account-btn" v-if="showAccountBtn" @tap.stop="$emit('account-action')">
            <text class="iconfont icon-account"></text>
            <text>{{ userInfo.disabled ? "启用账号" : "禁用账号" }}</text>
        </view>

        <slot name="buttons"></slot>
    </view>
</template>

<script>
export default {
    name: 'UserActions',
    props: {
        userInfo: {
            type: Object,
            required: true
        },
        showDetailBtn: {
            type: Boolean,
            default: true
        },
        showUsersBtn: {
            type: Boolean,
            default: true
        },
        showEmployeesBtn: {
            type: Boolean,
            default: true
        },
        showAccountBtn: {
            type: Boolean,
            default: false
        }
    }
}
</script>

<style scoped>
.footer-btns {
    display: flex;
    justify-content: flex-end;
    margin-top: 4rpx;
}

.footer-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6rpx 12rpx;
    border-radius: 24rpx;
    margin-left: 8rpx;
    font-size: 20rpx;
}

.footer-btn .iconfont {
    font-size: 20rpx;
    margin-right: 3rpx;
}

.detail-btn {
    background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
    border: 1rpx solid #bae7ff;
    color: #186BFF;
    box-shadow: 0 2rpx 6rpx rgba(24, 107, 255, 0.1);
}

.detail-btn:active {
    background: linear-gradient(135deg, #bae7ff, #e8f4ff);
    transform: scale(0.95);
}

.users-btn {
    background: linear-gradient(135deg, #f6ffed, #f9fff6);
    border: 1rpx solid #b7eb8f;
    color: #52c41a;
    box-shadow: 0 2rpx 6rpx rgba(82, 196, 26, 0.1);
}

.users-btn:active {
    background: linear-gradient(135deg, #b7eb8f, #f6ffed);
    transform: scale(0.95);
}

.employees-btn {
    background: linear-gradient(135deg, #fff7e6, #fffaf0);
    border: 1rpx solid #ffd591;
    color: #fa8c16;
    box-shadow: 0 2rpx 6rpx rgba(250, 140, 22, 0.1);
}

.employees-btn:active {
    background: linear-gradient(135deg, #ffd591, #fff7e6);
    transform: scale(0.95);
}

.account-btn {
    background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
    border: 1rpx solid #bae7ff;
    color: #186BFF;
    box-shadow: 0 2rpx 6rpx rgba(24, 107, 255, 0.1);
}

.account-btn:active {
    background: linear-gradient(135deg, #bae7ff, #e8f4ff);
    transform: scale(0.95);
}
</style>