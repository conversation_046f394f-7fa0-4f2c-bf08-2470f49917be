/**
 * 视频奖励相关API
 * 处理视频奖励的查询、领取等功能
 */

import request from '../utils/request.js'

/**
 * 奖励查询参数
 * @typedef {Object} VideoRewardQueryDto
 * @property {number} [UserId] - 用户ID
 * @property {number} [BatchId] - 批次ID
 * @property {number} [VideoId] - 视频ID
 * @property {number} [Status] - 状态
 * @property {string} [StartTime] - 开始时间
 * @property {string} [EndTime] - 结束时间
 * @property {number} [MinAmount] - 最小金额
 * @property {number} [MaxAmount] - 最大金额
 * @property {string} [Type] - 奖励类型
 * @property {number} [PageIndex] - 页码
 * @property {number} [PageSize] - 每页大小
 * @property {string} [OrderField] - 排序字段
 * @property {boolean} [IsAsc] - 是否升序
 */

/**
 * 奖励响应数据
 * @typedef {Object} VideoRewardResponseDto
 * @property {number} id - 奖励ID
 * @property {number} userId - 用户ID
 * @property {string} userNickname - 用户昵称
 * @property {string} userRealName - 用户真实姓名
 * @property {number} batchId - 批次ID
 * @property {string} batchName - 批次名称
 * @property {number} videoId - 视频ID
 * @property {string} videoTitle - 视频标题
 * @property {number} amount - 奖励金额
 * @property {number} status - 状态
 * @property {string} statusText - 状态文本
 * @property {string} transactionId - 交易ID
 * @property {string} outTradeNo - 外部交易号
 * @property {string} type - 奖励类型
 * @property {string} payTime - 支付时间
 * @property {string} distributeTime - 发放时间
 * @property {string} claimTime - 领取时间
 * @property {string} failReason - 失败原因
 * @property {number} retryCount - 重试次数
 * @property {string} createTime - 创建时间
 */

/**
 * 分页结果数据
 * @typedef {Object} PagedResultOfVideoRewardResponseDto
 * @property {Array<VideoRewardResponseDto>} items - 数据列表
 * @property {number} totalCount - 总记录数
 * @property {number} pageIndex - 当前页码
 * @property {number} pageSize - 每页大小
 * @property {number} totalPages - 总页数
 * @property {boolean} hasPreviousPage - 是否有上一页
 * @property {boolean} hasNextPage - 是否有下一页
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 获取奖励详情
 * @param {number} rewardId - 奖励ID
 * @returns {Promise<ApiResult<VideoRewardResponseDto>>} 奖励详情
 */
export function getRewardDetail (rewardId) {
  return request.get(`/Reward/${rewardId}`)
}

/**
 * 分页查询奖励列表
 * @param {VideoRewardQueryDto} params - 查询参数
 * @returns {Promise<ApiResult<PagedResultOfVideoRewardResponseDto>>} 分页查询结果
 */
export function queryRewards (params) {
  return request.get('/Reward', params)
}

/**
 * 获取用户奖励列表
 * @param {number} userId - 用户ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.status] - 状态筛选
 * @returns {Promise<ApiResult<Array<VideoRewardResponseDto>>>} 用户奖励列表
 */
export function getUserRewards (userId, options = {}) {
  return request.get(`/Reward/user/${userId}`, options)
}

/**
 * 获取我的奖励列表
 * @param {Object} [options] - 查询选项
 * @param {number} [options.status] - 状态筛选
 * @returns {Promise<ApiResult<Array<VideoRewardResponseDto>>>} 我的奖励列表
 */
export function getMyRewards (options = {}) {
  return request.get('/Reward/my-rewards', options)
}

/**
 * 领取奖励
 * @param {number} rewardId - 奖励ID
 * @returns {Promise<ApiResult<boolean>>} 领取结果
 */
export function claimReward (rewardId) {
  return request.post(`/Reward/${rewardId}/claim`)
}

/**
 * 根据批次ID查询奖励
 * @param {number} batchId - 批次ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.PageIndex=1] - 页码
 * @param {number} [options.PageSize=20] - 每页大小
 * @returns {Promise<ApiResult<PagedResultOfVideoRewardResponseDto>>} 查询结果
 */
export function queryRewardsByBatch (batchId, options = {}) {
  return queryRewards({
    BatchId: batchId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据视频ID查询奖励
 * @param {number} videoId - 视频ID
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoRewardResponseDto>>} 查询结果
 */
export function queryRewardsByVideo (videoId, options = {}) {
  return queryRewards({
    VideoId: videoId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据状态查询奖励
 * @param {number} status - 状态
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoRewardResponseDto>>} 查询结果
 */
export function queryRewardsByStatus (status, options = {}) {
  return queryRewards({
    Status: status,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据金额范围查询奖励
 * @param {number} minAmount - 最小金额
 * @param {number} maxAmount - 最大金额
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoRewardResponseDto>>} 查询结果
 */
export function queryRewardsByAmountRange (minAmount, maxAmount, options = {}) {
  return queryRewards({
    MinAmount: minAmount,
    MaxAmount: maxAmount,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据时间范围查询奖励
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoRewardResponseDto>>} 查询结果
 */
export function queryRewardsByTimeRange (startTime, endTime, options = {}) {
  return queryRewards({
    StartTime: startTime,
    EndTime: endTime,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 批量领取奖励
 * @param {Array<number>} rewardIds - 奖励ID列表
 * @returns {Promise<Array<ApiResult<boolean>>>} 批量领取结果
 */
export function batchClaimRewards (rewardIds) {
  const promises = rewardIds.map(rewardId => claimReward(rewardId))
  return Promise.all(promises)
}

/**
 * 获取奖励状态选项
 * @returns {Array<{value: number, label: string}>} 状态选项
 */
export function getRewardStatusOptions () {
  return [
    { value: 0, label: '待发放' },
    { value: 1, label: '已发放' },
    { value: 2, label: '已领取' },
    { value: 3, label: '发放失败' },
    { value: 4, label: '已过期' }
  ]
}

/**
 * 获取奖励类型选项
 * @returns {Array<{value: string, label: string}>} 类型选项
 */
export function getRewardTypeOptions () {
  return [
    { value: 'answer', label: '答题奖励' },
    { value: 'view', label: '观看奖励' },
    { value: 'share', label: '分享奖励' },
    { value: 'complete', label: '完播奖励' }
  ]
}

/**
 * 获取排序字段选项
 * @returns {Array<{value: string, label: string}>} 排序字段选项
 */
export function getRewardSortFieldOptions () {
  return [
    { value: 'createTime', label: '创建时间' },
    { value: 'amount', label: '奖励金额' },
    { value: 'distributeTime', label: '发放时间' },
    { value: 'claimTime', label: '领取时间' },
    { value: 'status', label: '状态' }
  ]
}

/**
 * 格式化奖励金额
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额
 */
export function formatRewardAmount (amount) {
  if (typeof amount !== 'number') return '0.00'
  return amount.toFixed(2)
}

/**
 * 检查奖励是否可以领取
 * @param {VideoRewardResponseDto} reward - 奖励数据
 * @returns {Object} 检查结果 {canClaim: boolean, reason: string}
 */
export function checkRewardClaimable (reward) {
  if (reward.status === 2) {
    return {
      canClaim: false,
      reason: '奖励已领取'
    }
  }

  if (reward.status === 3) {
    return {
      canClaim: false,
      reason: '奖励发放失败'
    }
  }

  if (reward.status === 4) {
    return {
      canClaim: false,
      reason: '奖励已过期'
    }
  }

  if (reward.status !== 1) {
    return {
      canClaim: false,
      reason: '奖励未发放'
    }
  }

  return {
    canClaim: true,
    reason: ''
  }
}

/**
 * 计算奖励统计信息
 * @param {Array<VideoRewardResponseDto>} rewards - 奖励列表
 * @returns {Object} 统计信息
 */
export function calculateRewardStatistics (rewards) {
  if (!Array.isArray(rewards) || rewards.length === 0) {
    return {
      totalCount: 0,
      totalAmount: 0,
      claimedCount: 0,
      claimedAmount: 0,
      pendingCount: 0,
      pendingAmount: 0,
      failedCount: 0,
      failedAmount: 0
    }
  }

  const stats = {
    totalCount: rewards.length,
    totalAmount: 0,
    claimedCount: 0,
    claimedAmount: 0,
    pendingCount: 0,
    pendingAmount: 0,
    failedCount: 0,
    failedAmount: 0
  }

  rewards.forEach(reward => {
    stats.totalAmount += reward.amount || 0

    switch (reward.status) {
      case 2: // 已领取
        stats.claimedCount++
        stats.claimedAmount += reward.amount || 0
        break
      case 0: // 待发放
      case 1: // 已发放
        stats.pendingCount++
        stats.pendingAmount += reward.amount || 0
        break
      case 3: // 发放失败
      case 4: // 已过期
        stats.failedCount++
        stats.failedAmount += reward.amount || 0
        break
    }
  })

  return stats
}

// 默认导出所有奖励相关API
export default {
  getRewardDetail,
  queryRewards,
  getUserRewards,
  getMyRewards,
  claimReward,
  queryRewardsByBatch,
  queryRewardsByVideo,
  queryRewardsByStatus,
  queryRewardsByAmountRange,
  queryRewardsByTimeRange,
  batchClaimRewards,
  getRewardStatusOptions,
  getRewardTypeOptions,
  getRewardSortFieldOptions,
  formatRewardAmount,
  checkRewardClaimable,
  calculateRewardStatistics
}
