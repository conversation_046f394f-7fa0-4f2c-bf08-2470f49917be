/**
 * 格式化工具函数
 * 统一处理时间、日期、数字等格式化需求
 */

/**
 * 格式化视频时长（秒转为 MM:SS 格式）
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时长字符串
 */
export function formatDuration (seconds) {
  if (!seconds || seconds < 0) return '00:00';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * 格式化详细时长（秒转为 X小时Y分Z秒 格式）
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时长字符串
 */
export function formatDetailedDuration (seconds) {
  if (!seconds || seconds < 0) return '0秒';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSecs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}小时${minutes}分${remainingSecs}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${remainingSecs}秒`;
  } else {
    return `${remainingSecs}秒`;
  }
}

/**
 * 格式化日期（YYYY-MM-DD）
 * @param {string|Date} dateInput - 日期字符串或Date对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate (dateInput) {
  if (!dateInput) return '';

  try {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化失败:', error);
    return '';
  }
}

/**
 * 格式化日期时间（YYYY-MM-DD HH:mm）
 * @param {string|Date} dateInput - 日期字符串或Date对象
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime (dateInput) {
  if (!dateInput) return '未设置';

  try {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return '未设置';

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('日期时间格式化失败:', error);
    return '未设置';
  }
}

/**
 * 格式化详细日期时间（YYYY-MM-DD HH:mm:ss）
 * @param {string|Date} dateInput - 日期字符串或Date对象
 * @returns {string} 格式化后的详细日期时间字符串
 */
export function formatDetailedDateTime (dateInput) {
  if (!dateInput) return '';

  try {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('详细日期时间格式化失败:', error);
    return '';
  }
}

/**
 * 将时长格式（如 "2:30"）转换为秒数
 * @param {string} durationStr - 时长字符串
 * @returns {number} 秒数
 */
export function parseDurationToSeconds (durationStr) {
  if (!durationStr) return 0;

  const parts = durationStr.split(':');
  if (parts.length === 2) {
    const minutes = parseInt(parts[0]) || 0;
    const seconds = parseInt(parts[1]) || 0;
    return minutes * 60 + seconds;
  }

  return parseInt(durationStr) || 0;
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize (bytes) {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化数字（添加千分位分隔符）
 * @param {number} num - 数字
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber (num) {
  if (num === null || num === undefined) return '0';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 格式化百分比
 * @param {number} value - 数值（0-1之间）
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercentage (value, decimals = 2) {
  if (value === null || value === undefined) return '0.00%';
  return (value * 100).toFixed(decimals) + '%';
}

/**
 * 格式化金额（元）
 * @param {number} amount - 金额
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmount (amount, decimals = 2) {
  if (amount === null || amount === undefined) return '0.00';
  return amount.toFixed(decimals);
}

// 默认导出所有格式化函数
export default {
  formatDuration,
  formatDetailedDuration,
  formatDate,
  formatDateTime,
  formatDetailedDateTime,
  parseDurationToSeconds,
  formatFileSize,
  formatNumber,
  formatPercentage,
  formatAmount
};
