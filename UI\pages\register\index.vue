<template>
  <view class="register-container">
    <view class="register-content">
      <!-- Header section -->
      <view class="header-section">
        <text class="app-title">用户注册</text>
        <text class="app-subtitle">创建您的账号开始观看视频</text>
      </view>

      <!-- Register form -->
      <view class="form-container">
        <!-- Nickname input -->
        <view class="input-group">
          <text class="input-label">昵称 *</text>
          <u-input v-model="registerForm.nickname" placeholder="请输入您的昵称" border="surround" clearable
            :error="!!errors.nickname" @blur="validateNickname" @input="clearError('nickname')" class="register-input" />
          <text v-if="errors.nickname" class="error-message">{{ errors.nickname }}</text>
        </view>

        <!-- Mobile input -->
        <view class="input-group">
          <text class="input-label">手机号</text>
          <u-input v-model="registerForm.mobile" placeholder="请输入手机号（可选）" border="surround" clearable
            :error="!!errors.mobile" @blur="validateMobile" @input="clearError('mobile')" class="register-input" />
          <text v-if="errors.mobile" class="error-message">{{ errors.mobile }}</text>
        </view>

        <!-- Register button -->
        <u-button type="primary" :text="isLoading ? '注册中...' : '注册'" :loading="isLoading"
          :disabled="!canSubmit || isLoading" @click="handleRegister" class="register-btn" />

        <!-- Login link -->
        <view class="login-link-container">
          <text class="login-link-text">已有账号？</text>
          <text class="login-link" @click="goToLogin">立即登录</text>
        </view>
      </view>

      <!-- Footer -->
      <view class="footer">
        <text class="footer-text">© 2024 视频分享系统</text>
        <text class="footer-version">Version 1.0.0</text>
      </view>
    </view>

    <!-- Toast -->
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import { simpleRegister } from '@/api/video-user.js'

export default {
  data () {
    return {
      registerForm: {
        nickname: '',
        mobile: ''
      },
      isLoading: false,
      errors: {
        nickname: '',
        mobile: ''
      }
    }
  },
  computed: {
    canSubmit () {
      return this.registerForm.nickname.trim() &&
        !this.isLoading
    }
  },
  methods: {
    // 验证昵称
    validateNickname () {
      const nickname = this.registerForm.nickname.trim()
      if (!nickname) {
        this.errors.nickname = '昵称不能为空'
        return false
      }
      if (nickname.length < 2) {
        this.errors.nickname = '昵称至少2个字符'
        return false
      }
      if (nickname.length > 20) {
        this.errors.nickname = '昵称不能超过20个字符'
        return false
      }
      this.errors.nickname = ''
      return true
    },

    // 验证手机号
    validateMobile () {
      const mobile = this.registerForm.mobile.trim()
      if (mobile && !/^1[3-9]\d{9}$/.test(mobile)) {
        this.errors.mobile = '请输入正确的手机号'
        return false
      }
      this.errors.mobile = ''
      return true
    },

    // 清除错误信息
    clearError (field) {
      this.errors[field] = ''
    },

    // 验证整个表单
    validateForm () {
      const nicknameValid = this.validateNickname()
      const mobileValid = this.validateMobile()
      return nicknameValid && mobileValid
    },

    // 处理注册
    async handleRegister () {
      if (!this.validateForm()) {
        this.showToastMessage('请检查输入信息', 'error')
        return
      }

      if (!this.canSubmit || this.isLoading) return

      this.isLoading = true

      try {
        const { nickname, mobile } = this.registerForm
        const response = await simpleRegister({
          nickname: nickname.trim(),
          mobile: mobile.trim() || undefined
        })

        if (response.success && response.data) {
          // 保存用户信息到本地存储
          uni.setStorageSync('userInfo', response.data.userInfo)
          uni.setStorageSync('token', response.data.token)

          this.showToastMessage('注册成功！', 'success')
          await this.delay(1500)

          // 跳转到主页或返回上一页
          uni.switchTab({
            url: '/pages/index/index'
          })
        } else {
          this.showToastMessage(response.msg || '注册失败', 'error')
        }
      } catch (error) {
        console.error('Register error:', error)
        this.showToastMessage('注册失败，请重试', 'error')
      } finally {
        this.isLoading = false
      }
    },

    // 跳转到登录页面
    goToLogin () {
      uni.navigateTo({
        url: '/pages/user-login/index'
      })
    },

    showToastMessage (message, type = 'success') {
      this.$refs.uToast.show({
        message: message,
        type: type,
        duration: 3000
      })
    },

    delay (ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

.register-content {
  width: 100%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.form-container {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 48rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.register-input {
  width: 100%;
}

.error-message {
  display: block;
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 8rpx;
  line-height: 1.4;
}

.register-btn {
  width: 100%;
  height: 88rpx;
  margin-top: 20rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.login-link-container {
  text-align: center;
  margin-top: 32rpx;
}

.login-link-text {
  font-size: 28rpx;
  color: #666;
}

.login-link {
  font-size: 28rpx;
  color: #1976D2;
  margin-left: 8rpx;
  text-decoration: underline;
}

.footer {
  text-align: center;
  margin-top: 60rpx;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8rpx;
}

.footer-version {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
}
</style>
