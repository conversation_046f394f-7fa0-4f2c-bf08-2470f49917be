<template>
	<view class="member-card-wrapper">
		<view class="member-card" @tap="viewDetail">
			<view class="member-info">
				<view class="member-avatar" :style="{ backgroundImage: `url(${userInfo.avatar})` }"></view>
				<view class="member-details">
					<view class="member-name">{{ userInfo.username }}</view>
					<view class="member-register">
						<text class="date-label">{{ formatDate(userInfo.registerTime) }}</text> 注册
					</view>
				</view>
			</view>
			<view class="member-stats">
				<view class="stat-item">
					<text class="stat-label">观看视频</text>
					<text class="stat-value">{{ userInfo.watchedVideos || 0 }}个</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">完成测验</text>
					<text class="stat-value">{{ userInfo.completedQuizzes || 0 }}次</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">获得奖励</text>
					<text class="stat-value">{{ userInfo.totalRewards || 0 }}元</text>
				</view>
			</view>
		</view>
		<!-- 右上角操作按钮 -->
		<view class="top-right-actions">
			<view class="action-btn more-btn" @tap.stop="handleMore">
				<text>更多</text>
			</view>
			<view class="action-btn ban-btn" @tap.stop="handleBan">
				<text>禁用</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'MemberCard',
	props: {
		userInfo: {
			type: Object,
			required: true
		}
	},
	methods: {
		viewDetail () {
			this.$emit('view-detail', this.userInfo);
		},

		handleMore () {
			this.$emit('more', this.userInfo);
		},

		handleBan () {
			this.$emit('ban', this.userInfo);
		},

		formatDate (dateString) {
			if (!dateString) return '';

			// 处理日期格式
			const date = new Date(dateString);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
		}
	}
}
</script>

<style>
.member-card-wrapper {
	position: relative;
	margin-bottom: 16rpx;
}

.member-card {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(24, 144, 255, 0.1);
	border: 1rpx solid #e6f7ff;
}

.member-info {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.member-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 8rpx;
	background-size: cover;
	background-position: center;
	margin-right: 16rpx;
	background-color: #e6f7ff;
	border: 1rpx solid #bae7ff;
}

.member-details {
	flex: 1;
}

.member-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 6rpx;
}

.member-register {
	font-size: 22rpx;
	color: #999999;
}

.date-label {
	color: #666666;
}

.member-stats {
	display: flex;
	justify-content: space-between;
	padding: 12rpx 0;
	border-bottom: 1rpx solid #e6f7ff;
	border-top: 1rpx solid #e6f7ff;
	margin-bottom: 16rpx;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stat-label {
	font-size: 20rpx;
	color: #999999;
	margin-bottom: 4rpx;
}

.stat-value {
	font-size: 24rpx;
	font-weight: bold;
	color: #186BFF;
}

/* 右上角操作按钮 */
.top-right-actions {
	position: absolute;
	top: 15rpx;
	right: 15rpx;
	display: flex;
	gap: 10rpx;
	z-index: 10;
}

.action-btn {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	min-width: 60rpx;
	text-align: center;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.more-btn {
	background-color: #e6f7ff;
	color: #186BFF;
	border: 1rpx solid #91d5ff;
}

.ban-btn {
	background-color: #fff1f0;
	color: #ff4d4f;
	border: 1rpx solid #ffccc7;
}
</style>