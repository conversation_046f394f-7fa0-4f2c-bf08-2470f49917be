<template>
  <view class="custom-button" :class="[
    `custom-button--${type}`,
    `custom-button--${size}`,
    { 'custom-button--plain': plain },
    { 'custom-button--disabled': disabled },
    { 'custom-button--loading': loading },
    { 'custom-button--round': round }
  ]" :hover-class="!disabled && !loading ? 'custom-button--active' : ''" :style="customStyle" @click="clickHandler">
    <view class="custom-button__content">
      <view class="custom-button__loading" v-if="loading">
        <view class="custom-button__loading-indicator"></view>
      </view>
      <text class="custom-button__text">{{ text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomButton',
  props: {
    // 按钮类型
    type: {
      type: String,
      default: 'primary',
      validator: (value) => {
        return ['primary', 'success', 'warning', 'error', 'info', 'default'].includes(value);
      }
    },
    // 按钮尺寸
    size: {
      type: String,
      default: 'normal',
      validator: (value) => {
        return ['large', 'normal', 'small', 'mini'].includes(value);
      }
    },
    // 按钮文字
    text: {
      type: String,
      default: ''
    },
    // 是否为朴素按钮
    plain: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 是否为圆形按钮
    round: {
      type: Boolean,
      default: false
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    clickHandler (e) {
      // 禁用或加载中时不触发点击事件
      if (!this.disabled && !this.loading) {
        this.$emit('click', e);
      }
    }
  }
}
</script>

<style lang="scss">
.custom-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 1;
  text-align: center;
  border-radius: 8rpx;
  overflow: hidden;
  transition: all 0.25s;

  &--active {
    opacity: 0.7;
  }

  &--disabled {
    opacity: 0.5;
  }

  &--round {
    border-radius: 40rpx;
  }

  &__content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__loading {
    margin-right: 10rpx;

    &-indicator {
      width: 30rpx;
      height: 30rpx;
      border: 3rpx solid #ffffff;
      border-color: #ffffff transparent transparent transparent;
      border-radius: 50%;
      animation: custom-button-loading 1s infinite linear;
    }
  }

  &__text {
    font-size: 28rpx;
  }

  // 按钮类型
  &--primary {
    background-color: #186BFF;
    color: #ffffff;

    &.custom-button--plain {
      background-color: transparent;
      color: #186BFF;
      border: 1rpx solid #186BFF;

      .custom-button__loading-indicator {
        border-color: #186BFF transparent transparent transparent;
      }
    }
  }

  &--success {
    background-color: #52c41a;
    color: #ffffff;

    &.custom-button--plain {
      background-color: transparent;
      color: #52c41a;
      border: 1rpx solid #52c41a;

      .custom-button__loading-indicator {
        border-color: #52c41a transparent transparent transparent;
      }
    }
  }

  &--warning {
    background-color: #faad14;
    color: #ffffff;

    &.custom-button--plain {
      background-color: transparent;
      color: #faad14;
      border: 1rpx solid #faad14;

      .custom-button__loading-indicator {
        border-color: #faad14 transparent transparent transparent;
      }
    }
  }

  &--error {
    background-color: #ff4d4f;
    color: #ffffff;

    &.custom-button--plain {
      background-color: transparent;
      color: #ff4d4f;
      border: 1rpx solid #ff4d4f;

      .custom-button__loading-indicator {
        border-color: #ff4d4f transparent transparent transparent;
      }
    }
  }

  &--info {
    background-color: #909399;
    color: #ffffff;

    &.custom-button--plain {
      background-color: transparent;
      color: #909399;
      border: 1rpx solid #909399;

      .custom-button__loading-indicator {
        border-color: #909399 transparent transparent transparent;
      }
    }
  }

  &--default {
    background-color: #f5f5f5;
    color: #606266;

    &.custom-button--plain {
      background-color: transparent;
      color: #606266;
      border: 1rpx solid #d9d9d9;

      .custom-button__loading-indicator {
        border-color: #606266 transparent transparent transparent;
      }
    }
  }

  // 按钮尺寸
  &--large {
    height: 96rpx;
    font-size: 32rpx;
  }

  &--normal {
    height: 80rpx;
    font-size: 28rpx;
  }

  &--small {
    height: 64rpx;
    font-size: 24rpx;
    padding: 0 20rpx;
  }

  &--mini {
    height: 48rpx;
    font-size: 20rpx;
    padding: 0 16rpx;
  }
}

@keyframes custom-button-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
