<template>
    <view class="container">


        <view class="publish-form">


            <!-- 批次基本设置 -->
            <view class="form-section">
                <view class="section-title">批次基本信息</view>
                <view class="form-group">
                    <view class="form-label required">批次标题</view>
                    <input type="text" class="form-input" v-model="batchTitle" placeholder="请输入批次标题（2-50字符）"
                        maxlength="50" />
                    <view class="form-hint">{{ batchTitle.length }}/50</view>
                </view>


                <view class="form-group">
                    <view class="form-label required">活动开始日期</view>
                    <picker mode="date" :value="startDate" @change="onStartDateChange">
                        <view class="form-input picker-display">
                            {{ startDate || '请选择开始日期' }}
                        </view>
                    </picker>
                </view>
                <view class="form-group">
                    <view class="form-label required">活动开始时间</view>
                    <picker mode="time" :value="startTime" @change="onStartTimeChange">
                        <view class="form-input picker-display">
                            {{ startTime || '请选择开始时间' }}
                        </view>
                    </picker>
                </view>
                <view class="form-group">
                    <view class="form-label required">活动结束日期</view>
                    <picker mode="date" :value="endDate" @change="onEndDateChange">
                        <view class="form-input picker-display">
                            {{ endDate || '请选择结束日期' }}
                        </view>
                    </picker>
                </view>
                <view class="form-group">
                    <view class="form-label required">活动结束时间</view>
                    <picker mode="time" :value="endTime" @change="onEndTimeChange">
                        <view class="form-input picker-display">
                            {{ endTime || '请选择结束时间' }}
                        </view>
                    </picker>
                </view>

                <view class="action-buttons">
                    <button class="action-btn cancel-btn" @tap="goBack">
                        <i class="fa fa-times"></i> 取消
                    </button>
                    <button class="action-btn publish-btn" @tap="createBatch()" :disabled="!isFormValid">
                        <i class="fa fa-check"></i> 创建批次
                    </button>
                </view>
            </view>






            <!-- 提交按钮 -->


        </view>
    </view>
</template>

<script>

import { createBatch } from "@/api/batch.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    data () {
        return {
            videoId: 0,           // 视频ID
            batchTitle: '',       // 批次标题
            batchDescription: '', // 批次描述

            // 日期时间选择
            startDate: '',
            startTime: '',
            endDate: '',
            endTime: '',


        }
    },
    computed: {
        isFormValid () {
            // 简化验证 - 只要有标题即可（时间有默认值）
            return this.batchTitle &&
                this.batchTitle.trim() &&
                this.batchTitle.trim().length >= 2;
        }
    },
    onLoad (options) {
        console.log('创建批次页接收到的参数:', options);

        if (options && (options.videoId || options.id)) {
            const videoId = parseInt(options.videoId || options.id);
            this.videoId = isNaN(videoId) ? 0 : videoId;
        }

        // 初始化时间
        this.initDateTimeValues();
    },
    methods: {


        // 初始化日期时间值
        initDateTimeValues () {
            // 设置开始时间为当前日期时间
            const now = new Date();
            this.startDate = this.formatDate(now);
            this.startTime = this.formatTime(now);

            // 设置结束时间为30天后
            const thirtyDaysLater = new Date();
            thirtyDaysLater.setDate(thirtyDaysLater.getDate() + 30);
            this.endDate = this.formatDate(thirtyDaysLater);
            this.endTime = this.formatTime(thirtyDaysLater);
        },

        formatDate (date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },

        formatTime (date) {
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${hours}:${minutes}`;
        },

        // 开始日期选择事件
        onStartDateChange (e) {
            this.startDate = e.detail.value;
        },

        // 开始时间选择事件
        onStartTimeChange (e) {
            this.startTime = e.detail.value;
        },

        // 结束日期选择事件
        onEndDateChange (e) {
            this.endDate = e.detail.value;
        },

        // 结束时间选择事件
        onEndTimeChange (e) {
            this.endTime = e.detail.value;
        },

        // 获取完整的开始时间
        getStartDateTime () {
            if (!this.startDate || !this.startTime) return '';
            // 确保日期格式正确，只取日期部分
            let dateOnly = this.startDate;
            if (dateOnly.includes(' ')) {
                dateOnly = dateOnly.split(' ')[0];
            }
            // 转换为ISO 8601格式
            return `${dateOnly}T${this.startTime}:00`;
        },

        // 获取完整的结束时间
        getEndDateTime () {
            if (!this.endDate || !this.endTime) return '';
            // 确保日期格式正确，只取日期部分
            let dateOnly = this.endDate;
            if (dateOnly.includes(' ')) {
                dateOnly = dateOnly.split(' ')[0];
            }
            // 转换为ISO 8601格式
            return `${dateOnly}T${this.endTime}:00`;
        },





        // 创建批次 - 创建模式
        async createBatch () {
            try {
                // 表单验证
                const validation = this.validateForm();
                if (!validation.valid) {
                    uni.showToast({
                        title: validation.message,
                        icon: "none",
                    });
                    return;
                }

                uni.showLoading({
                    title: "创建批次中...",
                });

                // 构建批次数据 - 确保数据类型正确
                const batchData = {
                    name: this.batchTitle.trim(),
                    description: this.batchDescription.trim() || `暂无描述`,
                    videoId: this.videoId, // 使用页面的videoId
                    startTime: this.getStartDateTime(),
                    endTime: this.getEndDateTime(),
                    redPacketAmount: 0 // 默认为0
                };



                // 调用API创建批次
                const response = await createBatch(batchData);

                if (response.success) {
                    uni.hideLoading();
                    uni.showToast({
                        title: '批次创建成功',
                        icon: 'success'
                    });

                    // 返回批次列表页面
                    setTimeout(() => {
                        this.goBack();
                    }, 1500);
                } else {
                    throw new Error(response.msg || '创建批次失败');
                }
            } catch (error) {
                console.error('创建批次失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: error.message || "创建失败",
                    icon: "none",
                });
            }
        },

        // 表单验证
        validateForm () {
            if (!this.batchTitle || this.batchTitle.trim() === '') {
                return { valid: false, message: '请输入批次标题' };
            }

            if (this.batchTitle.trim().length < 2) {
                return { valid: false, message: '批次标题至少需要2个字符' };
            }

            if (this.batchTitle.trim().length > 50) {
                return { valid: false, message: '批次标题不能超过50个字符' };
            }

            if (this.batchDescription && this.batchDescription.length > 200) {
                return { valid: false, message: '批次描述不能超过200个字符' };
            }

            if (!this.startDate || !this.startTime) {
                return { valid: false, message: '请选择活动开始时间' };
            }

            if (!this.endDate || !this.endTime) {
                return { valid: false, message: '请选择活动结束时间' };
            }

            if (!this.videoId || this.videoId <= 0) {
                return { valid: false, message: '视频ID无效，请重新进入页面' };
            }

            // 验证时间逻辑
            const startDateTime = new Date(this.getStartDateTime());
            const endDateTime = new Date(this.getEndDateTime());

            if (startDateTime >= endDateTime) {
                return { valid: false, message: '开始时间必须早于结束时间' };
            }

            return { valid: true, message: '' };
        },









        goBack () {
            uni.navigateBack();
        }
    }
}
</script>

<style>
.container {
    background-color: #f7f7f7;
    padding-bottom: 120rpx;
}



.form-section {
    margin-bottom: 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx 30rpx;
    box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.05);
    border: 1rpx solid #f0f0f0;
}

.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
    padding-bottom: 20rpx;
    border-bottom: 2rpx solid #f0f0f0;
    position: relative;
}

.section-title::before {
    content: '';
    position: absolute;
    bottom: -2rpx;
    left: 0;
    width: 60rpx;
    height: 4rpx;
    background: linear-gradient(to right, #186BFF, #4A90E2);
    border-radius: 2rpx;
}

.info-card {
    padding: 20rpx 0;
}

.batch-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
}

.batch-info {
    margin-top: 20rpx;
    background-color: #fafafa;
    padding: 20rpx;
    border-radius: 8rpx;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    font-size: 28rpx;
}

.info-label {
    color: #666;
    min-width: 160rpx;
}

.info-value {
    color: #333;
    flex: 1;
}

.form-group {
    margin-bottom: 32rpx;
    position: relative;
}

.form-label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 16rpx;
    font-weight: 500;
    display: block;
}

.form-label.required::before {
    content: '*';
    color: #ff4757;
    margin-right: 6rpx;
    font-weight: bold;
}



.form-input {
    height: 88rpx;
    line-height: 88rpx;
    padding: 0 30rpx;
    border: 2rpx solid #e5e5e5;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333;
    background-color: #ffffff;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.form-input:focus {
    border-color: #186BFF;
    background-color: #f8fbff;
    outline: none;
}

.form-input::placeholder {
    color: #999;
}

/* 时间选择器样式 */
.datetime-picker {
    width: 100%;
}

.picker-display {
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    position: relative;
}

.picker-display:after {
    content: '';
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 8rpx solid #999;
    border-top: 6rpx solid transparent;
    border-bottom: 6rpx solid transparent;
}

.form-textarea {
    width: 100%;
    height: 200rpx;
    padding: 20rpx 30rpx;
    border: 1px solid #e5e5e5;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #333;
    background-color: #ffffff;
    box-sizing: border-box;
    resize: none;
    line-height: 1.5;
}

.form-hint {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
    text-align: right;
}

.error-message {
    padding: 30rpx;
    color: #f56c6c;
    text-align: center;
    font-size: 28rpx;
}



/* 按钮样式优化 */
.action-buttons {
    display: flex;
    justify-content: space-between;
    padding: 40rpx 30rpx;
    margin-top: 20rpx;
    gap: 20rpx;
}

.action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 12rpx;
    font-size: 30rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.action-btn .fa {
    margin-right: 8rpx;
    font-size: 28rpx;
}

.action-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.action-btn.full-width {
    width: 100%;
}

.cancel-btn {
    background-color: #ffffff;
    color: #186BFF;
    border: 2rpx solid #186BFF;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.cancel-btn:active {
    background-color: #f8fbff;
    border-color: #096dd9;
    color: #096dd9;
}

.publish-btn {
    background-color: #186BFF;
    color: #ffffff;
    border: none;
}

.publish-btn:active {
    background-color: #096dd9;
    transform: translateY(2rpx);
}

.publish-btn[disabled] {
    background: #f5f5f5;
    color: #bfbfbf;
    box-shadow: none;
    cursor: not-allowed;
}

.back-btn {
    background-color: #fafafa;
    color: #8c8c8c;
    width: 100%;
    border: 2rpx solid #d9d9d9;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.back-btn:active {
    background-color: #f5f5f5;
    border-color: #bfbfbf;
    color: #595959;
}

.page-subtitle {
    font-size: 24rpx;
    color: #999;
}

/* 批次状态标签 */
.batch-status {
    display: inline-block;
    padding: 4rpx 16rpx;
    border-radius: 30rpx;
    font-size: 24rpx;
    color: #fff;
}

.status-pending {
    background-color: #faad14;
}

.status-active {
    background-color: #07c160;
}

.status-ended {
    background-color: #999;
}
</style>
