/**
 * 用户管理相关API
 * 处理用户的增删改查、权限管理等功能
 */

import request from '../utils/request.js'

/**
 * 用户查询参数
 * @typedef {Object} UserQueryParams
 * @property {string} [username] - 用户名
 * @property {string} [name] - 姓名
 * @property {string} [role] - 角色
 * @property {string} [status] - 状态
 * @property {string} [department] - 部门
 * @property {string} [createTimeStart] - 创建时间开始
 * @property {string} [createTimeEnd] - 创建时间结束
 * @property {number} page - 页码
 * @property {number} pageSize - 每页大小
 */

/**
 * 用户数据
 * @typedef {Object} UserDto
 * @property {number} id - 用户ID
 * @property {string} username - 用户名
 * @property {string} name - 姓名
 * @property {string} email - 邮箱
 * @property {string} phone - 手机号
 * @property {string} role - 角色
 * @property {string} department - 部门
 * @property {string} status - 状态
 * @property {string} avatar - 头像URL
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 * @property {string} lastLoginTime - 最后登录时间
 * @property {Array<string>} permissions - 权限列表
 */

/**
 * 创建用户请求
 * @typedef {Object} CreateUserDto
 * @property {string} username - 用户名
 * @property {string} password - 密码
 * @property {string} name - 姓名
 * @property {string} email - 邮箱
 * @property {string} phone - 手机号
 * @property {string} role - 角色
 * @property {string} department - 部门
 * @property {string} [avatar] - 头像URL
 * @property {Array<string>} [permissions] - 权限列表
 */

/**
 * 更新用户请求
 * @typedef {Object} UpdateUserDto
 * @property {string} [name] - 姓名
 * @property {string} [email] - 邮箱
 * @property {string} [phone] - 手机号
 * @property {string} [role] - 角色
 * @property {string} [department] - 部门
 * @property {string} [status] - 状态
 * @property {string} [avatar] - 头像URL
 * @property {Array<string>} [permissions] - 权限列表
 */

/**
 * 用户查询结果类型
 * @typedef {import('./types/common.js').ApiResult<import('./types/common.js').PageResult<UserDto>>} UserQueryResult
 */

/**
 * 用户详情结果类型
 * @typedef {import('./types/common.js').ApiResult<UserDto>} UserDetailResult
 */

/**
 * 创建用户结果类型
 * @typedef {import('./types/common.js').ApiResult<UserDto>} CreateUserResult
 */

/**
 * 更新用户结果类型
 * @typedef {import('./types/common.js').ApiResult<UserDto>} UpdateUserResult
 */

/**
 * 删除用户结果类型
 * @typedef {import('./types/common.js').ApiResult<boolean>} DeleteUserResult
 */

/**
 * 查询用户列表
 * @param {UserQueryParams} params - 查询参数
 * @returns {Promise<UserQueryResult>} 用户列表
 */
export function queryUsers(params) {
  return request.get('/users/query', params)
}

/**
 * 获取用户详情
 * @param {number} userId - 用户ID
 * @returns {Promise<UserDetailResult>} 用户详情
 */
export function getUserDetail(userId) {
  return request.get(`/users/${userId}`)
}

/**
 * 创建用户
 * @param {CreateUserDto} data - 用户数据
 * @returns {Promise<CreateUserResult>} 创建结果
 */
export function createUser(data) {
  return request.post('/users/create', data)
}

/**
 * 更新用户
 * @param {number} userId - 用户ID
 * @param {UpdateUserDto} data - 更新数据
 * @returns {Promise<UpdateUserResult>} 更新结果
 */
export function updateUser(userId, data) {
  return request.put(`/users/${userId}`, data)
}

/**
 * 删除用户
 * @param {number} userId - 用户ID
 * @returns {Promise<DeleteUserResult>} 删除结果
 */
export function deleteUser(userId) {
  return request.delete(`/users/${userId}`)
}

/**
 * 批量删除用户
 * @param {Array<number>} userIds - 用户ID列表
 * @returns {Promise<DeleteUserResult>} 删除结果
 */
export function batchDeleteUsers(userIds) {
  return request.post('/users/batch-delete', { userIds })
}

/**
 * 启用/禁用用户
 * @param {number} userId - 用户ID
 * @param {string} status - 状态 (active/inactive)
 * @returns {Promise<UpdateUserResult>} 更新结果
 */
export function toggleUserStatus(userId, status) {
  return request.post(`/users/${userId}/toggle-status`, { status })
}

/**
 * 重置用户密码
 * @param {number} userId - 用户ID
 * @param {string} newPassword - 新密码
 * @returns {Promise<import('./types/common.js').ApiResult<boolean>>} 重置结果
 */
export function resetUserPassword(userId, newPassword) {
  return request.post(`/users/${userId}/reset-password`, { newPassword })
}

/**
 * 获取用户角色列表
 * @returns {Promise<import('./types/common.js').ApiResult<Array<{value: string, label: string}>>>} 角色列表
 */
export function getUserRoles() {
  return request.get('/users/roles')
}

/**
 * 获取用户部门列表
 * @returns {Promise<import('./types/common.js').ApiResult<Array<{value: string, label: string}>>>} 部门列表
 */
export function getUserDepartments() {
  return request.get('/users/departments')
}

/**
 * 获取用户权限列表
 * @returns {Promise<import('./types/common.js').ApiResult<Array<{value: string, label: string, group: string}>>>} 权限列表
 */
export function getUserPermissionList() {
  return request.get('/users/permissions')
}

/**
 * 导出用户数据
 * @param {UserQueryParams} params - 查询参数
 * @returns {Promise<Blob>} 导出文件
 */
export function exportUsers(params) {
  return request.post('/users/export', params, {
    responseType: 'blob'
  })
}

/**
 * 导入用户数据
 * @param {FormData} formData - 包含文件的表单数据
 * @returns {Promise<import('./types/common.js').ApiResult<{success: number, failed: number, errors: Array<string>}>>} 导入结果
 */
export function importUsers(formData) {
  return request.post('/users/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取用户统计信息
 * @returns {Promise<import('./types/common.js').ApiResult<{total: number, active: number, inactive: number, byRole: Object, byDepartment: Object}>>} 统计信息
 */
export function getUserStats() {
  return request.get('/users/stats')
}

/**
 * 检查用户名是否可用
 * @param {string} username - 用户名
 * @param {number} [excludeUserId] - 排除的用户ID（用于编辑时检查）
 * @returns {Promise<import('./types/common.js').ApiResult<boolean>>} 检查结果
 */
export function checkUsernameAvailable(username, excludeUserId) {
  return request.get('/users/check-username', { username, excludeUserId })
}

/**
 * 检查邮箱是否可用
 * @param {string} email - 邮箱
 * @param {number} [excludeUserId] - 排除的用户ID（用于编辑时检查）
 * @returns {Promise<import('./types/common.js').ApiResult<boolean>>} 检查结果
 */
export function checkEmailAvailable(email, excludeUserId) {
  return request.get('/users/check-email', { email, excludeUserId })
}

// 默认导出所有用户相关API
export default {
  queryUsers,
  getUserDetail,
  createUser,
  updateUser,
  deleteUser,
  batchDeleteUsers,
  toggleUserStatus,
  resetUserPassword,
  getUserRoles,
  getUserDepartments,
  getUserPermissionList,
  exportUsers,
  importUsers,
  getUserStats,
  checkUsernameAvailable,
  checkEmailAvailable
}
