# 视频分享系统使用说明

## 功能概述

本系统实现了完整的视频分享功能，包括：
- 批次详情页面分享
- 视频播放页面优化
- 统一用户系统
- 用户活动记录

## 主要功能

### 1. 批次分享功能

#### 分享链接生成
- 位置：`UI/pages/admin/media/batch-detail.vue`
- 功能：管理员可以在批次详情页面生成分享链接
- 链接格式：`http://localhost:5173/#/pages/video/index?videoId={videoId}&batchId={batchId}&sharerId={sharerId}`

#### 使用方法
1. 进入批次详情页面
2. 点击"复制视频链接"按钮
3. 系统自动生成包含分享人信息的链接
4. 链接复制到剪贴板，可分享给其他用户

### 2. 视频播放优化

#### 分享参数检测
- 位置：`UI/pages/video/index.vue`
- 功能：自动检测URL中的分享参数（sharerId, batchId）
- 验证：批次有效性、时间范围、状态检查

#### 批次验证逻辑
```javascript
// 验证批次是否有效
validateBatch() {
  // 检查批次是否存在
  // 检查批次状态（启用/禁用）
  // 检查时间范围（开始时间/结束时间）
}
```

### 3. IP用户系统（无需注册）

#### 自动用户创建
- 工具：`UI/utils/ip-user.js`
- 原理：基于IP地址或设备信息自动生成用户
- 标识：IP/设备ID转MD5作为唯一用户标识
- 昵称：自动生成格式为 `访客_[哈希前8位]`

#### 核心功能
- `getOrCreateIPUser()` - 获取或创建IP用户
- `getCurrentIPUserId()` - 获取当前IP用户ID
- `clearIPUserCache()` - 清除IP用户缓存
- `getIPUserDisplayInfo()` - 获取用户显示信息

#### 数据存储
- **ipUser**: 完整IP用户信息（包含哈希、IP地址等）
- **userInfo**: 兼容现有代码的用户信息
- **deviceId**: 设备唯一标识（备用方案）

#### 视频页面访问
- **无需登录**: 视频页面完全公开访问
- **自动创建**: 页面加载时自动创建匿名用户
- **用户跟踪**: 通过IP/设备标识跟踪用户行为

### 4. 用户活动记录

#### 观看记录
- API：`UI/api/user-batch-record.js`
- 功能：记录用户观看进度、时长、完成状态
- 自动调用：视频播放过程中自动记录

#### 答题记录
- 功能：记录用户答题结果、得分
- 触发：用户提交答案时自动记录

#### 奖励发放
- 功能：答题完成后自动发放奖励
- 记录：奖励金额、类型、发放时间

## 配置说明

### URL配置
- 文件：`UI/static/config/app-config.js`
- 配置项：`UIProjectUrl: 'http://localhost:5173'`
- 用途：分享链接的基础URL

### API端点
```javascript
// 用户相关
POST /User/register        // 用户注册
POST /User/simple-login    // 简单登录
POST /User/login          // 微信登录

// 用户记录相关
POST /UserBatchRecord/create-or-get    // 创建或获取记录
PUT  /UserBatchRecord/watch-progress   // 更新观看进度
POST /UserBatchRecord/start-watching   // 开始观看
POST /UserBatchRecord/submit-answer    // 提交答案
POST /UserBatchRecord/grant-reward     // 发放奖励
```

## 测试功能

### 测试页面
- 位置：`UI/pages/test-share/index.vue`
- 功能：
  - 生成测试分享链接
  - 测试用户注册/登录
  - 查看本地存储数据
  - 清除测试数据

### 测试流程
1. 访问测试页面
2. 输入测试参数（批次ID、视频ID、分享人ID）
3. 生成测试链接
4. 点击"测试链接"验证功能
5. 测试用户注册和登录流程

## 使用流程

### 完整分享流程
1. **管理员操作**：
   - 登录管理后台
   - 进入批次详情页面
   - 点击"复制视频链接"生成分享链接

2. **用户接收**：
   - 接收分享链接
   - 点击链接打开视频页面

3. **用户体验**：
   - 系统检测分享参数
   - 验证批次有效性
   - 要求用户登录（如未登录）
   - 记录分享人信息
   - 开始视频播放和记录

4. **数据记录**：
   - 观看进度自动记录
   - 答题结果自动提交
   - 奖励自动发放

## 注意事项

1. **URL配置**：确保 `app-config.js` 中的URL配置正确
2. **批次时间**：分享链接受批次时间限制
3. **用户认证**：视频播放需要用户登录
4. **数据同步**：观看记录实时同步到服务器
5. **错误处理**：各个环节都有完善的错误处理机制

## 后续扩展

1. **微信登录**：已预留接口，可后续接入
2. **分享统计**：可添加分享效果统计功能
3. **奖励系统**：可扩展更复杂的奖励规则
4. **社交功能**：可添加用户间的社交互动功能
