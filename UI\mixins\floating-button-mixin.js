/**
 * 悬浮按钮功能混入
 * 提供拖拽、贴边等通用功能
 */
export default {
  data () {
    return {
      btnPosition: {
        left: 20,
        top: 300,
      },
      startTouch: {
        x: 0,
        y: 0,
      },
    };
  },

  methods: {
    /**
     * 初始化悬浮按钮位置
     */
    initFloatingButtonPosition () {
      uni.getSystemInfo({
        success: (res) => {
          const btnSize = 40; // 80rpx 转换为 px 大约是 40px
          const edgeMargin = 15; // 边距
          this.btnPosition = {
            left: res.windowWidth - btnSize - edgeMargin,
            top: res.windowHeight - 200,
          };
        },
      });
    },

    /**
     * 移动悬浮按钮
     */
    moveFloatingBtn (e) {
      const touch = e.touches[0];
      const btnSize = 40; // 80rpx 转换为 px 大约是 40px
      this.btnPosition.left = touch.clientX - btnSize / 2;
      this.btnPosition.top = touch.clientY - btnSize / 2;
    },

    /**
     * 触摸开始
     */
    touchStart (e) {
      // 记录初始触摸位置
      this.startTouch = {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      };
    },

    /**
     * 贴边处理
     */
    snapToEdge () {
      // 获取屏幕宽度
      const screenWidth = uni.getSystemInfoSync().windowWidth;
      const screenHeight = uni.getSystemInfoSync().windowHeight;
      const btnSize = 40; // 80rpx 转换为 px 大约是 40px
      const edgeMargin = 15; // 边距

      // 如果距离右边更近，则贴右边
      if (this.btnPosition.left > screenWidth / 2) {
        this.btnPosition.left = screenWidth - btnSize - edgeMargin;
      } else {
        this.btnPosition.left = edgeMargin; // 贴左边
      }

      // 确保按钮不会超出屏幕上下边界
      if (this.btnPosition.top < 100) {
        this.btnPosition.top = 100; // 顶部留出一些空间
      } else if (this.btnPosition.top > screenHeight - 150) {
        this.btnPosition.top = screenHeight - 150; // 底部留出一些空间
      }
    },
  },
};
