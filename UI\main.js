import App from './App'
import config from './config/index.js'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
Vue.prototype.$config = config
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import uviewPlus from 'uview-plus'

export function createApp () {
  const app = createSSRApp(App)

  // 使用 uview-plus
  app.use(uviewPlus)

  // 注册全局配置
  app.config.globalProperties.$config = config

  return {
    app
  }
}
// #endif