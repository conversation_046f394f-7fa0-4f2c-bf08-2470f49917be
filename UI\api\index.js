/**
 * API模块统一导出
 * 提供所有API模块的统一入口
 */

// 导入所有API模块
import authApi from './auth.js'
import userApi from './user.js'
import sysUserApi from './sysuser.js'
import videoApi from './video.js'
import quizApi from './quiz.js'

import statisticsApi from './statistics.js'
import cardApi from './card.js'
import answerRecordApi from './answer-record.js'
import batchApi from './batch.js'
import dashboardApi from './dashboard.js'
import videoRewardApi from './video-reward.js'
import videoUserApi from './video-user.js'
import employeeApi from './employee.js'
import userAuditApi from './user-audit.js'
import userTransferApi from './user-transfer.js'
import videoManagementApi from './video-management.js'
import viewRecordApi from './view-record.js'
import systemConfigApi from './system-config.js'

// 导入类型定义
import commonTypes from './types/common.js'

// 导入基础请求模块
import request from '../utils/request.js'

/**
 * 统一的API对象
 * 包含所有业务模块的API方法
 */
const api = {
  // 认证相关
  auth: authApi,

  // 用户管理
  user: userApi,

  // 系统用户管理
  sysUser: sysUserApi,

  // 视频管理
  video: videoApi,

  // 测验管理
  quiz: quizApi,



  // 统计分析
  statistics: statisticsApi,

  // 卡片管理
  card: cardApi,

  // 答题记录管理
  answerRecord: answerRecordApi,

  // 批次管理
  batch: batchApi,

  // 仪表板数据 (新的Dashboard API端点)
  dashboard: dashboardApi,

  // 视频奖励管理
  videoReward: videoRewardApi,

  // 视频用户管理
  videoUser: videoUserApi,

  // 员工管理
  employee: employeeApi,

  // 用户审核管理
  userAudit: userAuditApi,

  // 用户转移管理
  userTransfer: userTransferApi,

  // 视频管理
  videoManagement: videoManagementApi,

  // 观看记录管理
  viewRecord: viewRecordApi,

  // 系统配置管理
  systemConfig: systemConfigApi,

  // 基础请求方法
  request: request
}

// 按功能分组导出（方便按需引入）
export const auth = authApi
export const user = userApi
export const sysUser = sysUserApi
export const video = videoApi
export const quiz = quizApi

export const statistics = statisticsApi
export const card = cardApi
export const answerRecord = answerRecordApi
export const batch = batchApi
export const dashboard = dashboardApi
export const videoReward = videoRewardApi
export const videoUser = videoUserApi
export const employee = employeeApi
export const userAudit = userAuditApi
export const userTransfer = userTransferApi
export const videoManagement = videoManagementApi
export const viewRecord = viewRecordApi
export const systemConfig = systemConfigApi

// 导出类型定义
export const types = commonTypes

// 导出基础请求方法
export { default as request } from '../utils/request.js'

// 默认导出统一API对象
export default api

/**
 * 使用示例：
 *
 * // 方式1: 使用统一API对象
 * import api from '@/api'
 * const userList = await api.user.queryUsers({ page: 1, pageSize: 20 })
 * const loginResult = await api.auth.login({ username: 'admin', password: '123456' })
 * const dashboardData = await api.dashboard.getDashboard({ startDate: '2025-01-01', endDate: '2025-01-18' })
 *
 * // 方式2: 按需导入特定模块
 * import { user, auth, dashboard, systemConfig } from '@/api'
 * const userList = await user.queryUsers({ page: 1, pageSize: 20 })
 * const loginResult = await auth.login({ username: 'admin', password: '123456' })
 * const dashboardData = await dashboard.getDashboard()
 * const keyMetrics = await dashboard.getKeyMetrics()
 * const todayData = await dashboard.getTodayDashboard()
 * const configs = await systemConfig.querySystemConfigs({ PageIndex: 1, PageSize: 20 })
 *
 * // 方式3: 导入单个模块
 * import userApi from '@/api/user'
 * import dashboardApi from '@/api/dashboard'
 * const userList = await userApi.queryUsers({ page: 1, pageSize: 20 })
 * const allStats = await dashboardApi.getAllStatistics()
 *
 * // 方式4: 使用基础请求方法
 * import { request } from '@/api'
 * const response = await request.get('/custom/endpoint')
 */
