<template>
  <view class="redirect-page">
    <view class="loading">
      <text>正在跳转到管理员页面...</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ManagerListRedirect',
  onLoad () {
    console.log('manager-list.vue 重定向到统一页面');
    // 立即重定向到统一的用户管理页面
    uni.redirectTo({
      url: '/pages/admin/users/user-management?type=manager'
    });
  }
};
</script>

<style lang="scss" scoped>
.redirect-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;

  .loading {
    text-align: center;
    color: #666;
  }
}
</style>
