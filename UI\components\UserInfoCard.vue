<template>
  <view class="user-info-card">
    <!-- 用户基本信息 -->
    <UserBasicInfo :userInfo="userInfo" :showActionBtn="showActionBtn" :actionIcon="actionIcon" @action="handleAction"
      @copy-id="copyUserId" />

    <!-- 统计数据 -->
    <UserStatistics :userInfo="userInfo" :timeFilter="actualTimeFilter" />

    <!-- 额外信息(插槽) -->
    <view class="extra-section" v-if="$slots.extra">
      <slot name="extra"></slot>
    </view>

    <!-- 底部操作按钮 -->
    <UserActions v-if="showFooterBtns" :userInfo="userInfo" :showDetailBtn="showDetailBtn" :showUsersBtn="showUsersBtn"
      :showEmployeesBtn="showEmployeesBtn" :showAccountBtn="showAccountBtn" @view-detail="viewDetail"
      @view-users="viewUsers" @view-employees="viewEmployees" @account-action="handleAccountAction">
      <slot name="buttons"></slot>
    </UserActions>
  </view>
</template>

<script>
import UserBasicInfo from './user/UserBasicInfo.vue'
import UserStatistics from './user/UserStatistics.vue'
import UserActions from './user/UserActions.vue'

export default {
  name: "UserInfoCard",
  components: {
    UserBasicInfo,
    UserStatistics,
    UserActions
  },
  props: {
    userInfo: {
      type: Object,
      required: true,
    },
    showActionBtn: {
      type: Boolean,
      default: false,
    },
    actionIcon: {
      type: String,
      default: "icon-more",
    },
    showFooterBtns: {
      type: Boolean,
      default: true,
    },
    showDetailBtn: {
      type: Boolean,
      default: true,
    },
    showUsersBtn: {
      type: Boolean,
      default: true,
    },
    showEmployeesBtn: {
      type: Boolean,
      default: true,
    },
    showAccountBtn: {
      type: Boolean,
      default: false,
    },
    timeFilter: {
      type: String,
      default: "today",
    },
  },
  computed: {
    actualTimeFilter () {
      return this.timeFilter || "today";
    },
  },
  methods: {
    // 复制用户ID
    copyUserId () {
      uni.setClipboardData({
        data: this.userInfo.id.toString(),
        success: () => {
          uni.showToast({
            title: "ID已复制",
            icon: "success",
          });
        },
      });
    },

    // 处理操作按钮点击
    handleAction () {
      this.$emit("action", this.userInfo);
    },

    // 查看详情
    viewDetail () {
      this.$emit("view-detail", this.userInfo);
    },

    // 查看用户
    viewUsers () {
      this.$emit("view-users", this.userInfo);
    },

    // 查看员工
    viewEmployees () {
      this.$emit("view-employees", this.userInfo);
    },

    // 处理账号操作
    handleAccountAction () {
      this.$emit("account-action", this.userInfo);
    },
  },
};
</script>

<style scoped>
.user-info-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.extra-section {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}
</style>