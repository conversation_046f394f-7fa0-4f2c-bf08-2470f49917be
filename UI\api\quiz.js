/**
 * 测验管理相关API
 * 处理测验的创建、管理、答题记录等功能
 */

import request from '../utils/request.js'

/**
 * 测验查询参数
 * @typedef {Object} QuizQueryParams
 * @property {string} [title] - 测验标题
 * @property {number} [videoId] - 关联视频ID
 * @property {string} [status] - 状态
 * @property {string} [creator] - 创建者
 * @property {string} [createTimeStart] - 创建时间开始
 * @property {string} [createTimeEnd] - 创建时间结束
 * @property {number} page - 页码
 * @property {number} pageSize - 每页大小
 */

/**
 * 创建测验数据
 * @typedef {Object} CreateQuizDto
 * @property {string} title - 测验标题
 * @property {string} description - 测验描述
 * @property {number} videoId - 关联视频ID
 * @property {number} passingScore - 及格分数
 * @property {number} timeLimit - 时间限制(分钟)
 * @property {number} maxAttempts - 最大尝试次数
 * @property {boolean} shuffleQuestions - 是否打乱题目顺序
 * @property {boolean} shuffleOptions - 是否打乱选项顺序
 * @property {boolean} showResult - 是否显示结果
 * @property {boolean} showCorrectAnswer - 是否显示正确答案
 * @property {string} status - 状态
 * @property {Array<CreateQuizQuestionDto>} questions - 题目列表
 */

/**
 * 测验数据
 * @typedef {Object} QuizDto
 * @property {number} id - 测验ID
 * @property {string} title - 测验标题
 * @property {string} description - 测验描述
 * @property {number} videoId - 关联视频ID
 * @property {string} videoTitle - 视频标题
 * @property {number} questionCount - 题目数量
 * @property {number} passingScore - 及格分数
 * @property {number} timeLimit - 时间限制(分钟)
 * @property {number} maxAttempts - 最大尝试次数
 * @property {boolean} shuffleQuestions - 是否打乱题目顺序
 * @property {boolean} shuffleOptions - 是否打乱选项顺序
 * @property {boolean} showResult - 是否显示结果
 * @property {boolean} showCorrectAnswer - 是否显示正确答案
 * @property {string} status - 状态
 * @property {string} creator - 创建者
 * @property {string} creatorName - 创建者姓名
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 */

/**
 * 测验题目
 * @typedef {Object} QuizQuestion
 * @property {number} id - 题目ID
 * @property {number} quizId - 测验ID
 * @property {string} questionText - 题目内容
 * @property {string} questionType - 题目类型
 * @property {number} score - 分值
 * @property {number} order - 排序
 * @property {Array<QuizOption>} options - 选项列表
 * @property {string} explanation - 解释说明
 */

/**
 * 测验选项
 * @typedef {Object} QuizOption
 * @property {number} id - 选项ID
 * @property {number} questionId - 题目ID
 * @property {string} optionText - 选项内容
 * @property {boolean} isCorrect - 是否正确答案
 * @property {number} order - 排序
 */

/**
 * 测验尝试记录
 * @typedef {Object} QuizAttempt
 * @property {number} id - 记录ID
 * @property {number} quizId - 测验ID
 * @property {string} quizTitle - 测验标题
 * @property {number} userId - 用户ID
 * @property {string} username - 用户名
 * @property {string} userName - 用户姓名
 * @property {number} score - 得分
 * @property {number} totalScore - 总分
 * @property {boolean} passed - 是否通过
 * @property {string} startTime - 开始时间
 * @property {string} endTime - 结束时间
 * @property {number} duration - 用时(秒)
 * @property {string} status - 状态
 * @property {Array<QuizAnswer>} answers - 答案列表
 */

/**
 * 测验答案
 * @typedef {Object} QuizAnswer
 * @property {number} questionId - 题目ID
 * @property {Array<string>} userAnswers - 用户答案
 * @property {Array<string>} correctAnswers - 正确答案
 * @property {boolean} isCorrect - 是否正确
 * @property {number} score - 得分
 */

/**
 * 查询测验列表
 * @param {QuizQueryParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/common.js').PageResult<QuizDto>>>} 测验列表
 */
export function queryQuizzes(params) {
  return request.get('/quizzes/query', params)
}

/**
 * 获取测验详情
 * @param {number} quizId - 测验ID
 * @returns {Promise<import('./types/common.js').ApiResult<QuizDto>>} 测验详情
 */
export function getQuizDetail(quizId) {
  return request.get(`/quizzes/${quizId}`)
}

/**
 * 创建测验
 * @param {CreateQuizDto} data - 测验数据
 * @returns {Promise<import('./types/common.js').ApiResult<QuizDto>>} 创建结果
 */
export function createQuiz(data) {
  return request.post('/quizzes/create', data)
}

/**
 * 更新测验
 * @param {number} quizId - 测验ID
 * @param {Partial<CreateQuizDto>} data - 更新数据
 * @returns {Promise<import('./types/common.js').ApiResult<QuizDto>>} 更新结果
 */
export function updateQuiz(quizId, data) {
  return request.put(`/quizzes/${quizId}`, data)
}

/**
 * 删除测验
 * @param {number} quizId - 测验ID
 * @returns {Promise<import('./types/common.js').ApiResult<boolean>>} 删除结果
 */
export function deleteQuiz(quizId) {
  return request.delete(`/quizzes/${quizId}`)
}

/**
 * 批量删除测验
 * @param {Array<number>} quizIds - 测验ID列表
 * @returns {Promise<import('./types/common.js').ApiResult<boolean>>} 删除结果
 */
export function batchDeleteQuizzes(quizIds) {
  return request.post('/quizzes/batch-delete', { quizIds })
}

/**
 * 启用/禁用测验
 * @param {number} quizId - 测验ID
 * @param {string} status - 状态 (active/inactive)
 * @returns {Promise<import('./types/common.js').ApiResult<QuizDto>>} 更新结果
 */
export function toggleQuizStatus(quizId, status) {
  return request.post(`/quizzes/${quizId}/toggle-status`, { status })
}

/**
 * 获取测验题目列表
 * @param {number} quizId - 测验ID
 * @returns {Promise<import('./types/common.js').ApiResult<Array<QuizQuestion>>>} 题目列表
 */
export function getQuizQuestions(quizId) {
  return request.get(`/quizzes/${quizId}/questions`)
}

/**
 * 开始测验
 * @param {number} quizId - 测验ID
 * @returns {Promise<import('./types/common.js').ApiResult<{attemptId: number, questions: Array<QuizQuestion>, timeLimit: number}>>} 开始结果
 */
export function startQuiz(quizId) {
  return request.post(`/quizzes/${quizId}/start`)
}

/**
 * 提交测验答案
 * @param {number} quizId - 测验ID
 * @param {Object} data - 提交数据
 * @param {number} data.attemptId - 尝试ID
 * @param {Array<{questionId: number, answers: Array<string>}>} data.answers - 答案列表
 * @returns {Promise<import('./types/common.js').ApiResult<QuizAttempt>>} 提交结果
 */
export function submitQuizAnswers(quizId, data) {
  return request.post(`/quizzes/${quizId}/submit`, data)
}

/**
 * 获取测验答题记录
 * @param {Object} params - 查询参数
 * @param {number} [params.quizId] - 测验ID
 * @param {number} [params.userId] - 用户ID
 * @param {string} [params.startTime] - 开始时间
 * @param {string} [params.endTime] - 结束时间
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/common.js').PageResult<QuizAttempt>>>} 答题记录列表
 */
export function getQuizAttempts(params) {
  return request.get('/quizzes/attempts', params)
}

/**
 * 获取用户测验记录
 * @param {number} quizId - 测验ID
 * @returns {Promise<import('./types/common.js').ApiResult<Array<QuizAttempt>>>} 用户测验记录
 */
export function getUserQuizAttempts(quizId) {
  return request.get(`/quizzes/${quizId}/my-attempts`)
}

/**
 * 获取测验统计信息
 * @param {number} [quizId] - 测验ID（不传则获取全部统计）
 * @returns {Promise<import('./types/common.js').ApiResult<{totalQuizzes: number, totalAttempts: number, avgScore: number, passRate: number, topQuizzes: Array}>>} 统计信息
 */
export function getQuizStats(quizId) {
  const url = quizId ? `/quizzes/${quizId}/stats` : '/quizzes/stats'
  return request.get(url)
}

/**
 * 导出测验数据
 * @param {QuizQueryParams} params - 查询参数
 * @returns {Promise<Blob>} 导出文件
 */
export function exportQuizzes(params) {
  return request.post('/quizzes/export', params, {
    responseType: 'blob'
  })
}

/**
 * 导出测验答题记录
 * @param {Object} params - 查询参数
 * @returns {Promise<Blob>} 导出文件
 */
export function exportQuizAttempts(params) {
  return request.post('/quizzes/attempts/export', params, {
    responseType: 'blob'
  })
}

/**
 * 复制测验
 * @param {number} quizId - 测验ID
 * @param {string} newTitle - 新测验标题
 * @returns {Promise<import('./types/common.js').ApiResult<QuizDto>>} 复制结果
 */
export function copyQuiz(quizId, newTitle) {
  return request.post(`/quizzes/${quizId}/copy`, { newTitle })
}

// 默认导出所有测验相关API
export default {
  queryQuizzes,
  getQuizDetail,
  createQuiz,
  updateQuiz,
  deleteQuiz,
  batchDeleteQuizzes,
  toggleQuizStatus,
  getQuizQuestions,
  startQuiz,
  submitQuizAnswers,
  getQuizAttempts,
  getUserQuizAttempts,
  getQuizStats,
  exportQuizzes,
  exportQuizAttempts,
  copyQuiz
}
