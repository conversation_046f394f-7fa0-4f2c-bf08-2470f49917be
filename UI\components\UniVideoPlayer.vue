<template>
  <view class="video-container" :class="{ 'fullscreen': isFullscreen }">
    <!-- uni-app video 组件 -->
    <video
      :id="videoId"
      ref="videoRef"
      :src="src"
      :poster="poster"
      class="video-element"
      :controls="true"
      :autoplay="autoplay"
      :muted="false"
      :show-progress="!disableProgressDrag"
      :show-fullscreen-btn="true"
      :show-play-btn="true"
      :show-center-play-btn="true"
      :enable-progress-gesture="!disableProgressDrag"
      :page-gesture="false"
      :direction="0"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
      @ended="onEnded"
      @play="onPlay"
      @pause="onPause"
      @fullscreenchange="onFullscreenChange"
      @waiting="onWaiting"
      @canplay="onCanPlay"
    ></video>

    <!-- 防快进遮罩层 -->
    <view 
      v-if="disableSeek" 
      class="seek-blocker"
      @touchstart="blockSeek"
      @touchmove="blockSeek"
      @touchend="blockSeek"
      @click="blockSeek"
    ></view>

    <!-- 加载状态 -->
    <view class="loading-overlay" v-if="isLoading">
      <view class="loading-spinner">
        <view class="spinner"></view>
      </view>
      <text class="loading-text">{{ isBuffering ? '缓冲中...' : '加载中...' }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniVideoPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    poster: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    disableSeek: {
      type: Boolean,
      default: false
    },
    disableProgressDrag: {
      type: Boolean,
      default: false
    },
    showRate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      videoId: 'video-' + Date.now(),
      videoContext: null,
      isPlaying: false,
      isFullscreen: false,
      isLoading: true,
      isBuffering: false,
      currentTime: 0,
      duration: 0,
      maxWatchedTime: 0,
      lastValidTime: 0
    }
  },
  mounted() {
    this.initPlayer()
  },
  methods: {
    initPlayer() {
      this.$nextTick(() => {
        // 创建 video 上下文
        this.videoContext = uni.createVideoContext(this.videoId, this)
        console.log('视频播放器初始化完成')
      })
    },

    // 事件处理
    onLoadedMetadata(e) {
      console.log('视频元数据加载完成:', e.detail)
      this.duration = e.detail.duration || 0
      this.isLoading = false
    },

    onTimeUpdate(e) {
      const detail = e.detail
      this.currentTime = detail.currentTime || 0
      this.duration = detail.duration || this.duration

      // 更新最大观看时间
      if (this.currentTime > this.maxWatchedTime) {
        this.maxWatchedTime = this.currentTime
        this.lastValidTime = this.currentTime
      }

      // 防快进检查
      if (this.disableSeek && this.currentTime > this.maxWatchedTime + 2) {
        this.seek(this.lastValidTime)
        uni.showToast({
          title: '请完整观看视频',
          icon: 'none'
        })
        return
      }

      this.lastValidTime = this.currentTime

      this.$emit('timeupdate', {
        current: this.currentTime,
        duration: this.duration
      })
    },

    onEnded() {
      this.isPlaying = false
      this.maxWatchedTime = this.duration
      this.$emit('ended')
    },

    onPlay() {
      this.isPlaying = true
      this.isLoading = false
      this.isBuffering = false
    },

    onPause() {
      this.isPlaying = false
    },

    onFullscreenChange(e) {
      this.isFullscreen = e.detail.fullScreen
      
      if (this.isFullscreen) {
        this.lockOrientation()
      } else {
        this.unlockOrientation()
      }
      
      this.$emit('fullscreenchange', this.isFullscreen)
    },

    onWaiting() {
      this.isBuffering = true
    },

    onCanPlay() {
      this.isBuffering = false
      this.isLoading = false
    },

    // 强制横屏
    async lockOrientation() {
      if (screen.orientation && screen.orientation.lock) {
        try {
          await screen.orientation.lock('landscape')
        } catch (error) {
          console.log('横屏锁定失败:', error)
        }
      }
    },

    // 解锁屏幕方向
    unlockOrientation() {
      if (screen.orientation && screen.orientation.unlock) {
        try {
          screen.orientation.unlock()
        } catch (error) {
          console.log('屏幕方向解锁失败:', error)
        }
      }
    },

    // 阻止快进操作
    blockSeek(event) {
      if (this.disableSeek) {
        event.preventDefault()
        event.stopPropagation()
        uni.showToast({
          title: '请完整观看视频',
          icon: 'none'
        })
      }
    },

    // 公共方法
    play() {
      if (this.videoContext) {
        this.videoContext.play()
      }
    },

    pause() {
      if (this.videoContext) {
        this.videoContext.pause()
      }
    },

    seek(time) {
      if (time <= this.maxWatchedTime + 2 && this.videoContext) {
        this.videoContext.seek(time)
      }
    },

    getCurrentTime() {
      return this.currentTime
    },

    toggleFullscreen() {
      if (this.videoContext) {
        if (this.isFullscreen) {
          this.videoContext.exitFullScreen()
        } else {
          this.videoContext.requestFullScreen()
        }
      }
    }
  }
}
</script>

<style scoped>
.video-container {
  position: relative;
  width: 100%;
  height: 422rpx;
  background: #000;
  overflow: hidden;
  border-radius: 8rpx;
}

.video-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.seek-blocker {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60rpx;
  z-index: 10;
  background: transparent;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  z-index: 11;
}

.loading-spinner {
  position: relative;
}

.spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #39BFFD;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: white;
  font-size: 28rpx;
  text-align: center;
}
</style>
