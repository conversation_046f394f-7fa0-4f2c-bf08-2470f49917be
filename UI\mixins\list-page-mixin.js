/**
 * 通用列表页面混入
 * 整合数据加载、用户管理、分页等功能
 */
import { apiCallWrapper, ErrorHandlerPresets } from "../utils/api-error-handler.js";
import { PaginationManager } from "../utils/pagination-helper.js";
import { SmartCache, CACHE_TYPES } from "../utils/cache-manager.js";
import { createSearchDebouncer } from "../utils/pagination-helper.js";

export default {
    data () {
        return {
            // 搜索相关
            searchKeyword: "",
            searchDebouncer: null,

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            totalCount: 0,
            hasMore: true,
            loading: false,
            refreshing: false,
            loadingMore: false,

            // 时间筛选
            activeTimeFilter: "today",
            customDateRange: {
                startDate: "",
                endDate: "",
            },

            // 列表高度
            listHeight: 0,
            windowHeight: 0,
            safeAreaBottom: 0,

            // 数据状态
            dataList: [],
            hasData: false,
            isEmpty: false,
            error: null,
            errorMessage: '',

            // 页面配置
            hasPageHeader: false,
            selectedManagerId: null,
            selectedEmployeeId: null,

            // 选项卡
            activeTab: '',
            tabs: [],

            // 筛选参数
            filterParams: {},
            lastSearchParams: {},
        };
    },

    computed: {
        /**
         * 是否显示加载状态
         */
        showLoading () {
            return this.loading && !this.refreshing;
        },

        /**
         * 是否显示空状态
         */
        showEmpty () {
            return !this.loading && !this.error && this.isEmpty;
        },

        /**
         * 是否显示错误状态
         */
        showError () {
            return !this.loading && this.error;
        },

        /**
         * 是否显示数据列表
         */
        showDataList () {
            return !this.loading && !this.error && this.hasData;
        },

        /**
         * 获取搜索参数
         */
        searchParams () {
            return {
                ...this.filterParams,
                keyword: this.searchKeyword,
                PageIndex: this.currentPage,
                PageSize: this.pageSize
            };
        },

        /**
         * 过滤后的列表
         */
        filteredList () {
            let filtered = this.dataList;

            // 选项卡过滤
            if (this.activeTab && this.tabs.length > 0) {
                filtered = filtered.filter(item => item.type === this.activeTab);
            }

            // 搜索过滤
            if (this.searchKeyword) {
                const keyword = this.searchKeyword.toLowerCase();
                filtered = filtered.filter(item =>
                    (item.name || item.title || item.username || '').toLowerCase().includes(keyword)
                );
            }

            return filtered;
        }
    },

    created () {
        // 初始化搜索防抖
        this.searchDebouncer = createSearchDebouncer(this.handleSearchDebounced, 500);
    },

    methods: {
        /**
         * 初始化数据加载
         * 子类需要重写此方法来设置API调用
         */
        initDataLoading () {
            throw new Error('子类必须实现 initDataLoading 方法');
        },

        /**
         * 获取API调用函数
         * 子类需要重写此方法
         */
        getApiCall () {
            throw new Error('子类必须实现 getApiCall 方法');
        },

        /**
         * 格式化数据
         * 子类可以重写此方法来格式化API返回的数据
         */
        formatData (data) {
            return data;
        },

        /**
         * 加载数据
         */
        async loadData (showLoading = true, isRefresh = false) {
            try {
                if (showLoading) {
                    this.loading = true;
                }
                if (isRefresh) {
                    this.refreshing = true;
                }

                this.error = null;
                this.errorMessage = '';

                const apiCall = this.getApiCall();
                if (!apiCall) {
                    throw new Error('API调用函数未定义');
                }

                const response = await apiCallWrapper(
                    () => apiCall(this.searchParams),
                    {
                        ...ErrorHandlerPresets.quick,
                        showLoading: false,
                        showError: false
                    }
                );

                if (response.success && response.data) {
                    let formattedData = this.formatData(response.data);

                    // 处理分页数据
                    if (Array.isArray(formattedData)) {
                        this.dataList = formattedData;
                    } else if (formattedData.items && Array.isArray(formattedData.items)) {
                        this.dataList = formattedData.items;
                        this.totalCount = formattedData.totalCount || 0;
                        this.hasMore = this.dataList.length < this.totalCount;
                    } else {
                        this.dataList = [];
                    }

                    this.hasData = this.dataList.length > 0;
                    this.isEmpty = this.dataList.length === 0;
                } else {
                    this.dataList = [];
                    this.hasData = false;
                    this.isEmpty = true;
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.error = error;
                this.errorMessage = error.message || '加载数据失败';
                this.hasData = false;
                this.isEmpty = false;
            } finally {
                this.loading = false;
                this.refreshing = false;
            }
        },

        /**
         * 计算列表高度
         */
        calculateListHeight () {
            uni.getSystemInfo({
                success: (res) => {
                    this.windowHeight = res.windowHeight;
                    this.safeAreaBottom = res.safeAreaInsets?.bottom || 0;

                    setTimeout(() => {
                        const query = uni.createSelectorQuery().in(this);
                        query.select(".control-section").boundingClientRect();
                        query.exec((data) => {
                            if (data && data[0]) {
                                const controlHeight = data[0].height || 140;
                                const tabbarHeight = 40;
                                const headerHeight = this.hasPageHeader ? 70 : 0;
                                const padding = 10;

                                this.listHeight = Math.max(
                                    this.windowHeight - controlHeight - tabbarHeight -
                                    this.safeAreaBottom - headerHeight - padding,
                                    550
                                );
                            } else {
                                this.listHeight = this.windowHeight - 200;
                            }
                        });
                    }, 500);
                },
            });
        },

        /**
         * 修复TabBar层级
         */
        fixTabBarZIndex () {
            setTimeout(() => {
                uni.setTabBarStyle({
                    zIndex: 9999,
                });
            }, 100);
        },

        /**
         * 时间筛选变化处理
         */
        handleTimeFilterChange (filter) {
            this.activeTimeFilter = filter;
            this.$emit('time-filter-change', filter);
        },

        /**
         * 自定义日期变化处理
         */
        handleCustomDateChange (dateRange) {
            this.customDateRange = dateRange;
        },

        /**
         * 搜索输入处理
         */
        onSearchInput () {
            if (this.searchDebouncer) {
                this.searchDebouncer();
            }
        },

        /**
         * 搜索防抖处理
         */
        handleSearchDebounced () {
            this.performSearch();
        },

        /**
         * 执行搜索
         */
        async performSearch () {
            // 清除缓存
            SmartCache.clearType(CACHE_TYPES.EMPLOYEE_LIST);
            // 重新加载数据
            await this.loadData(true, true);
        },

        /**
         * 搜索确认处理
         */
        async handleSearch () {
            await this.performSearch();
        },

        /**
         * 清除搜索
         */
        clearSearch () {
            this.searchKeyword = "";
        },

        /**
         * 切换选项卡
         */
        switchTab (tabKey) {
            this.activeTab = tabKey;
            this.$emit('tab-change', tabKey);
        },

        /**
         * 加载更多数据
         */
        async loadMore () {
            if (!this.hasMore || this.loadingMore) return;

            this.loadingMore = true;
            this.currentPage++;

            try {
                await this.loadData(false, false);
            } finally {
                this.loadingMore = false;
            }
        },

        /**
         * 下拉刷新处理
         */
        async handlePullDownRefresh () {
            await this.loadData(false, true);
            uni.stopPullDownRefresh();
        },

        /**
         * 上拉加载更多处理
         */
        handleReachBottom () {
            this.loadMore();
        },

        /**
         * 页面显示处理
         */
        handleShow () {
            this.fixTabBarZIndex();
            this.calculateListHeight();
        },

        /**
         * 重试加载
         */
        async retryLoad () {
            this.error = null;
            this.errorMessage = '';
            await this.loadData(true, true);
        },

        /**
         * 清除数据
         */
        clearData () {
            this.dataList = [];
            this.hasData = false;
            this.isEmpty = false;
            this.currentPage = 1;
            this.hasMore = true;
            this.error = null;
            this.errorMessage = '';
        }
    },

    onLoad () {
        this.initDataLoading();
        this.loadData();
    },

    onShow () {
        this.handleShow();
    },

    onPullDownRefresh () {
        this.handlePullDownRefresh();
    },

    onReachBottom () {
        this.handleReachBottom();
    }
}; 