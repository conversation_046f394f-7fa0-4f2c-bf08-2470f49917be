<template>
	<text :class="customClass">
		{{displayValue}}{{suffix}}
	</text>
</template>

<script>
export default {
	name: 'CountUp',
	props: {
		endValue: {
			type: [Number, String],
			required: true,
			default: 0
		},
		suffix: {
			type: String,
			default: ''
		},
		duration: {
			type: Number,
			default: 1000
		},
		decimals: {
			type: Number,
			default: 0
		},
		customClass: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			displayValue: '0',
			startValue: 0,
			interval: null
		}
	},
	watch: {
		endValue: {
			handler(newValue) {
				if (newValue !== undefined && newValue !== null) {
					this.startAnimation(newValue);
				}
			},
			immediate: true
		}
	},
	methods: {
		startAnimation(newValue) {
			// 清除之前的动画
			if (this.interval) {
				clearInterval(this.interval);
			}
			
			// 确保数值有效
			const endValue = this.parseValue(newValue);
			const startValue = this.parseValue(this.displayValue);
			
			// 如果值相同，不需要动画
			if (startValue === endValue) {
				this.displayValue = this.formatNumber(endValue);
				return;
			}
			
			const duration = this.duration;
			const steps = 20; // 减少步数从30到20，使每步变化更明显
			const stepDuration = Math.min(duration / steps, 25); // 确保每步最快25ms，让动画更流畅
			const increment = (endValue - startValue) / steps;
			
			let currentStep = 0;
			
			this.interval = setInterval(() => {
				currentStep++;
				
				if (currentStep >= steps) {
					// 动画结束，设置最终值
					this.displayValue = this.formatNumber(endValue);
					clearInterval(this.interval);
				} else {
					// 计算当前值
					const currentValue = startValue + (increment * currentStep);
					this.displayValue = this.formatNumber(currentValue);
				}
			}, stepDuration);
		},
		parseValue(value) {
			// 处理字符串或数字输入
			const parsed = parseFloat(value);
			return isNaN(parsed) ? 0 : parsed;
		},
		formatNumber(value) {
			// 根据小数位数格式化数字
			const formatted = Number(value).toFixed(this.decimals);
			return isNaN(formatted) ? '0' : formatted;
		}
	},
	beforeDestroy() {
		// 组件销毁前清除定时器
		if (this.interval) {
			clearInterval(this.interval);
		}
	}
}
</script>

<style>
/* 可以添加自定义样式 */
</style> 