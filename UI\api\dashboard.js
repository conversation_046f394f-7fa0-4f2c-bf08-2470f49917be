/**
 * 仪表板数据相关API
 * 基于新的Dashboard API端点完全重写
 * 支持综合仪表板数据、统计信息、关键指标等功能
 *
 * 更新日期: 2025-01-18
 * API版本: v1.0
 *
 * 包含的API端点:
 * 1. /Dashboard - 获取综合仪表板数据（主接口）
 * 2. /Dashboard/summary - 获取数据汇总
 * 3. /Dashboard/tags - 获取标签统计
 * 4. /Dashboard/course - 获取课程统计
 * 5. /Dashboard/answer - 获取答题统计
 * 6. /Dashboard/reward - 获取红包统计
 * 7. /Dashboard/order - 获取订单统计
 * 8. /Dashboard/today - 获取今日数据快照
 * 9. /Dashboard/thisMonth - 获取本月数据快照
 * 10. /Dashboard/metrics - 获取关键指标摘要
 * 11. /Dashboard/compare/today-yesterday - 获取数据对比（今日vs昨日）
 */

import request from '../utils/request.js'

/**
 * 标签统计数据
 * @typedef {Object} VideoTagStatisticsDto
 * @property {number} tagId - 标签ID
 * @property {string} tagName - 标签名称
 * @property {number} userCount - 用户数量
 * @property {boolean} isUntagged - 是否为未标签用户
 * @property {number} percentage - 百分比
 */

/**
 * 数据汇总
 * @typedef {Object} VideoDashboardSummaryDto
 * @property {number} totalMembers - 会员总数
 * @property {number} todayNewMembers - 今日新增会员
 * @property {number} totalOrders - 订单总数
 * @property {number} todayOrders - 今日订单数
 * @property {number} totalOrderAmount - 订单总金额
 * @property {number} todayOrderAmount - 今日订单金额
 */

/**
 * 课程统计数据
 * @typedef {Object} VideoCourseStatisticsDto
 * @property {number} viewerCount - 观看人数
 * @property {number} completeViewerCount - 完播人数
 * @property {number} completeRate - 完播率
 * @property {number} totalViews - 总观看次数
 * @property {number} totalCompleteViews - 总完播次数
 * @property {number} averageViewDuration - 平均观看时长
 * @property {string} timeRange - 时间范围
 * @property {Object} compare - 对比数据
 */

/**
 * 答题统计数据
 * @typedef {Object} VideoAnswerStatisticsDto
 * @property {number} answerUserCount - 答题用户数
 * @property {number} correctUserCount - 答对用户数
 * @property {number} correctRate - 正确率
 * @property {number} totalAnswerCount - 总答题次数
 * @property {number} correctAnswerCount - 正确答题次数
 * @property {number} averageAnswerTime - 平均答题时间
 * @property {string} timeRange - 时间范围
 * @property {Object} compare - 对比数据
 */

/**
 * 红包统计数据
 * @typedef {Object} VideoRewardStatisticsDto
 * @property {number} answerRewardCount - 答题红包数
 * @property {number} answerRewardAmount - 答题红包金额
 * @property {number} viewRewardCount - 观看红包数
 * @property {number} viewRewardAmount - 观看红包金额
 * @property {number} shareRewardCount - 分享红包数
 * @property {number} shareRewardAmount - 分享红包金额
 * @property {number} totalRewardCount - 总红包数
 * @property {number} totalRewardAmount - 总红包金额
 * @property {number} distributedCount - 已发放数量
 * @property {number} distributedAmount - 已发放金额
 * @property {number} distributionSuccessRate - 发放成功率
 * @property {string} timeRange - 时间范围
 * @property {Object} compare - 对比数据
 */

/**
 * 订单统计数据
 * @typedef {Object} VideoOrderStatisticsDto
 * @property {number} totalCount - 总订单数
 * @property {number} successCount - 成功订单数
 * @property {number} failedCount - 失败订单数
 * @property {number} pendingCount - 待处理订单数
 * @property {number} totalAmount - 总金额
 * @property {number} successAmount - 成功金额
 * @property {number} averageAmount - 平均金额
 * @property {number} successRate - 成功率
 * @property {string} timeRange - 时间范围
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 */

/**
 * 综合仪表板数据
 * @typedef {Object} VideoDashboardDto
 * @property {Object} summary - 数据汇总
 * @property {Array<VideoTagStatisticsDto>} tagStatistics - 标签统计
 * @property {Object} courseStatistics - 课程统计
 * @property {Object} answerStatistics - 答题统计
 * @property {Object} rewardStatistics - 红包统计
 * @property {Object} orderStatistics - 订单统计
 * @property {string} timeRange - 时间范围
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 * @property {string} updateTime - 更新时间
 */

/**
 * 关键指标摘要
 * @typedef {Object} KeyMetricsSummaryDto
 * @property {number} totalMembers - 会员总数
 * @property {number} todayNewMembers - 今日新增会员
 * @property {number} totalOrders - 订单总数
 * @property {number} viewerCount - 观看人数
 * @property {number} completeRate - 完播率
 * @property {number} answerUserCount - 答题用户数
 * @property {number} correctRate - 正确率
 * @property {number} totalRewardAmount - 总红包金额
 * @property {number} untaggedUserCount - 未标签用户数
 * @property {string} timeRange - 时间范围
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 获取综合仪表板数据（主接口）
 * 返回类似截图的完整仪表板数据，包括：
 * - 数据汇总（会员总数、今日新增会员、订单总数）
 * - 标签统计（未指定标签用户数）
 * - 课程统计（观看人数、完播人数、完播率）
 * - 答题统计（答题人数、正确人数、正确率）
 * - 红包统计（答题红包数、答题红包金额）
 * @param {Object} [params] - 查询参数
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<ApiResult<VideoDashboardDto>>} 仪表板数据
 */
export function getDashboard (params = {}) {
  return request.get('/Dashboard', params)
}

/**
 * 获取数据汇总
 * 包括会员总数、今日新增会员、订单总数等关键指标
 * @param {Object} [params] - 查询参数
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<ApiResult<VideoDashboardSummaryDto>>} 数据汇总
 */
export function getDashboardSummary (params = {}) {
  return request.get('/Dashboard/summary', params)
}

/**
 * 获取标签统计
 * 包括各标签的用户数量和未指定标签的用户数量
 * @returns {Promise<ApiResult<Array<VideoTagStatisticsDto>>>} 标签统计数据
 */
export function getTagStatistics () {
  return request.get('/Dashboard/tags')
}

/**
 * 获取课程统计
 * 包括观看人数、完播人数、完播率等
 * @param {Object} [params] - 查询参数
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<ApiResult<VideoCourseStatisticsDto>>} 课程统计数据
 */
export function getCourseStatistics (params = {}) {
  return request.get('/Dashboard/course', params)
}

/**
 * 获取答题统计
 * 包括答题人数、正确人数、正确率等
 * @param {Object} [params] - 查询参数
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<ApiResult<VideoAnswerStatisticsDto>>} 答题统计数据
 */
export function getAnswerStatistics (params = {}) {
  return request.get('/Dashboard/answer', params)
}

/**
 * 获取红包统计
 * 包括红包数量、红包金额等
 * @param {Object} [params] - 查询参数
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<ApiResult<VideoRewardStatisticsDto>>} 红包统计数据
 */
export function getRewardStatistics (params = {}) {
  return request.get('/Dashboard/reward', params)
}

/**
 * 获取订单统计
 * 包括订单数量、订单金额等
 * @param {Object} [params] - 查询参数
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<ApiResult<VideoOrderStatisticsDto>>} 订单统计数据
 */
export function getOrderStatistics (params = {}) {
  return request.get('/Dashboard/order', params)
}

/**
 * 获取今日数据快照
 * 快速获取今日的所有关键数据
 * @returns {Promise<ApiResult<VideoDashboardDto>>} 今日数据
 */
export function getTodayDashboard () {
  return request.get('/Dashboard/today')
}

/**
 * 获取本月数据快照
 * 快速获取本月的所有关键数据
 * @returns {Promise<ApiResult<VideoDashboardDto>>} 本月数据
 */
export function getThisMonthDashboard () {
  return request.get('/Dashboard/thisMonth')
}

/**
 * 获取关键指标摘要
 * 提供最核心的几个指标数据
 * @param {Object} [params] - 查询参数
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<ApiResult<KeyMetricsSummaryDto>>} 关键指标摘要
 */
export function getKeyMetrics (params = {}) {
  return request.get('/Dashboard/metrics', params)
}

/**
 * 获取数据对比（今日vs昨日）
 * @returns {Promise<ApiResult<Object>>} 对比数据
 */
export function getTodayYesterdayCompare () {
  return request.get('/Dashboard/compare/today-yesterday')
}

/**
 * 批量获取所有统计数据
 * 使用新的Dashboard API端点
 * @param {Object} [params] - 查询参数
 * @returns {Promise<Object>} 所有统计数据
 */
export async function getAllStatistics (params = {}) {
  try {
    const [
      dashboard,
      keyMetrics,
      todayData
    ] = await Promise.all([
      getDashboard(params),
      getKeyMetrics(params),
      getTodayDashboard()
    ])

    return {
      dashboard: dashboard.data,
      keyMetrics: keyMetrics.data,
      todayData: todayData.data,
      // 保持向后兼容性
      summary: keyMetrics.data,
      tags: dashboard.data?.tagStatistics || [],
      course: dashboard.data?.courseStatistics || {},
      answer: dashboard.data?.answerStatistics || {},
      reward: dashboard.data?.rewardStatistics || {},
      order: dashboard.data?.orderStatistics || {}
    }
  } catch (error) {
    console.error('批量获取统计数据失败:', error)
    throw error
  }
}

/**
 * 获取时间范围选项
 * @returns {Array<{value: string, label: string}>} 时间范围选项
 */
export function getTimeRangeOptions () {
  return [
    { value: 'today', label: '今日' },
    { value: 'yesterday', label: '昨日' },
    { value: 'thisWeek', label: '本周' },
    { value: 'lastWeek', label: '上周' },
    { value: 'thisMonth', label: '本月' },
    { value: 'lastMonth', label: '上月' },
    { value: 'thisYear', label: '今年' },
    { value: 'custom', label: '自定义' }
  ]
}

/**
 * 格式化统计数据
 * @param {Object} data - 原始数据
 * @returns {Object} 格式化后的数据
 */
export function formatStatisticsData (data) {
  if (!data) return {}

  return {
    ...data,
    // 格式化百分比（API已返回百分比格式的数值，无需再乘100）
    completeRate: data.completeRate ? data.completeRate.toFixed(2) + '%' : '0.00%',
    correctRate: data.correctRate ? data.correctRate.toFixed(2) + '%' : '0.00%',
    successRate: data.successRate ? data.successRate.toFixed(2) + '%' : '0.00%',
    distributionSuccessRate: data.distributionSuccessRate ? data.distributionSuccessRate.toFixed(2) + '%' : '0.00%',

    // 格式化金额
    totalOrderAmount: data.totalOrderAmount ? data.totalOrderAmount.toFixed(2) : '0.00',
    todayOrderAmount: data.todayOrderAmount ? data.todayOrderAmount.toFixed(2) : '0.00',
    totalRewardAmount: data.totalRewardAmount ? data.totalRewardAmount.toFixed(2) : '0.00',
    distributedAmount: data.distributedAmount ? data.distributedAmount.toFixed(2) : '0.00'
  }
}

// 默认导出所有仪表板相关API
export default {
  getDashboard,
  getDashboardSummary,
  getTagStatistics,
  getCourseStatistics,
  getAnswerStatistics,
  getRewardStatistics,
  getOrderStatistics,
  getTodayDashboard,
  getThisMonthDashboard,
  getKeyMetrics,
  getTodayYesterdayCompare,
  getAllStatistics,
  getTimeRangeOptions,
  formatStatisticsData
}
