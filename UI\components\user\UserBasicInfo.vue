<template>
    <view class="user-basic-info">
        <!-- 用户头像 -->
        <view class="user-avatar" :style="{ backgroundImage: `url(${avatarUrl})` }"></view>

        <!-- 用户信息 -->
        <view class="user-info">
            <view class="user-top-row">
                <view class="user-name-container">
                    <text class="user-name">{{ userInfo.username || userInfo.nickname }}</text>
                    <view class="copy-id-btn" @tap.stop="$emit('copy-id')">
                        <text class="copy-id-icon">复制ID</text>
                    </view>
                </view>
                <view class="user-type">
                    <text class="type-tag" :class="getRoleTagClass()">
                        {{ getRoleText() }}
                    </text>
                </view>
            </view>

            <view class="user-phone">{{ userInfo.phone }}</view>

            <!-- 注册和关系信息 -->
            <view class="user-meta-info">
                <view class="meta-item">
                    <text class="meta-label">注册:</text>
                    <text class="meta-value">{{ formatDate(userInfo.registerTime) }}</text>
                </view>

                <view class="meta-item" v-if="userInfo.type === 'employee'">
                    <text class="meta-label">推广:</text>
                    <text class="meta-value">{{ userInfo.userCount || 0 }}人</text>
                </view>

                <view class="meta-item" v-if="userInfo.type === 'agent'">
                    <text class="meta-label">管理:</text>
                    <text class="meta-value">{{ userInfo.employeeCount || userInfo.userCount || 0 }}人</text>
                </view>

                <view class="meta-item" v-if="userInfo.type === 'user' && userInfo.employeeId">
                    <text class="meta-label">所属:</text>
                    <text class="meta-value link-text" @tap="viewEmployee(userInfo.employeeId)">
                        {{ userInfo.employeeName || "员工" }}
                    </text>
                </view>

                <view class="meta-item" v-if="userInfo.type === 'employee' && userInfo.managerId">
                    <text class="meta-label">所属:</text>
                    <text class="meta-value link-text" @tap="viewManager(userInfo.managerId)">
                        {{ userInfo.managerName || "管理" }}
                    </text>
                </view>
            </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-btn" v-if="showActionBtn" @tap="$emit('action')">
            <text class="iconfont" :class="actionIcon"></text>
        </view>
    </view>
</template>

<script>
import mediaCommon from '@/mixins/media-common.js'

export default {
    name: 'UserBasicInfo',
    mixins: [mediaCommon],
    props: {
        userInfo: {
            type: Object,
            required: true
        },
        showActionBtn: {
            type: Boolean,
            default: false
        },
        actionIcon: {
            type: String,
            default: 'icon-more'
        }
    },
    computed: {
        avatarUrl () {
            return this.buildCompleteFileUrl(this.userInfo.avatar) || '/assets/images/default-avatar.png'
        }
    },
    methods: {
        getRoleText () {
            switch (this.userInfo.type) {
                case "agent":
                    return "代理";
                case "employee":
                    return "员工";
                case "user":
                    return "用户";
                default:
                    return "未知";
            }
        },

        getRoleTagClass () {
            switch (this.userInfo.type) {
                case "agent":
                    return "agent-tag";
                case "employee":
                    return "employee-tag";
                case "user":
                    return "user-tag";
                default:
                    return "";
            }
        },

        formatDate (dateString) {
            if (!dateString) return "";
            if (dateString.includes(" ")) {
                return dateString.split(" ")[0];
            }
            return dateString;
        },

        viewEmployee (employeeId) {
            this.$emit('view-employee', employeeId);
        },

        viewManager (managerId) {
            this.$emit('view-manager', managerId);
        }
    }
}
</script>

<style scoped>
.user-basic-info {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;
}

.user-avatar {
    width: 88rpx;
    height: 88rpx;
    border-radius: 8rpx;
    background-size: cover;
    background-position: center;
    margin-right: 24rpx;
    flex-shrink: 0;
    border: 3rpx solid #e8f4ff;
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
    position: relative;
}

.user-avatar::after {
    content: '';
    position: absolute;
    top: -3rpx;
    left: -3rpx;
    right: -3rpx;
    bottom: -3rpx;
    border-radius: 8rpx;
    background: linear-gradient(45deg, #186BFF, #4A90FF);
    z-index: -1;
}

.user-info {
    flex: 1;
}

.user-top-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4rpx;
}

.user-name-container {
    display: flex;
    align-items: center;
}

.user-name {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
    text-shadow: 0 1rpx 2rpx rgba(24, 144, 255, 0.05);
}

.copy-id-btn {
    margin-left: 12rpx;
    padding: 4rpx 12rpx;
    background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
    border: 1rpx solid #bae7ff;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.copy-id-btn:active {
    background: linear-gradient(135deg, #bae7ff, #e8f4ff);
    transform: scale(0.95);
}

.copy-id-icon {
    font-size: 20rpx;
    color: #186BFF;
    font-weight: 500;
}

.user-phone {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 4rpx;
}

.user-meta-info {
    display: flex;
    flex-wrap: wrap;
    margin-top: 4rpx;
}

.meta-item {
    display: flex;
    align-items: center;
    margin-right: 12rpx;
    font-size: 20rpx;
}

.meta-label {
    color: #999;
    margin-right: 4rpx;
}

.meta-value {
    color: #666;
}

.user-type {
    margin-left: 10rpx;
}

.type-tag {
    display: inline-block;
    font-size: 18rpx;
    padding: 2rpx 8rpx;
    border-radius: 16rpx;
}

.agent-tag {
    background-color: #fff7e6;
    color: #fa8c16;
}

.employee-tag {
    background-color: #e6f7ff;
    color: #186BFF;
}

.user-tag {
    background-color: #f6ffed;
    color: #52c41a;
}

.action-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.action-btn .iconfont {
    font-size: 40rpx;
    color: #999;
}

.link-text {
    color: #186BFF;
    font-weight: 500;
    text-decoration: underline;
    text-decoration-color: rgba(24, 107, 255, 0.3);
}
</style>