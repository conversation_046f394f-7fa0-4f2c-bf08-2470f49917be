/**
 * 应用全局类型定义
 */

// 声明全局变量
declare global {
  interface Window {
    APP_CONFIG: {
      UIProjectUrl: string;
      apiTimeout: number;
      debugMode: boolean;
      version: string;
    };
    getAppConfig: (key: string) => any;
    setAppConfig: (key: string, value: any) => void;
  }

  // uni-app 全局变量
  const uni: any;
  const plus: any;
  const wx: any;
  const getCurrentPages: () => any[];
}

// 应用配置类型
export interface AppConfig {
  UIProjectUrl: string;
  apiTimeout: number;
  debugMode: boolean;
  version: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  msg: string;
  code: number;
}

// 分页响应类型
export interface PageResponse<T = any> {
  success: boolean;
  data: {
    records: T[];
    total: number;
    current: number;
    size: number;
    pages: number;
  };
  msg: string;
  code: number;
}

// 用户类型
export interface User {
  id: string | number;
  username: string;
  name: string;
  avatar?: string;
  role: string;
  status: string;
  createTime?: string;
  updateTime?: string;
}

// 视频类型
export interface Video {
  id: string | number;
  title: string;
  description?: string;
  videoUrl: string;
  thumbnail?: string;
  duration?: number;
  status: string;
  createTime?: string;
  updateTime?: string;
}

// 批次类型
export interface Batch {
  id: string | number;
  batchId: string;
  title: string;
  status: string;
  startTime: string;
  endTime: string;
  creator: string;
  videoCount: number;
  totalViews: number;
  participants: number;
  totalReward: number;
  description?: string;
  videoId?: string | number;
  videoTitle?: string;
  videoUrl?: string;
  videoCoverUrl?: string;
  videoDuration?: number;
  questions?: any[];
  statistics?: any;
}

// 观看用户类型
export interface ViewingUser {
  id: string | number;
  name: string;
  avatar?: string;
  viewTime: string;
  progress: number;
  reward: number;
}

export {};
