{"version": 3, "sources": ["../../../../../node_modules/uview-plus/libs/vue.js", "../../../../../node_modules/uview-plus/libs/function/test.js", "../../../../../node_modules/uview-plus/libs/function/digit.js", "../../../../../node_modules/uview-plus/libs/config/config.js", "../../../../../node_modules/uview-plus/libs/function/index.js", "../../../../../node_modules/uview-plus/libs/util/route.js", "../../../../../node_modules/uview-plus/libs/mixin/mixin.js", "../../../../../node_modules/uview-plus/libs/mixin/mpMixin.js", "../../../../../node_modules/uview-plus/libs/function/colorGradient.js", "../../../../../node_modules/uview-plus/libs/function/debounce.js", "../../../../../node_modules/uview-plus/libs/function/throttle.js", "../../../../../node_modules/uview-plus/libs/function/calc.js", "../../../../../node_modules/uview-plus/libs/config/zIndex.js", "../../../../../node_modules/uview-plus/libs/config/color.js", "../../../../../node_modules/uview-plus/libs/luch-request/utils.js", "../../../../../node_modules/uview-plus/libs/luch-request/helpers/buildURL.js", "../../../../../node_modules/uview-plus/libs/luch-request/helpers/isAbsoluteURL.js", "../../../../../node_modules/uview-plus/libs/luch-request/helpers/combineURLs.js", "../../../../../node_modules/uview-plus/libs/luch-request/core/buildFullPath.js", "../../../../../node_modules/uview-plus/libs/luch-request/core/settle.js", "../../../../../node_modules/uview-plus/libs/luch-request/adapters/index.js", "../../../../../node_modules/uview-plus/libs/luch-request/core/dispatchRequest.js", "../../../../../node_modules/uview-plus/libs/luch-request/core/InterceptorManager.js", "../../../../../node_modules/uview-plus/libs/luch-request/core/mergeConfig.js", "../../../../../node_modules/uview-plus/libs/luch-request/core/defaults.js", "../../../../../node_modules/uview-plus/libs/luch-request/utils/clone.js", "../../../../../node_modules/uview-plus/libs/luch-request/core/Request.js", "../../../../../node_modules/uview-plus/libs/luch-request/index.js", "../../../../../node_modules/uview-plus/libs/function/http.js", "../../../../../node_modules/uview-plus/components/u-action-sheet/actionSheet.js", "../../../../../node_modules/uview-plus/components/u-album/album.js", "../../../../../node_modules/uview-plus/components/u-alert/alert.js", "../../../../../node_modules/uview-plus/components/u-avatar/avatar.js", "../../../../../node_modules/uview-plus/components/u-avatar-group/avatarGroup.js", "../../../../../node_modules/uview-plus/components/u-back-top/backtop.js", "../../../../../node_modules/uview-plus/components/u-badge/badge.js", "../../../../../node_modules/uview-plus/components/u-button/button.js", "../../../../../node_modules/uview-plus/components/u-calendar/calendar.js", "../../../../../node_modules/uview-plus/components/u-car-keyboard/carKeyboard.js", "../../../../../node_modules/uview-plus/components/u-card/card.js", "../../../../../node_modules/uview-plus/components/u-cell/cell.js", "../../../../../node_modules/uview-plus/components/u-cell-group/cellGroup.js", "../../../../../node_modules/uview-plus/components/u-checkbox/checkbox.js", "../../../../../node_modules/uview-plus/components/u-checkbox-group/checkboxGroup.js", "../../../../../node_modules/uview-plus/components/u-circle-progress/circleProgress.js", "../../../../../node_modules/uview-plus/components/u-code/code.js", "../../../../../node_modules/uview-plus/components/u-code-input/codeInput.js", "../../../../../node_modules/uview-plus/components/u-col/col.js", "../../../../../node_modules/uview-plus/components/u-collapse/collapse.js", "../../../../../node_modules/uview-plus/components/u-collapse-item/collapseItem.js", "../../../../../node_modules/uview-plus/components/u-column-notice/columnNotice.js", "../../../../../node_modules/uview-plus/components/u-count-down/countDown.js", "../../../../../node_modules/uview-plus/components/u-count-to/countTo.js", "../../../../../node_modules/uview-plus/components/u-datetime-picker/datetimePicker.js", "../../../../../node_modules/uview-plus/components/u-divider/divider.js", "../../../../../node_modules/uview-plus/components/u-empty/empty.js", "../../../../../node_modules/uview-plus/components/u-form/form.js", "../../../../../node_modules/uview-plus/components/u-form-item/formItem.js", "../../../../../node_modules/uview-plus/components/u-gap/gap.js", "../../../../../node_modules/uview-plus/components/u-grid/grid.js", "../../../../../node_modules/uview-plus/components/u-grid-item/gridItem.js", "../../../../../node_modules/uview-plus/components/u-icon/icon.js", "../../../../../node_modules/uview-plus/components/u-image/image.js", "../../../../../node_modules/uview-plus/components/u-index-anchor/indexAnchor.js", "../../../../../node_modules/uview-plus/components/u-index-list/indexList.js", "../../../../../node_modules/uview-plus/components/u-input/input.js", "../../../../../node_modules/uview-plus/components/u-keyboard/keyboard.js", "../../../../../node_modules/uview-plus/components/u-line/line.js", "../../../../../node_modules/uview-plus/components/u-line-progress/lineProgress.js", "../../../../../node_modules/uview-plus/components/u-link/link.js", "../../../../../node_modules/uview-plus/components/u-list/list.js", "../../../../../node_modules/uview-plus/components/u-list-item/listItem.js", "../../../../../node_modules/uview-plus/components/u-loading-icon/loadingIcon.js", "../../../../../node_modules/uview-plus/components/u-loading-page/loadingPage.js", "../../../../../node_modules/uview-plus/components/u-loadmore/loadmore.js", "../../../../../node_modules/uview-plus/components/u-modal/modal.js", "../../../../../node_modules/uview-plus/components/u-navbar/navbar.js", "../../../../../node_modules/uview-plus/components/u-no-network/noNetwork.js", "../../../../../node_modules/uview-plus/components/u-notice-bar/noticeBar.js", "../../../../../node_modules/uview-plus/components/u-notify/notify.js", "../../../../../node_modules/uview-plus/components/u-number-box/numberBox.js", "../../../../../node_modules/uview-plus/components/u-number-keyboard/numberKeyboard.js", "../../../../../node_modules/uview-plus/components/u-overlay/overlay.js", "../../../../../node_modules/uview-plus/components/u-parse/parse.js", "../../../../../node_modules/uview-plus/components/u-picker/picker.js", "../../../../../node_modules/uview-plus/components/u-popup/popup.js", "../../../../../node_modules/uview-plus/components/u-radio/radio.js", "../../../../../node_modules/uview-plus/components/u-radio-group/radioGroup.js", "../../../../../node_modules/uview-plus/components/u-rate/rate.js", "../../../../../node_modules/uview-plus/components/u-read-more/readMore.js", "../../../../../node_modules/uview-plus/components/u-row/row.js", "../../../../../node_modules/uview-plus/components/u-row-notice/rowNotice.js", "../../../../../node_modules/uview-plus/components/u-scroll-list/scrollList.js", "../../../../../node_modules/uview-plus/components/u-search/search.js", "../../../../../node_modules/uview-plus/components/u-section/section.js", "../../../../../node_modules/uview-plus/components/u-skeleton/skeleton.js", "../../../../../node_modules/uview-plus/components/u-slider/slider.js", "../../../../../node_modules/uview-plus/components/u-status-bar/statusBar.js", "../../../../../node_modules/uview-plus/components/u-steps/steps.js", "../../../../../node_modules/uview-plus/components/u-steps-item/stepsItem.js", "../../../../../node_modules/uview-plus/components/u-sticky/sticky.js", "../../../../../node_modules/uview-plus/components/u-subsection/subsection.js", "../../../../../node_modules/uview-plus/components/u-swipe-action/swipeAction.js", "../../../../../node_modules/uview-plus/components/u-swipe-action-item/swipeActionItem.js", "../../../../../node_modules/uview-plus/components/u-swiper/swiper.js", "../../../../../node_modules/uview-plus/components/u-swiper-indicator/swipterIndicator.js", "../../../../../node_modules/uview-plus/components/u-switch/switch.js", "../../../../../node_modules/uview-plus/components/u-tabbar/tabbar.js", "../../../../../node_modules/uview-plus/components/u-tabbar-item/tabbarItem.js", "../../../../../node_modules/uview-plus/components/u-tabs/tabs.js", "../../../../../node_modules/uview-plus/components/u-tag/tag.js", "../../../../../node_modules/uview-plus/components/u-text/text.js", "../../../../../node_modules/uview-plus/components/u-textarea/textarea.js", "../../../../../node_modules/uview-plus/components/u-toast/toast.js", "../../../../../node_modules/uview-plus/components/u-toolbar/toolbar.js", "../../../../../node_modules/uview-plus/components/u-tooltip/tooltip.js", "../../../../../node_modules/uview-plus/components/u-transition/transition.js", "../../../../../node_modules/uview-plus/components/u-upload/upload.js", "../../../../../node_modules/uview-plus/libs/config/props.js", "../../../../../node_modules/uview-plus/libs/function/platform.js", "../../../../../node_modules/uview-plus/components/u-icon/util.js", "../../../../../node_modules/uview-plus/index.js"], "sourcesContent": ["export const defineMixin = (options) => {\n  return options\n}\n", "/**\r\n * 验证电子邮箱格式\r\n */\r\nexport function email(value) {\r\n    return /^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证手机格式\r\n */\r\nexport function mobile(value) {\r\n    return /^1[23456789]\\d{9}$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证URL格式\r\n */\r\nexport function url(value) {\r\n    return /^((https|http|ftp|rtsp|mms):\\/\\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\\/?)|(\\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\\/?)$/\r\n        .test(value)\r\n}\r\n\r\n/**\r\n * 验证日期格式\r\n * @param {number | string} value yyyy-mm-dd hh:mm:ss 或 时间戳\r\n */\r\nexport function date(value) {\r\n    if (!value) return false;\r\n    // number类型，判断是否是时间戳\r\n    if (typeof value === \"number\") {\r\n        // len === 10 秒级时间戳 len === 13 毫秒级时间戳\r\n        if (value.toString().length !== 10 && value.toString().length !== 13) {\r\n            return false;\r\n        }\r\n        return !isNaN(new Date(value).getTime());\r\n    }\r\n    if (typeof value === \"string\") {\r\n        // 是否为string类型时间戳\r\n        const numV = Number(value);\r\n        if (!isNaN(numV)) {\r\n            if (\r\n                numV.toString().length === 10 ||\r\n                numV.toString().length === 13\r\n            ) {\r\n                return !isNaN(new Date(numV).getTime());\r\n            }\r\n        }\r\n        // 非时间戳，且长度在yyyy-mm-dd 至 yyyy-mm-dd hh:mm:ss 之间\r\n        if (value.length < 10 || value.length > 19) {\r\n            return false;\r\n        }\r\n        const dateRegex =\r\n            /^\\d{4}[-\\/]\\d{2}[-\\/]\\d{2}( \\d{1,2}:\\d{2}(:\\d{2})?)?$/;\r\n        if (!dateRegex.test(value)) {\r\n            return false;\r\n        }\r\n        // 检查是否为有效日期\r\n        const dateValue = new Date(value);\r\n        return !isNaN(dateValue.getTime());\r\n    }\r\n    // 非number和string类型，不做校验\r\n    return false;\r\n}\r\n\r\n/**\r\n * 验证ISO类型的日期格式\r\n */\r\nexport function dateISO(value) {\r\n    return /^\\d{4}[\\/\\-](0?[1-9]|1[012])[\\/\\-](0?[1-9]|[12][0-9]|3[01])$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证十进制数字\r\n */\r\nexport function number(value) {\r\n    return /^[\\+-]?(\\d+\\.?\\d*|\\.\\d+|\\d\\.\\d+e\\+\\d+)$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证字符串\r\n */\r\nexport function string(value) {\r\n    return typeof value === 'string'\r\n}\r\n\r\n/**\r\n * 验证整数\r\n */\r\nexport function digits(value) {\r\n    return /^\\d+$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证身份证号码\r\n */\r\nexport function idCard(value) {\r\n    return /^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X)$/.test(\r\n        value\r\n    )\r\n}\r\n\r\n/**\r\n * 是否车牌号\r\n */\r\nexport function carNo(value) {\r\n    // 新能源车牌\r\n    const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/\r\n    // 旧车牌\r\n    const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/\r\n    if (value.length === 7) {\r\n        return creg.test(value)\r\n    } if (value.length === 8) {\r\n        return xreg.test(value)\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 金额,只允许2位小数\r\n */\r\nexport function amount(value) {\r\n    // 金额，只允许保留两位小数\r\n    return /^[1-9]\\d*(,\\d{3})*(\\.\\d{1,2})?$|^0\\.\\d{1,2}$/.test(value)\r\n}\r\n\r\n/**\r\n * 中文\r\n */\r\nexport function chinese(value) {\r\n    const reg = /^[\\u4e00-\\u9fa5]+$/gi\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 只能输入字母\r\n */\r\nexport function letter(value) {\r\n    return /^[a-zA-Z]*$/.test(value)\r\n}\r\n\r\n/**\r\n * 只能是字母或者数字\r\n */\r\nexport function enOrNum(value) {\r\n    // 英文或者数字\r\n    const reg = /^[0-9a-zA-Z]*$/g\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 验证是否包含某个值\r\n */\r\nexport function contains(value, param) {\r\n    return value.indexOf(param) >= 0\r\n}\r\n\r\n/**\r\n * 验证一个值范围[min, max]\r\n */\r\nexport function range(value, param) {\r\n    return value >= param[0] && value <= param[1]\r\n}\r\n\r\n/**\r\n * 验证一个长度范围[min, max]\r\n */\r\nexport function rangeLength(value, param) {\r\n    return value.length >= param[0] && value.length <= param[1]\r\n}\r\n\r\n/**\r\n * 是否固定电话\r\n */\r\nexport function landline(value) {\r\n    const reg = /^\\d{3,4}-\\d{7,8}(-\\d{3,4})?$/\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 判断是否为空\r\n */\r\nexport function empty(value) {\r\n    switch (typeof value) {\r\n    case 'undefined':\r\n        return true\r\n    case 'string':\r\n        if (value.replace(/(^[ \\t\\n\\r]*)|([ \\t\\n\\r]*$)/g, '').length == 0) return true\r\n        break\r\n    case 'boolean':\r\n        if (!value) return true\r\n        break\r\n    case 'number':\r\n        if (value === 0 || isNaN(value)) return true\r\n        break\r\n    case 'object':\r\n        if (value === null || value.length === 0) return true\r\n        for (const i in value) {\r\n            return false\r\n        }\r\n        return true\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 是否json字符串\r\n */\r\nexport function jsonString(value) {\r\n    if (typeof value === 'string') {\r\n        try {\r\n            const obj = JSON.parse(value)\r\n            if (typeof obj === 'object' && obj) {\r\n                return true\r\n            }\r\n            return false\r\n        } catch (e) {\r\n            return false\r\n        }\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 是否数组\r\n */\r\nexport function array(value) {\r\n    if (typeof Array.isArray === 'function') {\r\n        return Array.isArray(value)\r\n    }\r\n    return Object.prototype.toString.call(value) === '[object Array]'\r\n}\r\n\r\n/**\r\n * 是否对象\r\n */\r\nexport function object(value) {\r\n    return Object.prototype.toString.call(value) === '[object Object]'\r\n}\r\n\r\n/**\r\n * 是否是Promise对象\r\n */\r\nexport function objectPromise(value) {\r\n    return Object.prototype.toString.call(value) === '[object Promise]';\r\n}\r\n\r\n/**\r\n * 是否短信验证码\r\n */\r\nexport function code(value, len = 6) {\r\n    return new RegExp(`^\\\\d{${len}}$`).test(value)\r\n}\r\n\r\n/**\r\n * 是否函数方法\r\n * @param {Object} value\r\n */\r\nexport function func(value) {\r\n    return typeof value === 'function'\r\n}\r\n\r\n/**\r\n * 是否promise对象\r\n * @param {Object} value\r\n */\r\nexport function promise(value) {\r\n    return objectPromise(value) && func(value.then) && func(value.catch)\r\n}\r\n\r\n/** 是否图片格式\r\n * @param {Object} value\r\n */\r\nexport function image(value) {\r\n    const newValue = value.split('?')[0]\r\n    const IMAGE_REGEXP = /\\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i\r\n    return IMAGE_REGEXP.test(newValue)\r\n}\r\n\r\n/**\r\n * 是否视频格式\r\n * @param {Object} value\r\n */\r\nexport function video(value) {\r\n    const VIDEO_REGEXP = /\\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i\r\n    return VIDEO_REGEXP.test(value)\r\n}\r\n\r\n/**\r\n * 是否为正则对象\r\n * @param {Object}\r\n * @return {Boolean}\r\n */\r\nexport function regExp(o) {\r\n    return o && Object.prototype.toString.call(o) === '[object RegExp]'\r\n}\r\n\r\nexport default {\r\n    email,\r\n    mobile,\r\n    url,\r\n    date,\r\n    dateISO,\r\n    number,\r\n    digits,\r\n    idCard,\r\n    carNo,\r\n    amount,\r\n    chinese,\r\n    letter,\r\n    enOrNum,\r\n    contains,\r\n    range,\r\n    rangeLength,\r\n    empty,\r\n    isEmpty: empty,\r\n    jsonString,\r\n    landline,\r\n    object,\r\n    array,\r\n    code,\r\n    func,\r\n    promise,\r\n    video,\r\n    image,\r\n    regExp,\r\n    string\r\n}\r\n", "let _boundaryCheckingState = true; // 是否进行越界检查的全局开关\n\n/**\n * 把错误的数据转正\n * @private\n * @example strip(0.09999999999999998)=0.1\n */\nexport function strip(num, precision = 15) {\n  return +parseFloat(Number(num).toPrecision(precision));\n}\n\n/**\n * Return digits length of a number\n * @private\n * @param {*number} num Input number\n */\nexport function digitLength(num) {\n  // Get digit length of e\n  const eSplit = num.toString().split(/[eE]/);\n  const len = (eSplit[0].split('.')[1] || '').length - +(eSplit[1] || 0);\n  return len > 0 ? len : 0;\n}\n\n/**\n * 把小数转成整数,如果是小数则放大成整数\n * @private\n * @param {*number} num 输入数\n */\nexport function float2Fixed(num) {\n  if (num.toString().indexOf('e') === -1) {\n    return Number(num.toString().replace('.', ''));\n  }\n  const dLen = digitLength(num);\n  return dLen > 0 ? strip(Number(num) * Math.pow(10, dLen)) : Number(num);\n}\n\n/**\n * 检测数字是否越界，如果越界给出提示\n * @private\n * @param {*number} num 输入数\n */\nexport function checkBoundary(num) {\n  if (_boundaryCheckingState) {\n    if (num > Number.MAX_SAFE_INTEGER || num < Number.MIN_SAFE_INTEGER) {\n      console.warn(`${num} 超出了精度限制，结果可能不正确`);\n    }\n  }\n}\n\n/**\n * 把递归操作扁平迭代化\n * @param {number[]} arr 要操作的数字数组\n * @param {function} operation 迭代操作\n * @private\n */\nexport function iteratorOperation(arr, operation) {\n  const [num1, num2, ...others] = arr;\n  let res = operation(num1, num2);\n\n  others.forEach((num) => {\n    res = operation(res, num);\n  });\n\n  return res;\n}\n\n/**\n * 高精度乘法\n * @export\n */\nexport function times(...nums) {\n  if (nums.length > 2) {\n    return iteratorOperation(nums, times);\n  }\n\n  const [num1, num2] = nums;\n  const num1Changed = float2Fixed(num1);\n  const num2Changed = float2Fixed(num2);\n  const baseNum = digitLength(num1) + digitLength(num2);\n  const leftValue = num1Changed * num2Changed;\n\n  checkBoundary(leftValue);\n\n  return leftValue / Math.pow(10, baseNum);\n}\n\n/**\n * 高精度加法\n * @export\n */\nexport function plus(...nums) {\n  if (nums.length > 2) {\n    return iteratorOperation(nums, plus);\n  }\n\n  const [num1, num2] = nums;\n  // 取最大的小数位\n  const baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)));\n  // 把小数都转为整数然后再计算\n  return (times(num1, baseNum) + times(num2, baseNum)) / baseNum;\n}\n\n/**\n * 高精度减法\n * @export\n */\nexport function minus(...nums) {\n  if (nums.length > 2) {\n    return iteratorOperation(nums, minus);\n  }\n\n  const [num1, num2] = nums;\n  const baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)));\n  return (times(num1, baseNum) - times(num2, baseNum)) / baseNum;\n}\n\n/**\n * 高精度除法\n * @export\n */\nexport function divide(...nums) {\n  if (nums.length > 2) {\n    return iteratorOperation(nums, divide);\n  }\n\n  const [num1, num2] = nums;\n  const num1Changed = float2Fixed(num1);\n  const num2Changed = float2Fixed(num2);\n  checkBoundary(num1Changed);\n  checkBoundary(num2Changed);\n  // 重要，这里必须用strip进行修正\n  return times(num1Changed / num2Changed, strip(Math.pow(10, digitLength(num2) - digitLength(num1))));\n}\n\n/**\n * 四舍五入\n * @export\n */\nexport function round(num, ratio) {\n  const base = Math.pow(10, ratio);\n  let result = divide(Math.round(Math.abs(times(num, base))), base);\n  if (num < 0 && result !== 0) {\n    result = times(result, -1);\n  }\n  // 位数不足则补0\n  return result;\n}\n\n/**\n * 是否进行边界检查，默认开启\n * @param flag 标记开关，true 为开启，false 为关闭，默认为 true\n * @export\n */\nexport function enableBoundaryChecking(flag = true) {\n  _boundaryCheckingState = flag;\n}\n\n\nexport default {\n  times,\n  plus,\n  minus,\n  divide,\n  round,\n  enableBoundaryChecking,\n};\n\n", "const version = '3'\n\n// 开发环境才提示，生产环境不会提示\nif (process.env.NODE_ENV === 'development') {\n\tconsole.log(`\\n %c uview-plus V${version} %c https://ijry.github.io/uview-plus/ \\n\\n`, 'color: #ffffff; background: #3c9cff; padding:5px 0;', 'color: #3c9cff;background: #ffffff; padding:5px 0;');\n}\n\nexport default {\n    v: version,\n    version,\n    // 主题名称\n    type: [\n        'primary',\n        'success',\n        'info',\n        'error',\n        'warning'\n    ],\n    // 颜色部分，本来可以通过scss的:export导出供js使用，但是奈何nvue不支持\n    color: {\n        'u-primary': '#2979ff',\n        'u-warning': '#ff9900',\n        'u-success': '#19be6b',\n        'u-error': '#fa3534',\n        'u-info': '#909399',\n        'u-main-color': '#303133',\n        'u-content-color': '#606266',\n        'u-tips-color': '#909399',\n        'u-light-color': '#c0c4cc',\n        'up-primary': '#2979ff',\n        'up-warning': '#ff9900',\n        'up-success': '#19be6b',\n        'up-error': '#fa3534',\n        'up-info': '#909399',\n        'up-main-color': '#303133',\n        'up-content-color': '#606266',\n        'up-tips-color': '#909399',\n        'up-light-color': '#c0c4cc'\n    },\n    // 字体图标地址\n    iconUrl: 'https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf',\n     // 自定义图标\n    customIcon: {\n        family: '',\n        url: ''\n    },\n    customIcons: {}, // 自定义图标与unicode对应关系\n\t// 默认单位，可以通过配置为rpx，那么在用于传入组件大小参数为数值时，就默认为rpx\n\tunit: 'px',\n\t// 拦截器\n\tinterceptor: {\n\t\tnavbarLeftClick: null\n\t}\n}\n", "import {\r\n\tnumber as testNumber,\r\n\tarray as testArray,\r\n\tempty as testEmpty\r\n} from './test'\r\nimport { round } from './digit.js'\r\nimport config from '../config/config';\r\n/**\r\n * @description 如果value小于min，取min；如果value大于max，取max\r\n * @param {number} min \r\n * @param {number} max \r\n * @param {number} value\r\n */\r\nexport function range(min = 0, max = 0, value = 0) {\r\n\treturn Math.max(min, Math.min(max, Number(value)))\r\n}\r\n\r\n/**\r\n * @description 用于获取用户传递值的px值  如果用户传递了\"xxpx\"或者\"xxrpx\"，取出其数值部分，如果是\"xxxrpx\"还需要用过uni.rpx2px进行转换\r\n * @param {number|string} value 用户传递值的px值\r\n * @param {boolean} unit \r\n * @returns {number|string}\r\n */\r\nexport function getPx(value, unit = false) {\r\n\tif (testNumber(value)) {\r\n\t\treturn unit ? `${value}px` : Number(value)\r\n\t}\r\n\t// 如果带有rpx，先取出其数值部分，再转为px值\r\n\tif (/(rpx|upx)$/.test(value)) {\r\n\t\treturn unit ? `${uni.upx2px(parseInt(value))}px` : Number(uni.upx2px(parseInt(value)))\r\n\t}\r\n\treturn unit ? `${parseInt(value)}px` : parseInt(value)\r\n}\r\n\r\n/**\r\n * @description 进行延时，以达到可以简写代码的目的 比如: await uni.$u.sleep(20)将会阻塞20ms\r\n * @param {number} value 堵塞时间 单位ms 毫秒\r\n * @returns {Promise} 返回promise\r\n */\r\nexport function sleep(value = 30) {\r\n\treturn new Promise((resolve) => {\r\n\t\tsetTimeout(() => {\r\n\t\t\tresolve()\r\n\t\t}, value)\r\n\t})\r\n}\r\n/**\r\n * @description 运行期判断平台\r\n * @returns {string} 返回所在平台(小写) \r\n * @link 运行期判断平台 https://uniapp.dcloud.io/frame?id=判断平台\r\n */\r\nexport function os() {\r\n\r\n\treturn uni.getDeviceInfo().platform.toLowerCase()\r\n\r\n\r\n\r\n\r\n}\r\n/**\r\n * @description 获取系统信息同步接口\r\n * @link 获取系统信息同步接口 https://uniapp.dcloud.io/api/system/info?id=getsysteminfosync \r\n */\r\nexport function sys() {\r\n\treturn uni.getSystemInfoSync()\r\n}\r\nexport function getWindowInfo() {\r\n\tlet ret = {}\r\n\r\n\tret = uni.getWindowInfo()\r\n\r\n\r\n\r\n\r\n\treturn ret\r\n}\r\nexport function getDeviceInfo() {\r\n\tlet ret = {}\r\n\r\n\tret = uni.getDeviceInfo()\r\n\r\n\r\n\r\n\r\n\treturn ret\r\n}\r\n\r\n/**\r\n * @description 取一个区间数\r\n * @param {Number} min 最小值\r\n * @param {Number} max 最大值\r\n */\r\nexport function random(min, max) {\r\n\tif (min >= 0 && max > 0 && max >= min) {\r\n\t\tconst gab = max - min + 1\r\n\t\treturn Math.floor(Math.random() * gab + min)\r\n\t}\r\n\treturn 0\r\n}\r\n\r\n/**\r\n * @param {Number} len uuid的长度\r\n * @param {Boolean} firstU 将返回的首字母置为\"u\"\r\n * @param {Nubmer} radix 生成uuid的基数(意味着返回的字符串都是这个基数),2-二进制,8-八进制,10-十进制,16-十六进制\r\n */\r\nexport function guid(len = 32, firstU = true, radix = null) {\r\n\tconst chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')\r\n\tconst uuid = []\r\n\tradix = radix || chars.length\r\n\r\n\tif (len) {\r\n\t\t// 如果指定uuid长度,只是取随机的字符,0|x为位运算,能去掉x的小数位,返回整数位\r\n\t\tfor (let i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]\r\n\t} else {\r\n\t\tlet r\r\n\t\t// rfc4122标准要求返回的uuid中,某些位为固定的字符\r\n\t\tuuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'\r\n\t\tuuid[14] = '4'\r\n\r\n\t\tfor (let i = 0; i < 36; i++) {\r\n\t\t\tif (!uuid[i]) {\r\n\t\t\t\tr = 0 | Math.random() * 16\r\n\t\t\t\tuuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r]\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// 移除第一个字符,并用u替代,因为第一个字符为数值时,该guuid不能用作id或者class\r\n\tif (firstU) {\r\n\t\tuuid.shift()\r\n\t\treturn `u${uuid.join('')}`\r\n\t}\r\n\treturn uuid.join('')\r\n}\r\n\r\n/**\r\n* @description 获取父组件的参数，因为支付宝小程序不支持provide/inject的写法\r\n   this.$parent在非H5中，可以准确获取到父组件，但是在H5中，需要多次this.$parent.$parent.xxx\r\n   这里默认值等于undefined有它的含义，因为最顶层元素(组件)的$parent就是undefined，意味着不传name\r\n   值(默认为undefined)，就是查找最顶层的$parent\r\n*  @param {string|undefined} name 父组件的参数名\r\n*/\r\nexport function $parent(name = undefined) {\r\n\tlet parent = this.$parent\r\n\t// 通过while历遍，这里主要是为了H5需要多层解析的问题\r\n\twhile (parent) {\r\n\t\t// 父组件\r\n        name = name.replace(/up-([a-zA-Z0-9-_]+)/g, 'u-$1')        \r\n\t\tif (parent.$options && parent.$options.name !== name) {\r\n\t\t\t// 如果组件的name不相等，继续上一级寻找\r\n\t\t\tparent = parent.$parent\r\n\t\t} else {\r\n\t\t\treturn parent\r\n\t\t}\r\n\t}\r\n\treturn false\r\n}\r\n\r\n/**\r\n * @description 样式转换\r\n * 对象转字符串，或者字符串转对象\r\n * @param {object | string} customStyle 需要转换的目标\r\n * @param {String} target 转换的目的，object-转为对象，string-转为字符串\r\n * @returns {object|string}\r\n */\r\nexport function addStyle(customStyle, target = 'object') {\r\n\t// 字符串转字符串，对象转对象情形，直接返回\r\n\tif (testEmpty(customStyle) || typeof(customStyle) === 'object' && target === 'object' || target === 'string' &&\r\n\t\ttypeof(customStyle) === 'string') {\r\n\t\treturn customStyle\r\n\t}\r\n\t// 字符串转对象\r\n\tif (target === 'object') {\r\n\t\t// 去除字符串样式中的两端空格(中间的空格不能去掉，比如padding: 20px 0如果去掉了就错了)，空格是无用的\r\n\t\tcustomStyle = trim(customStyle)\r\n\t\t// 根据\";\"将字符串转为数组形式\r\n\t\tconst styleArray = customStyle.split(';')\r\n\t\tconst style = {}\r\n\t\t// 历遍数组，拼接成对象\r\n\t\tfor (let i = 0; i < styleArray.length; i++) {\r\n\t\t\t// 'font-size:20px;color:red;'，如此最后字符串有\";\"的话，会导致styleArray最后一个元素为空字符串，这里需要过滤\r\n\t\t\tif (styleArray[i]) {\r\n\t\t\t\tconst item = styleArray[i].split(':')\r\n\t\t\t\tstyle[trim(item[0])] = trim(item[1])\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn style\r\n\t}\r\n\t// 这里为对象转字符串形式\r\n\tlet string = ''\r\n\tif (typeof customStyle === 'object') {\r\n\t\tcustomStyle.forEach((val, i) => {\r\n\t\t\t// 驼峰转为中划线的形式，否则css内联样式，无法识别驼峰样式属性名\r\n\t\t\tconst key = i.replace(/([A-Z])/g, '-$1').toLowerCase()\r\n\t\t\tstring += `${key}:${val};`\r\n\t\t})\r\n\t}\r\n\t// 去除两端空格\r\n\treturn trim(string)\r\n}\r\n\r\n/**\r\n * @description 添加单位，如果有rpx，upx，%，px等单位结尾或者值为auto，直接返回，否则加上px单位结尾\r\n * @param {string|number} value 需要添加单位的值\r\n * @param {string} unit 添加的单位名 比如px\r\n */\r\nexport function addUnit(value = 'auto', unit = '') {\r\n\tif (!unit) {\r\n\t\tunit = config.unit || 'px'\r\n\t}\r\n\tif (unit == 'rpx' && testNumber(String(value))) {\r\n\t\tvalue = value * 2\r\n\t}\r\n\tvalue = String(value)\r\n\t// 用内置验证规则中的number判断是否为数值\r\n\treturn testNumber(value) ? `${value}${unit}` : value\r\n}\r\n\r\n/**\r\n * @description 深度克隆\r\n * @param {object} obj 需要深度克隆的对象\r\n * @returns {*} 克隆后的对象或者原值（不是对象）\r\n */\r\nexport function deepClone(obj) {\r\n\t// 对常见的“非”值，直接返回原来值\r\n\tif ([null, undefined, NaN, false].includes(obj)) return obj\r\n\tif (typeof obj !== 'object' && typeof obj !== 'function') {\r\n\t\t// 原始类型直接返回\r\n\t\treturn obj\r\n\t}\r\n\tconst o = testArray(obj) ? [] : {}\r\n\tfor (const i in obj) {\r\n\t\tif (obj.hasOwnProperty(i)) {\r\n\t\t\to[i] = typeof obj[i] === 'object' ? deepClone(obj[i]) : obj[i]\r\n\t\t}\r\n\t}\r\n\treturn o\r\n}\r\n\r\n/**\r\n * @description JS对象深度合并\r\n * @param {object} target 需要拷贝的对象\r\n * @param {object} source 拷贝的来源对象\r\n * @returns {object|boolean} 深度合并后的对象或者false（入参有不是对象）\r\n */\r\nexport function deepMerge(targetOrigin = {}, source = {}) {\r\n\tlet target = deepClone(targetOrigin)\r\n\tif (typeof target !== 'object' || typeof source !== 'object') return false\r\n\tfor (const prop in source) {\r\n\t\tif (!source.hasOwnProperty(prop)) continue\r\n\t\tif (prop in target) {\r\n\t\t\tif (source[prop] == null) {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t}else if (typeof target[prop] !== 'object') {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t} else if (typeof source[prop] !== 'object') {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t} else if (target[prop].concat && source[prop].concat) {\r\n\t\t\t\ttarget[prop] = target[prop].concat(source[prop])\r\n\t\t\t} else {\r\n\t\t\t\ttarget[prop] = deepMerge(target[prop], source[prop])\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\ttarget[prop] = source[prop]\r\n\t\t}\r\n\t}\r\n\treturn target\r\n}\r\n/**\r\n * @description JS对象深度合并\r\n * @param {object} target 需要拷贝的对象\r\n * @param {object} source 拷贝的来源对象\r\n * @returns {object|boolean} 深度合并后的对象或者false（入参有不是对象）\r\n */\r\nexport function shallowMerge(target, source = {}) {\r\n\tif (typeof target !== 'object' || typeof source !== 'object') return false\r\n\tfor (const prop in source) {\r\n\t\tif (!source.hasOwnProperty(prop)) continue\r\n\t\tif (prop in target) {\r\n\t\t\tif (source[prop] == null) {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t}else if (typeof target[prop] !== 'object') {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t} else if (typeof source[prop] !== 'object') {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t} else if (target[prop].concat && source[prop].concat) {\r\n\t\t\t\ttarget[prop] = target[prop].concat(source[prop])\r\n\t\t\t} else {\r\n\t\t\t\ttarget[prop] = shallowMerge(target[prop], source[prop])\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\ttarget[prop] = source[prop]\r\n\t\t}\r\n\t}\r\n\treturn target\r\n}\r\n\r\n/**\r\n * @description error提示\r\n * @param {*} err 错误内容\r\n */\r\nexport function error(err) {\r\n\t// 开发环境才提示，生产环境不会提示\r\n\tif (process.env.NODE_ENV === 'development') {\r\n\t\tconsole.error(`uView提示：${err}`)\r\n\t}\r\n}\r\n\r\n/**\r\n * @description 打乱数组\r\n * @param {array} array 需要打乱的数组\r\n * @returns {array} 打乱后的数组\r\n */\r\nexport function randomArray(array = []) {\r\n\t// 原理是sort排序,Math.random()产生0<= x < 1之间的数,会导致x-0.05大于或者小于0\r\n\treturn array.sort(() => Math.random() - 0.5)\r\n}\r\n\r\n// padStart 的 polyfill，因为某些机型或情况，还无法支持es7的padStart，比如电脑版的微信小程序\r\n// 所以这里做一个兼容polyfill的兼容处理\r\nif (!String.prototype.padStart) {\r\n\t// 为了方便表示这里 fillString 用了ES6 的默认参数，不影响理解\r\n\tString.prototype.padStart = function(maxLength, fillString = ' ') {\r\n\t\tif (Object.prototype.toString.call(fillString) !== '[object String]') {\r\n\t\t\tthrow new TypeError(\r\n\t\t\t\t'fillString must be String'\r\n\t\t\t)\r\n\t\t}\r\n\t\tconst str = this\r\n\t\t// 返回 String(str) 这里是为了使返回的值是字符串字面量，在控制台中更符合直觉\r\n\t\tif (str.length >= maxLength) return String(str)\r\n\r\n\t\tconst fillLength = maxLength - str.length\r\n\t\tlet times = Math.ceil(fillLength / fillString.length)\r\n\t\twhile (times >>= 1) {\r\n\t\t\tfillString += fillString\r\n\t\t\tif (times === 1) {\r\n\t\t\t\tfillString += fillString\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn fillString.slice(0, fillLength) + str\r\n\t}\r\n}\r\n\r\n/**\r\n * @description 格式化时间\r\n * @param {String|Number} dateTime 需要格式化的时间戳\r\n * @param {String} fmt 格式化规则 yyyy:mm:dd|yyyy:mm|yyyy年mm月dd日|yyyy年mm月dd日 hh时MM分等,可自定义组合 默认yyyy-mm-dd\r\n * @returns {string} 返回格式化后的字符串\r\n */\r\nexport function timeFormat(dateTime = null, formatStr = 'yyyy-mm-dd') {\r\n  let date\r\n\t// 若传入时间为假值，则取当前时间\r\n  if (!dateTime) {\r\n    date = new Date()\r\n  }\r\n  // 若为unix秒时间戳，则转为毫秒时间戳（逻辑有点奇怪，但不敢改，以保证历史兼容）\r\n  else if (/^\\d{10}$/.test(dateTime.toString().trim())) {\r\n    date = new Date(dateTime * 1000)\r\n  }\r\n  // 若用户传入字符串格式时间戳，new Date无法解析，需做兼容\r\n  else if (typeof dateTime === 'string' && /^\\d+$/.test(dateTime.trim())) {\r\n    date = new Date(Number(dateTime))\r\n  }\r\n  // 其他都认为符合 RFC 2822 规范\r\n  else {\r\n    // 处理平台性差异，在Safari/Webkit中，new Date仅支持/作为分割符的字符串时间\r\n    date = new Date(\r\n      typeof dateTime === 'string'\r\n        ? dateTime.replace(/-/g, '/')\r\n        : dateTime\r\n    )\r\n  }\r\n\r\n\tconst timeSource = {\r\n\t\t'y': date.getFullYear().toString(), // 年\r\n\t\t'm': (date.getMonth() + 1).toString().padStart(2, '0'), // 月\r\n\t\t'd': date.getDate().toString().padStart(2, '0'), // 日\r\n\t\t'h': date.getHours().toString().padStart(2, '0'), // 时\r\n\t\t'M': date.getMinutes().toString().padStart(2, '0'), // 分\r\n\t\t's': date.getSeconds().toString().padStart(2, '0') // 秒\r\n\t\t// 有其他格式化字符需求可以继续添加，必须转化成字符串\r\n\t}\r\n\r\n  for (const key in timeSource) {\r\n    const [ret] = new RegExp(`${key}+`).exec(formatStr) || []\r\n    if (ret) {\r\n      // 年可能只需展示两位\r\n      const beginIndex = key === 'y' && ret.length === 2 ? 2 : 0\r\n      formatStr = formatStr.replace(ret, timeSource[key].slice(beginIndex))\r\n    }\r\n  }\r\n\r\n  return formatStr\r\n}\r\n\r\n/**\r\n * @description 时间戳转为多久之前\r\n * @param {String|Number} timestamp 时间戳\r\n * @param {String|Boolean} format \r\n * 格式化规则如果为时间格式字符串，超出一定时间范围，返回固定的时间格式；\r\n * 如果为布尔值false，无论什么时间，都返回多久以前的格式\r\n * @returns {string} 转化后的内容\r\n */\r\nexport function timeFrom(timestamp = null, format = 'yyyy-mm-dd') {\r\n\tif (timestamp == null) timestamp = Number(new Date())\r\n\ttimestamp = parseInt(timestamp)\r\n\t// 判断用户输入的时间戳是秒还是毫秒,一般前端js获取的时间戳是毫秒(13位),后端传过来的为秒(10位)\r\n\tif (timestamp.toString().length == 10) timestamp *= 1000\r\n\tlet timer = (new Date()).getTime() - timestamp\r\n\ttimer = parseInt(timer / 1000)\r\n\t// 如果小于5分钟,则返回\"刚刚\",其他以此类推\r\n\tlet tips = ''\r\n\tswitch (true) {\r\n\t\tcase timer < 300:\r\n\t\t\ttips = '刚刚'\r\n\t\t\tbreak\r\n\t\tcase timer >= 300 && timer < 3600:\r\n\t\t\ttips = `${parseInt(timer / 60)}分钟前`\r\n\t\t\tbreak\r\n\t\tcase timer >= 3600 && timer < 86400:\r\n\t\t\ttips = `${parseInt(timer / 3600)}小时前`\r\n\t\t\tbreak\r\n\t\tcase timer >= 86400 && timer < 2592000:\r\n\t\t\ttips = `${parseInt(timer / 86400)}天前`\r\n\t\t\tbreak\r\n\t\tdefault:\r\n\t\t\t// 如果format为false，则无论什么时间戳，都显示xx之前\r\n\t\t\tif (format === false) {\r\n\t\t\t\tif (timer >= 2592000 && timer < 365 * 86400) {\r\n\t\t\t\t\ttips = `${parseInt(timer / (86400 * 30))}个月前`\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttips = `${parseInt(timer / (86400 * 365))}年前`\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\ttips = timeFormat(timestamp, format)\r\n\t\t\t}\r\n\t}\r\n\treturn tips\r\n}\r\n\r\n/**\r\n * @description 去除空格\r\n * @param String str 需要去除空格的字符串\r\n * @param String pos both(左右)|left|right|all 默认both\r\n */\r\nexport function trim(str, pos = 'both') {\r\n\tstr = String(str)\r\n\tif (pos == 'both') {\r\n\t\treturn str.replace(/^\\s+|\\s+$/g, '')\r\n\t}\r\n\tif (pos == 'left') {\r\n\t\treturn str.replace(/^\\s*/, '')\r\n\t}\r\n\tif (pos == 'right') {\r\n\t\treturn str.replace(/(\\s*$)/g, '')\r\n\t}\r\n\tif (pos == 'all') {\r\n\t\treturn str.replace(/\\s+/g, '')\r\n\t}\r\n\treturn str\r\n}\r\n\r\n/**\r\n * @description 对象转url参数\r\n * @param {object} data,对象\r\n * @param {Boolean} isPrefix,是否自动加上\"?\"\r\n * @param {string} arrayFormat 规则 indices|brackets|repeat|comma\r\n */\r\nexport function queryParams(data = {}, isPrefix = true, arrayFormat = 'brackets') {\r\n\tconst prefix = isPrefix ? '?' : ''\r\n\tconst _result = []\r\n\tif (['indices', 'brackets', 'repeat', 'comma'].indexOf(arrayFormat) == -1) arrayFormat = 'brackets'\r\n\tfor (const key in data) {\r\n\t\tconst value = data[key]\r\n\t\t// 去掉为空的参数\r\n\t\tif (['', undefined, null].indexOf(value) >= 0) {\r\n\t\t\tcontinue\r\n\t\t}\r\n\t\t// 如果值为数组，另行处理\r\n\t\tif (value.constructor === Array) {\r\n\t\t\t// e.g. {ids: [1, 2, 3]}\r\n\t\t\tswitch (arrayFormat) {\r\n\t\t\t\tcase 'indices':\r\n\t\t\t\t\t// 结果: ids[0]=1&ids[1]=2&ids[2]=3\r\n\t\t\t\t\tfor (let i = 0; i < value.length; i++) {\r\n\t\t\t\t\t\t_result.push(`${key}[${i}]=${value[i]}`)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'brackets':\r\n\t\t\t\t\t// 结果: ids[]=1&ids[]=2&ids[]=3\r\n\t\t\t\t\tvalue.forEach((_value) => {\r\n\t\t\t\t\t\t_result.push(`${key}[]=${_value}`)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'repeat':\r\n\t\t\t\t\t// 结果: ids=1&ids=2&ids=3\r\n\t\t\t\t\tvalue.forEach((_value) => {\r\n\t\t\t\t\t\t_result.push(`${key}=${_value}`)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'comma':\r\n\t\t\t\t\t// 结果: ids=1,2,3\r\n\t\t\t\t\tlet commaStr = ''\r\n\t\t\t\t\tvalue.forEach((_value) => {\r\n\t\t\t\t\t\tcommaStr += (commaStr ? ',' : '') + _value\r\n\t\t\t\t\t})\r\n\t\t\t\t\t_result.push(`${key}=${commaStr}`)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tvalue.forEach((_value) => {\r\n\t\t\t\t\t\t_result.push(`${key}[]=${_value}`)\r\n\t\t\t\t\t})\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t_result.push(`${key}=${value}`)\r\n\t\t}\r\n\t}\r\n\treturn _result.length ? prefix + _result.join('&') : ''\r\n}\r\n\r\n/**\r\n * 显示消息提示框\r\n * @param {String} title 提示的内容，长度与 icon 取值有关。\r\n * @param {Number} duration 提示的延迟时间，单位毫秒，默认：2000\r\n */\r\nexport function toast(title, duration = 2000) {\r\n\tuni.showToast({\r\n\t\ttitle: String(title),\r\n\t\ticon: 'none',\r\n\t\tduration\r\n\t})\r\n}\r\n\r\n/**\r\n * @description 根据主题type值,获取对应的图标\r\n * @param {String} type 主题名称,primary|info|error|warning|success\r\n * @param {boolean} fill 是否使用fill填充实体的图标\r\n */\r\nexport function type2icon(type = 'success', fill = false) {\r\n\t// 如果非预置值,默认为success\r\n\tif (['primary', 'info', 'error', 'warning', 'success'].indexOf(type) == -1) type = 'success'\r\n\tlet iconName = ''\r\n\t// 目前(2019-12-12),info和primary使用同一个图标\r\n\tswitch (type) {\r\n\t\tcase 'primary':\r\n\t\t\ticonName = 'info-circle'\r\n\t\t\tbreak\r\n\t\tcase 'info':\r\n\t\t\ticonName = 'info-circle'\r\n\t\t\tbreak\r\n\t\tcase 'error':\r\n\t\t\ticonName = 'close-circle'\r\n\t\t\tbreak\r\n\t\tcase 'warning':\r\n\t\t\ticonName = 'error-circle'\r\n\t\t\tbreak\r\n\t\tcase 'success':\r\n\t\t\ticonName = 'checkmark-circle'\r\n\t\t\tbreak\r\n\t\tdefault:\r\n\t\t\ticonName = 'checkmark-circle'\r\n\t}\r\n\t// 是否是实体类型,加上-fill,在icon组件库中,实体的类名是后面加-fill的\r\n\tif (fill) iconName += '-fill'\r\n\treturn iconName\r\n}\r\n\r\n/**\r\n * @description 数字格式化\r\n * @param {number|string} number 要格式化的数字\r\n * @param {number} decimals 保留几位小数\r\n * @param {string} decimalPoint 小数点符号\r\n * @param {string} thousandsSeparator 千分位符号\r\n * @returns {string} 格式化后的数字\r\n */\r\nexport function priceFormat(number, decimals = 0, decimalPoint = '.', thousandsSeparator = ',') {\r\n\tnumber = (`${number}`).replace(/[^0-9+-Ee.]/g, '')\r\n\tconst n = !isFinite(+number) ? 0 : +number\r\n\tconst prec = !isFinite(+decimals) ? 0 : Math.abs(decimals)\r\n\tconst sep = (typeof thousandsSeparator === 'undefined') ? ',' : thousandsSeparator\r\n\tconst dec = (typeof decimalPoint === 'undefined') ? '.' : decimalPoint\r\n\tlet s = ''\r\n\r\n\ts = (prec ? round(n, prec) + '' : `${Math.round(n)}`).split('.')\r\n\tconst re = /(-?\\d+)(\\d{3})/\r\n\twhile (re.test(s[0])) {\r\n\t\ts[0] = s[0].replace(re, `$1${sep}$2`)\r\n\t}\r\n\t\r\n\tif ((s[1] || '').length < prec) {\r\n\t\ts[1] = s[1] || ''\r\n\t\ts[1] += new Array(prec - s[1].length + 1).join('0')\r\n\t}\r\n\treturn s.join(dec)\r\n}\r\n\r\n/**\r\n * @description 获取duration值\r\n * 如果带有ms或者s直接返回，如果大于一定值，认为是ms单位，小于一定值，认为是s单位\r\n * 比如以30位阈值，那么300大于30，可以理解为用户想要的是300ms，而不是想花300s去执行一个动画\r\n * @param {String|number} value 比如: \"1s\"|\"100ms\"|1|100\r\n * @param {boolean} unit  提示: 如果是false 默认返回number\r\n * @return {string|number} \r\n */\r\nexport function getDuration(value, unit = true) {\r\n\tconst valueNum = parseInt(value)\r\n\tif (unit) {\r\n\t\tif (/s$/.test(value)) return value\r\n\t\treturn value > 30 ? `${value}ms` : `${value}s`\r\n\t}\r\n\tif (/ms$/.test(value)) return valueNum\r\n\tif (/s$/.test(value)) return valueNum > 30 ? valueNum : valueNum * 1000\r\n\treturn valueNum\r\n}\r\n\r\n/**\r\n * @description 日期的月或日补零操作\r\n * @param {String} value 需要补零的值\r\n */\r\nexport function padZero(value) {\r\n\treturn `00${value}`.slice(-2)\r\n}\r\n\r\n/**\r\n * @description 在u-form的子组件内容发生变化，或者失去焦点时，尝试通知u-form执行校验方法\r\n * @param {*} instance\r\n * @param {*} event\r\n */\r\nexport function formValidate(instance, event) {\r\n\tconst formItem = $parent.call(instance, 'u-form-item')\r\n\tconst form = $parent.call(instance, 'u-form')\r\n\t// 如果发生变化的input或者textarea等，其父组件中有u-form-item或者u-form等，就执行form的validate方法\r\n\t// 同时将form-item的pros传递给form，让其进行精确对象验证\r\n\tif (formItem && form) {\r\n\t\tform.validateField(formItem.prop, () => {}, event)\r\n\t}\r\n}\r\n\r\n/**\r\n * @description 获取某个对象下的属性，用于通过类似'a.b.c'的形式去获取一个对象的的属性的形式\r\n * @param {object} obj 对象\r\n * @param {string} key 需要获取的属性字段\r\n * @returns {*}\r\n */\r\nexport function getProperty(obj, key) {\r\n\tif (typeof obj !== 'object' || null == obj) {\r\n        return ''\r\n    }\r\n\tif (typeof key !== 'string' || key === '') {\r\n\t\treturn ''\r\n\t}\r\n\tif (key.indexOf('.') !== -1) {\r\n\t\tconst keys = key.split('.')\r\n\t\tlet firstObj = obj[keys[0]] || {}\r\n\r\n\t\tfor (let i = 1; i < keys.length; i++) {\r\n\t\t\tif (firstObj) {\r\n\t\t\t\tfirstObj = firstObj[keys[i]]\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn firstObj\r\n\t}\r\n\treturn obj[key]\r\n}\r\n\r\n/**\r\n * @description 设置对象的属性值，如果'a.b.c'的形式进行设置\r\n * @param {object} obj 对象\r\n * @param {string} key 需要设置的属性\r\n * @param {string} value 设置的值\r\n */\r\nexport function setProperty(obj, key, value) {\r\n\tif (typeof obj !== 'object' || null == obj) {\r\n\t\treturn\r\n\t}\r\n\t// 递归赋值\r\n\tconst inFn = function(_obj, keys, v) {\r\n\t\t// 最后一个属性key\r\n\t\tif (keys.length === 1) {\r\n\t\t\t_obj[keys[0]] = v\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// 0~length-1个key\r\n\t\twhile (keys.length > 1) {\r\n\t\t\tconst k = keys[0]\r\n\t\t\tif (!_obj[k] || (typeof _obj[k] !== 'object')) {\r\n\t\t\t\t_obj[k] = {}\r\n\t\t\t}\r\n\t\t\tconst key = keys.shift()\r\n\t\t\t// 自调用判断是否存在属性，不存在则自动创建对象\r\n\t\t\tinFn(_obj[k], keys, v)\r\n\t\t}\r\n\t}\r\n\r\n\tif (typeof key !== 'string' || key === '') {\r\n\r\n\t} else if (key.indexOf('.') !== -1) { // 支持多层级赋值操作\r\n\t\tconst keys = key.split('.')\r\n\t\tinFn(obj, keys, value)\r\n\t} else {\r\n\t\tobj[key] = value\r\n\t}\r\n}\r\n\r\n/**\r\n * @description 获取当前页面路径\r\n */\r\nexport function page() {\r\n\tconst pages = getCurrentPages()\r\n\t// 某些特殊情况下(比如页面进行redirectTo时的一些时机)，pages可能为空数组\r\n\treturn `/${pages[pages.length - 1].route || ''}`\r\n}\r\n\r\n/**\r\n * @description 获取当前路由栈实例数组\r\n */\r\nexport function pages() {\r\n\tconst pages = getCurrentPages()\r\n\treturn pages\r\n}\r\n\r\nexport function getValueByPath(obj, path) {\r\n    // 将路径字符串按 '.' 分割成数组\r\n    const pathArr = path.split('.');\r\n    // 使用 reduce 方法从 obj 开始，逐级访问嵌套属性\r\n    return pathArr.reduce((acc, curr) => {\r\n        // 如果当前累加器（acc）是对象且包含当前键（curr），则返回该键对应的值\r\n        // 否则返回 undefined（表示路径不存在）\r\n        return acc && acc[curr] !== undefined ? acc[curr] : undefined;\r\n    }, obj);\r\n}\r\n\r\n/**\r\n * 生成同色系浅色背景色\r\n * @param {string} textColor - 支持 #RGB、#RRGGBB、rgb()、rgba() 格式\r\n * @param {number} [lightness=85] - 目标亮度百分比（默认85%）\r\n * @returns {string} 十六进制颜色值\r\n */\r\nexport function genLightColor(textColor, lightness = 95) {\r\n\t// 手动解析颜色值（避免使用document）\r\n\tconst rgb = parseColorWithoutDOM(textColor);\r\n\t\r\n\t// RGB转HSL色域\r\n\tconst hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);\r\n\t\r\n\t// 生成浅色背景\r\n\tconst bgHsl = {\r\n\t  h: hsl.h,\r\n\t  s: hsl.s,\r\n\t  l: Math.min(lightness, 95)\r\n\t};\r\n\t\r\n\treturn hslToHex(bgHsl.h, bgHsl.s, bgHsl.l);\r\n  }\r\n  \r\n  /* 手动解析颜色字符串（兼容uni-app环境） */\r\n  function parseColorWithoutDOM(colorStr) {\r\n\t// 统一转小写处理\r\n\tconst str = colorStr.toLowerCase().trim();\r\n\t\r\n\t// 处理十六进制格式\r\n\tif (str.startsWith('#')) {\r\n\t  const hex = str.replace('#', '');\r\n\t  const fullHex = hex.length === 3 ? \r\n\t\thex.split('').map(c => c + c).join('') : hex;\r\n\t\t\r\n\t  return {\r\n\t\tr: parseInt(fullHex.substring(0,2), 16),\r\n\t\tg: parseInt(fullHex.substring(2,4), 16),\r\n\t\tb: parseInt(fullHex.substring(4,6), 16)\r\n\t  };\r\n\t}\r\n\t\r\n\t// 处理rgb/rgba格式\r\n\tconst rgbMatch = str.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\r\n\tif (rgbMatch) {\r\n\t  return {\r\n\t\tr: +rgbMatch[1],\r\n\t\tg: +rgbMatch[2],\r\n\t\tb: +rgbMatch[3]\r\n\t  };\r\n\t}\r\n\t\r\n\tthrow new Error('Invalid color format');\r\n  }\r\n\r\n// 辅助函数：RGB 转 HSL（色相、饱和度、亮度）\r\nfunction rgbToHsl(r, g, b) {\r\n r /= 255, g /= 255, b /= 255;\r\n const max = Math.max(r, g, b), min = Math.min(r, g, b);\r\n let h, s, l = (max + min) / 2;\r\n\r\n if (max === min) {\r\n   h = s = 0; // achromatic\r\n } else {\r\n   const d = max - min;\r\n   s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\r\n   switch (max) {\r\n\t case r: h = (g - b) / d + (g < b ? 6 : 0); break;\r\n\t case g: h = (b - r) / d + 2; break;\r\n\t case b: h = (r - g) / d + 4; break;\r\n   }\r\n   h = (h * 60).toFixed(1);\r\n }\r\n return { h: +h, s: +(s * 100).toFixed(1), l: +(l * 100).toFixed(1) };\r\n}\r\n\r\n// 辅助函数：HSL 转十六进制\r\nfunction hslToHex(h, s, l) {\r\n l /= 100;\r\n const a = s * Math.min(l, 1 - l) / 100;\r\n const f = n => {\r\n   const k = (n + h / 30) % 12;\r\n   const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\r\n   return Math.round(255 * color).toString(16).padStart(2, '0');\r\n };\r\n return `#${f(0)}${f(8)}${f(4)}`;\r\n}\r\n\r\nexport default {\r\n\trange,\r\n\tgetPx,\r\n\tsleep,\r\n\tos,\r\n\tsys,\r\n\tgetWindowInfo,\r\n\trandom,\r\n\tguid,\r\n\t$parent,\r\n\taddStyle,\r\n\taddUnit,\r\n\tdeepClone,\r\n\tdeepMerge,\r\n    shallowMerge,\r\n\terror,\r\n\trandomArray,\r\n\ttimeFormat,\r\n\ttimeFrom,\r\n\ttrim,\r\n\tqueryParams,\r\n\ttoast,\r\n\ttype2icon,\r\n\tpriceFormat,\r\n\tgetDuration,\r\n\tpadZero,\r\n\tformValidate,\r\n\tgetProperty,\r\n\tsetProperty,\r\n\tpage,\r\n\tpages,\r\n\tgetValueByPath,\r\n\tgenLightColor\r\n}\r\n", "/**\n * 路由跳转方法，该方法相对于直接使用uni.xxx的好处是使用更加简单快捷\n * 并且带有路由拦截功能\n */\nimport { queryParams, deepMerge, page } from '../function/index';\nclass Router {\n    constructor() {\n        // 原始属性定义\n        this.config = {\n            type: 'navigateTo',\n            url: '',\n            delta: 1, // navigateBack页面后退时,回退的层数\n            params: {}, // 传递的参数\n            animationType: 'pop-in', // 窗口动画,只在APP有效\n            animationDuration: 300, // 窗口动画持续时间,单位毫秒,只在APP有效\n            intercept: false // 是否需要拦截\n        }\n        // 因为route方法是需要对外赋值给另外的对象使用，同时route内部有使用this，会导致route失去上下文\n        // 这里在构造函数中进行this绑定\n        this.route = this.route.bind(this)\n    }\n\n    // 判断url前面是否有\"/\"，如果没有则加上，否则无法跳转\n    addRootPath(url) {\n        return url[0] === '/' ? url : `/${url}`\n    }\n\n    // 整合路由参数\n    mixinParam(url, params) {\n        url = url && this.addRootPath(url)\n\n        // 使用正则匹配，主要依据是判断是否有\"/\",\"?\",\"=\"等，如“/page/index/index?name=mary\"\n        // 如果有url中有get参数，转换后无需带上\"?\"\n        let query = ''\n        if (/.*\\/.*\\?.*=.*/.test(url)) {\n            // object对象转为get类型的参数\n            query = queryParams(params, false)\n            // 因为已有get参数,所以后面拼接的参数需要带上\"&\"隔开\n            return url += `&${query}`\n        }\n        // 直接拼接参数，因为此处url中没有后面的query参数，也就没有\"?/&\"之类的符号\n        query = queryParams(params)\n        return url += query\n    }\n\n    // 对外的方法名称\n    async route(options = {}, params = {}) {\n        // 合并用户的配置和内部的默认配置\n        let mergeConfig = {}\n\n        if (typeof options === 'string') {\n            // 如果options为字符串，则为route(url, params)的形式\n            mergeConfig.url = this.mixinParam(options, params)\n            mergeConfig.type = 'navigateTo'\n        } else {\n            mergeConfig = deepMerge(this.config, options)\n            // 否则正常使用mergeConfig中的url和params进行拼接\n            mergeConfig.url = this.mixinParam(options.url, options.params)\n        }\n\n        // 如果本次跳转的路径和本页面路径一致，不执行跳转，防止用户快速点击跳转按钮，造成多次跳转同一个页面的问题\n        if (mergeConfig.url === page()) return\n\n        if (params.intercept) {\n            this.config.intercept = params.intercept\n        }\n        // params参数也带给拦截器\n        mergeConfig.params = params\n        // 合并内外部参数\n        mergeConfig = deepMerge(this.config, mergeConfig)\n        // 判断用户是否定义了拦截器\n        if (typeof uni.$u.routeIntercept === 'function') {\n            // 定一个promise，根据用户执行resolve(true)或者resolve(false)来决定是否进行路由跳转\n            const isNext = await new Promise((resolve, reject) => {\n                uni.$u.routeIntercept(mergeConfig, resolve)\n            })\n            // 如果isNext为true，则执行路由跳转\n            isNext && this.openPage(mergeConfig)\n        } else {\n            this.openPage(mergeConfig)\n        }\n    }\n\n    // 执行路由跳转\n    openPage(config) {\n        // 解构参数\n        const {\n            url,\n            type,\n            delta,\n            animationType,\n            animationDuration\n        } = config\n        if (config.type == 'navigateTo' || config.type == 'to') {\n            uni.navigateTo({\n                url,\n                animationType,\n                animationDuration\n            })\n        }\n        if (config.type == 'redirectTo' || config.type == 'redirect') {\n            uni.redirectTo({\n                url\n            })\n        }\n        if (config.type == 'switchTab' || config.type == 'tab') {\n            uni.switchTab({\n                url\n            })\n        }\n        if (config.type == 'reLaunch' || config.type == 'launch') {\n            uni.reLaunch({\n                url\n            })\n        }\n        if (config.type == 'navigateBack' || config.type == 'back') {\n            uni.navigateBack({\n                delta\n            })\n        }\n    }\n}\n\nexport default (new Router()).route\n", "import { defineMixin } from '../vue'\nimport { deepMerge, $parent, sleep } from '../function/index'\nimport test from '../function/test'\nimport route from '../util/route'\n\n\n\n\n\nexport const mixin = defineMixin({\n    // 定义每个组件都可能需要用到的外部样式以及类名\n    props: {\n        // 每个组件都有的父组件传递的样式，可以为字符串或者对象形式\n        customStyle: {\n            type: [Object, String],\n            default: () => ({})\n        },\n        customClass: {\n            type: String,\n            default: ''\n        },\n        // 跳转的页面路径\n        url: {\n            type: String,\n            default: ''\n        },\n        // 页面跳转的类型\n        linkType: {\n            type: String,\n            default: 'navigateTo'\n        }\n    },\n    data() {\n        return {}\n    },\n    onLoad() {\n        // getRect挂载到$u上，因为这方法需要使用in(this)，所以无法把它独立成一个单独的文件导出\n        this.$u.getRect = this.$uGetRect\n    },\n    created() {\n        // 组件当中，只有created声明周期，为了能在组件使用，故也在created中将方法挂载到$u\n        this.$u.getRect = this.$uGetRect\n    },\n    computed: {\n        // 在2.x版本中，将会把$u挂载到uni对象下，导致在模板中无法使用uni.$u.xxx形式\n        // 所以这里通过computed计算属性将其附加到this.$u上，就可以在模板或者js中使用uni.$u.xxx\n        // 只在nvue环境通过此方式引入完整的$u，其他平台会出现性能问题，非nvue则按需引入（主要原因是props过大）\n        $u() {\n\n            // 在非nvue端，移除props，http，mixin等对象，避免在小程序setData时数据过大影响性能\n            return deepMerge(uni.$u, {\n                props: undefined,\n                http: undefined,\n                mixin: undefined\n            })\n\n\n\n\n        },\n        /**\n         * 生成bem规则类名\n         * 由于微信小程序，H5，nvue之间绑定class的差异，无法通过:class=\"[bem()]\"的形式进行同用\n         * 故采用如下折中做法，最后返回的是数组（一般平台）或字符串（支付宝和字节跳动平台），类似['a', 'b', 'c']或'a b c'的形式\n         * @param {String} name 组件名称\n         * @param {Array} fixed 一直会存在的类名\n         * @param {Array} change 会根据变量值为true或者false而出现或者隐藏的类名\n         * @returns {Array|string}\n         */\n        bem() {\n            return function (name, fixed, change) {\n                // 类名前缀\n                const prefix = `u-${name}--`\n                const classes = {}\n                if (fixed) {\n                    fixed.map((item) => {\n                        // 这里的类名，会一直存在\n                        classes[prefix + this[item]] = true\n                    })\n                }\n                if (change) {\n                    change.map((item) => {\n                        // 这里的类名，会根据this[item]的值为true或者false，而进行添加或者移除某一个类\n                        this[item] ? (classes[prefix + item] = this[item]) : (delete classes[prefix + item])\n                    })\n                }\n                return Object.keys(classes)\n                    // 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\n\n\n\n            }\n        }\n    },\n    methods: {\n        // 跳转某一个页面\n        openPage(urlKey = 'url') {\n            const url = this[urlKey]\n            if (url) {\n                // h5官方回应：发行h5会自动摇树优化，所有使用uni的地方，都会被直接转换成具体的API调用 https://ask.dcloud.net.cn/question/161523?notification_id-1201922__rf-false__item_id-226372\n                // 使用封装的 route 进行跳转（直接调用方法），不使用 uni 对象\n                route({ type: this.linkType, url })\n                // 执行类似uni.navigateTo的方法\n                // uni[this.linkType]({\n                //     url\n                // })\n            }\n        },\n        navTo(url = '', linkType = 'navigateTo') {\n            route({ type: this.linkType, url })\n        },\n        // 查询节点信息\n        // 目前此方法在支付宝小程序中无法获取组件跟接点的尺寸，为支付宝的bug(2020-07-21)\n        // 解决办法为在组件根部再套一个没有任何作用的view元素\n        $uGetRect(selector, all) {\n            return new Promise((resolve) => {\n\n                uni.createSelectorQuery()\n                    .in(this)[all ? 'selectAll' : 'select'](selector)\n                    .boundingClientRect((rect) => {\n                        if (all && Array.isArray(rect) && rect.length) {\n                            resolve(rect)\n                        }\n                        if (!all && rect) {\n                            resolve(rect)\n                        }\n                    })\n                    .exec()\n\n                \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n            })\n        },\n        getParentData(parentName = '') {\n            // 避免在created中去定义parent变量\n            if (!this.parent) this.parent = {}\n            // 这里的本质原理是，通过获取父组件实例(也即类似u-radio的父组件u-radio-group的this)\n            // 将父组件this中对应的参数，赋值给本组件(u-radio的this)的parentData对象中对应的属性\n            // 之所以需要这么做，是因为所有端中，头条小程序不支持通过this.parent.xxx去监听父组件参数的变化\n            // 此处并不会自动更新子组件的数据，而是依赖父组件u-radio-group去监听data的变化，手动调用更新子组件的方法去重新获取\n            this.parent = $parent.call(this, parentName)\n            if (this.parent.children) {\n                // 如果父组件的children不存在本组件的实例，才将本实例添加到父组件的children中\n                this.parent.children.indexOf(this) === -1 && this.parent.children.push(this)\n            }\n            if (this.parent && this.parentData) {\n                // 历遍parentData中的属性，将parent中的同名属性赋值给parentData\n                Object.keys(this.parentData).map((key) => {\n                    this.parentData[key] = this.parent[key]\n                })\n            }\n        },\n        // 阻止事件冒泡\n        preventEvent(e) {\n            e && typeof (e.stopPropagation) === 'function' && e.stopPropagation()\n        },\n        // 空操作\n        noop(e) {\n            this.preventEvent(e)\n        }\n    },\n    onReachBottom() {\n        uni.$emit('uOnReachBottom')\n\t},\n\tbeforeUnmount() {\n        // 判断当前页面是否存在parent和chldren，一般在checkbox和checkbox-group父子联动的场景会有此情况\n        // 组件销毁时，移除子组件在父组件children数组中的实例，释放资源，避免数据混乱\n        if (this.parent && test.array(this.parent.children)) {\n            // 组件销毁时，移除父组件中的children数组中对应的实例\n            const childrenList = this.parent.children\n            childrenList.map((child, index) => {\n                // 如果相等，则移除\n                if (child === this) {\n                    childrenList.splice(index, 1)\n                }\n            })\n        }\n    }\n})\n\nexport default mixin\n", "import { defineMixin } from '../vue'\n\nexport const mpMixin = defineMixin({\n\n\n\n\n\n\n})\n\nexport default mpMixin\n\n", "/**\r\n * 求两个颜色之间的渐变值\r\n * @param {string} startColor 开始的颜色\r\n * @param {string} endColor 结束的颜色\r\n * @param {number} step 颜色等分的份额\r\n * */\r\nexport function colorGradient(startColor = 'rgb(0, 0, 0)', endColor = 'rgb(255, 255, 255)', step = 10) {\r\n    const startRGB = hexToRgb(startColor, false) // 转换为rgb数组模式\r\n    const startR = startRGB[0]\r\n    const startG = startRGB[1]\r\n    const startB = startRGB[2]\r\n\r\n    const endRGB = hexToRgb(endColor, false)\r\n    const endR = endRGB[0]\r\n    const endG = endRGB[1]\r\n    const endB = endRGB[2]\r\n\r\n    const sR = (endR - startR) / step // 总差值\r\n    const sG = (endG - startG) / step\r\n    const sB = (endB - startB) / step\r\n    const colorArr = []\r\n    for (let i = 0; i < step; i++) {\r\n        // 计算每一步的hex值\r\n        let hex = rgbToHex(`rgb(${Math.round((sR * i + startR))},${Math.round((sG * i + startG))},${Math.round((sB\r\n\t\t\t* i + startB))})`)\r\n        // 确保第一个颜色值为startColor的值\r\n        if (i === 0) hex = rgbToHex(startColor)\r\n        // 确保最后一个颜色值为endColor的值\r\n        if (i === step - 1) hex = rgbToHex(endColor)\r\n        colorArr.push(hex)\r\n    }\r\n    return colorArr\r\n}\r\n\r\n// 将hex表示方式转换为rgb表示方式(这里返回rgb数组模式)\r\nexport function hexToRgb(sColor, str = true) {\r\n    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n    sColor = String(sColor).toLowerCase()\r\n    if (sColor && reg.test(sColor)) {\r\n        if (sColor.length === 4) {\r\n            let sColorNew = '#'\r\n            for (let i = 1; i < 4; i += 1) {\r\n                sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))\r\n            }\r\n            sColor = sColorNew\r\n        }\r\n        // 处理六位的颜色值\r\n        const sColorChange = []\r\n        for (let i = 1; i < 7; i += 2) {\r\n            sColorChange.push(parseInt(`0x${sColor.slice(i, i + 2)}`))\r\n        }\r\n        if (!str) {\r\n            return sColorChange\r\n        }\r\n        return `rgb(${sColorChange[0]},${sColorChange[1]},${sColorChange[2]})`\r\n    } if (/^(rgb|RGB)/.test(sColor)) {\r\n        const arr = sColor.replace(/(?:\\(|\\)|rgb|RGB)*/g, '').split(',')\r\n        return arr.map((val) => Number(val))\r\n    }\r\n    return sColor\r\n}\r\n\r\n// 将rgb表示方式转换为hex表示方式\r\nexport function rgbToHex(rgb) {\r\n    const _this = rgb\r\n    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n    if (/^(rgb|RGB)/.test(_this)) {\r\n        const aColor = _this.replace(/(?:\\(|\\)|rgb|RGB)*/g, '').split(',')\r\n        let strHex = '#'\r\n        for (let i = 0; i < aColor.length; i++) {\r\n            let hex = Number(aColor[i]).toString(16)\r\n            hex = String(hex).length == 1 ? `${0}${hex}` : hex // 保证每个rgb的值为2位\r\n            if (hex === '0') {\r\n                hex += hex\r\n            }\r\n            strHex += hex\r\n        }\r\n        if (strHex.length !== 7) {\r\n            strHex = _this\r\n        }\r\n        return strHex\r\n    } if (reg.test(_this)) {\r\n        const aNum = _this.replace(/#/, '').split('')\r\n        if (aNum.length === 6) {\r\n            return _this\r\n        } if (aNum.length === 3) {\r\n            let numHex = '#'\r\n            for (let i = 0; i < aNum.length; i += 1) {\r\n                numHex += (aNum[i] + aNum[i])\r\n            }\r\n            return numHex\r\n        }\r\n    } else {\r\n        return _this\r\n    }\r\n}\r\n\r\n/**\r\n* JS颜色十六进制转换为rgb或rgba,返回的格式为 rgba（255，255，255，0.5）字符串\r\n* sHex为传入的十六进制的色值\r\n* alpha为rgba的透明度\r\n*/\r\nexport function colorToRgba(color, alpha) {\r\n    color = rgbToHex(color)\r\n    // 十六进制颜色值的正则表达式\r\n    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n    /* 16进制颜色转为RGB格式 */\r\n    let sColor = String(color).toLowerCase()\r\n    if (sColor && reg.test(sColor)) {\r\n        if (sColor.length === 4) {\r\n            let sColorNew = '#'\r\n            for (let i = 1; i < 4; i += 1) {\r\n                sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))\r\n            }\r\n            sColor = sColorNew\r\n        }\r\n        // 处理六位的颜色值\r\n        const sColorChange = []\r\n        for (let i = 1; i < 7; i += 2) {\r\n            sColorChange.push(parseInt(`0x${sColor.slice(i, i + 2)}`))\r\n        }\r\n        // return sColorChange.join(',')\r\n        return `rgba(${sColorChange.join(',')},${alpha})`\r\n    }\r\n\r\n    return sColor\r\n}\r\n\r\nexport default {\r\n    colorGradient,\r\n    hexToRgb,\r\n    rgbToHex,\r\n    colorToRgba\r\n}\r\n", "let timeout = null\r\n\r\n/**\r\n * 防抖原理：一定时间内，只有最后一次操作，再过wait毫秒后才执行函数\r\n *\r\n * @param {Function} func 要执行的回调函数\r\n * @param {Number} wait 延时的时间\r\n * @param {Boolean} immediate 是否立即执行\r\n * @return null\r\n */\r\nexport function debounce(func, wait = 500, immediate = false) {\r\n    // 清除定时器\r\n    if (timeout !== null) clearTimeout(timeout)\r\n    // 立即执行，此类情况一般用不到\r\n    if (immediate) {\r\n        const callNow = !timeout\r\n        timeout = setTimeout(() => {\r\n            timeout = null\r\n        }, wait)\r\n        if (callNow) typeof func === 'function' && func()\r\n    } else {\r\n        // 设置定时器，当最后一次操作后，timeout不会再被清除，所以在延时wait毫秒后执行func回调方法\r\n        timeout = setTimeout(() => {\r\n            typeof func === 'function' && func()\r\n        }, wait)\r\n    }\r\n}\r\n\r\nexport default debounce\r\n", "let timer;\r\nlet flag;\r\n/**\r\n * 节流原理：在一定时间内，只能触发一次\r\n *\r\n * @param {Function} func 要执行的回调函数\r\n * @param {Number} wait 延时的时间\r\n * @param {Boolean} immediate 是否立即执行\r\n * @return null\r\n */\r\nexport function throttle(func, wait = 500, immediate = true) {\r\n    if (immediate) {\r\n        if (!flag) {\r\n            flag = true\r\n            // 如果是立即执行，则在wait毫秒内开始时执行\r\n            typeof func === 'function' && func()\r\n            timer = setTimeout(() => {\r\n                flag = false\r\n            }, wait)\r\n        }\r\n    } else if (!flag) {\r\n        flag = true\r\n        // 如果是非立即执行，则在wait毫秒内的结束处执行\r\n        timer = setTimeout(() => {\r\n            flag = false\r\n            typeof func === 'function' && func()\r\n        }, wait)\r\n    }\r\n}\r\nexport default throttle\r\n", "// 浮点数加法\nexport function add (arg1, arg2) {\n\tvar r1, r2, m\n\t\ttry {\n\t\t\tr1 = arg1.toString().split('.')[1].length\n\t\t} catch (e) {\n\t\t\tr1 = 0\n\t\t}\n\t\ttry {\n\t\t\tr2 = arg2.toString().split('.')[1].length\n\t\t} catch (e) {\n\t\t\tr2 = 0\n\t\t}\n\t\tm = Math.pow(10, Math.max(r1, r2))\n\treturn (arg1 * m + arg2 * m) / m\n}\n// 浮点数减法\nexport function sub (arg1, arg2) {\n\tvar r1, r2, m, n\n\t\ttry {\n\t\t  r1 = arg1.toString().split('.')[1].length\n\t\t} catch (e) {\n\t\t  r1 = 0\n\t\t}\n\t\ttry {\n\t\t  r2 = arg2.toString().split('.')[1].length\n\t\t} catch (e) {\n\t\t  r2 = 0\n\t\t}\n\t\tm = Math.pow(10, Math.max(r1, r2))\n\t\tn = (r1 >= r2) ? r1 : r2\n\treturn Math.abs(((arg1 * m - arg2 * m) / m).toFixed(n))\n}\n//浮点乘法\nexport function mul (a, b) {\n\tvar c = 0,\n\t\td = a.toString(),\n\t\te = b.toString();\n\ttry {\n\t\tc += d.split(\".\")[1].length;\n\t} catch (f) {}\n\ttry {\n\t\tc += e.split(\".\")[1].length;\n\t} catch (f) {}\n\treturn Number(d.replace(\".\", \"\")) * Number(e.replace(\".\", \"\")) / Math.pow(10, c);\n}\n//浮点除法\nexport function div (a, b) {\n\tvar c, d, e = 0,\n\t\tf = 0;\n\ttry {\n\t\te = a.toString().split(\".\")[1].length;\n\t} catch (g) {}\n\ttry {\n\t\tf = b.toString().split(\".\")[1].length;\n\t} catch (g) {}\n\treturn c = Number(a.toString().replace(\".\", \"\")), d = Number(b.toString().replace(\".\", \"\")), xyutil.mul(c / d, Math.pow(10, f - e));\n}\nexport default {\n\tadd,\n\tsub,\n\tmul,\n\tdiv\n}\n", "// uniapp在H5中各API的z-index值如下：\r\n/**\r\n * actionsheet: 999\r\n * modal: 999\r\n * navigate: 998\r\n * tabbar: 998\r\n * toast: 999\r\n */\r\n\r\nexport default {\r\n    toast: 10090,\r\n    noNetwork: 10080,\r\n    // popup包含popup，actionsheet，keyboard，picker的值\r\n    popup: 10075,\r\n    mask: 10070,\r\n    navbar: 980,\r\n    topTips: 975,\r\n    sticky: 970,\r\n    indexListSticky: 965\r\n}\n", "// 为了让用户能够自定义主题，会逐步弃用此文件，各颜色通过css提供\r\n// 为了给某些特殊场景使用和向后兼容，无需删除此文件(2020-06-20)\r\nconst color = {\r\n    primary: '#3c9cff',\r\n    info: '#909399',\r\n    default: '#909399',\r\n    warning: '#f9ae3d',\r\n    error: '#f56c6c',\r\n    success: '#5ac725',\r\n    mainColor: '#303133',\r\n    contentColor: '#606266',\r\n    tipsColor: '#909399',\r\n    lightColor: '#c0c4cc',\r\n    borderColor: '#e4e7ed'\r\n}\r\n\r\nexport default color\n", "'use strict'\r\n\r\n// utils is a library of generic helper functions non-specific to axios\r\n\r\nconst { toString } = Object.prototype\r\n\r\n/**\r\n * Determine if a value is an Array\r\n *\r\n * @param {Object} val The value to test\r\n * @returns {boolean} True if value is an Array, otherwise false\r\n */\r\nexport function isArray(val) {\r\n    return toString.call(val) === '[object Array]'\r\n}\r\n\r\n/**\r\n * Determine if a value is an Object\r\n *\r\n * @param {Object} val The value to test\r\n * @returns {boolean} True if value is an Object, otherwise false\r\n */\r\nexport function isObject(val) {\r\n    return val !== null && typeof val === 'object'\r\n}\r\n\r\n/**\r\n * Determine if a value is a Date\r\n *\r\n * @param {Object} val The value to test\r\n * @returns {boolean} True if value is a Date, otherwise false\r\n */\r\nexport function isDate(val) {\r\n    return toString.call(val) === '[object Date]'\r\n}\r\n\r\n/**\r\n * Determine if a value is a URLSearchParams object\r\n *\r\n * @param {Object} val The value to test\r\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\r\n */\r\nexport function isURLSearchParams(val) {\r\n    return typeof URLSearchParams !== 'undefined' && val instanceof URLSearchParams\r\n}\r\n\r\n/**\r\n * Iterate over an Array or an Object invoking a function for each item.\r\n *\r\n * If `obj` is an Array callback will be called passing\r\n * the value, index, and complete array for each item.\r\n *\r\n * If 'obj' is an Object callback will be called passing\r\n * the value, key, and complete object for each property.\r\n *\r\n * @param {Object|Array} obj The object to iterate\r\n * @param {Function} fn The callback to invoke for each item\r\n */\r\nexport function forEach(obj, fn) {\r\n    // Don't bother if no value provided\r\n    if (obj === null || typeof obj === 'undefined') {\r\n        return\r\n    }\r\n\r\n    // Force an array if not already something iterable\r\n    if (typeof obj !== 'object') {\r\n    /* eslint no-param-reassign:0 */\r\n        obj = [obj]\r\n    }\r\n\r\n    if (isArray(obj)) {\r\n    // Iterate over array values\r\n        for (let i = 0, l = obj.length; i < l; i++) {\r\n            fn.call(null, obj[i], i, obj)\r\n        }\r\n    } else {\r\n    // Iterate over object keys\r\n        for (const key in obj) {\r\n            if (Object.prototype.hasOwnProperty.call(obj, key)) {\r\n                fn.call(null, obj[key], key, obj)\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * 是否为boolean 值\r\n * @param val\r\n * @returns {boolean}\r\n */\r\nexport function isBoolean(val) {\r\n    return typeof val === 'boolean'\r\n}\r\n\r\n/**\r\n * 是否为真正的对象{} new Object\r\n * @param {any} obj - 检测的对象\r\n * @returns {boolean}\r\n */\r\nexport function isPlainObject(obj) {\r\n    return Object.prototype.toString.call(obj) === '[object Object]'\r\n}\r\n\r\n/**\r\n * Function equal to merge with the difference being that no reference\r\n * to original objects is kept.\r\n *\r\n * @see merge\r\n * @param {Object} obj1 Object to merge\r\n * @returns {Object} Result of all merge properties\r\n */\r\nexport function deepMerge(/* obj1, obj2, obj3, ... */) {\r\n    const result = {}\r\n    function assignValue(val, key) {\r\n        if (typeof result[key] === 'object' && typeof val === 'object') {\r\n            result[key] = deepMerge(result[key], val)\r\n        } else if (typeof val === 'object') {\r\n            result[key] = deepMerge({}, val)\r\n        } else {\r\n            result[key] = val\r\n        }\r\n    }\r\n    for (let i = 0, l = arguments.length; i < l; i++) {\r\n        forEach(arguments[i], assignValue)\r\n    }\r\n    return result\r\n}\r\n\r\nexport function isUndefined(val) {\r\n    return typeof val === 'undefined'\r\n}\r\n", "'use strict'\r\n\r\nimport * as utils from '../utils'\r\n\r\nfunction encode(val) {\r\n    return encodeURIComponent(val)\r\n        .replace(/%40/gi, '@')\r\n        .replace(/%3A/gi, ':')\r\n        .replace(/%24/g, '$')\r\n        .replace(/%2C/gi, ',')\r\n        .replace(/%20/g, '+')\r\n        .replace(/%5B/gi, '[')\r\n        .replace(/%5D/gi, ']')\r\n}\r\n\r\n/**\r\n * Build a URL by appending params to the end\r\n *\r\n * @param {string} url The base of the url (e.g., http://www.google.com)\r\n * @param {object} [params] The params to be appended\r\n * @returns {string} The formatted url\r\n */\r\nexport default function buildURL(url, params) {\r\n    /* eslint no-param-reassign:0 */\r\n    if (!params) {\r\n        return url\r\n    }\r\n\r\n    let serializedParams\r\n    if (utils.isURLSearchParams(params)) {\r\n        serializedParams = params.toString()\r\n    } else {\r\n        const parts = []\r\n\r\n        utils.forEach(params, (val, key) => {\r\n            if (val === null || typeof val === 'undefined') {\r\n                return\r\n            }\r\n\r\n            if (utils.isArray(val)) {\r\n                key = `${key}[]`\r\n            } else {\r\n                val = [val]\r\n            }\r\n\r\n            utils.forEach(val, (v) => {\r\n                if (utils.isDate(v)) {\r\n                    v = v.toISOString()\r\n                } else if (utils.isObject(v)) {\r\n                    v = JSON.stringify(v)\r\n                }\r\n                parts.push(`${encode(key)}=${encode(v)}`)\r\n            })\r\n        })\r\n\r\n        serializedParams = parts.join('&')\r\n    }\r\n\r\n    if (serializedParams) {\r\n        const hashmarkIndex = url.indexOf('#')\r\n        if (hashmarkIndex !== -1) {\r\n            url = url.slice(0, hashmarkIndex)\r\n        }\r\n\r\n        url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams\r\n    }\r\n\r\n    return url\r\n}\r\n", "'use strict'\r\n\r\n/**\r\n * Determines whether the specified URL is absolute\r\n *\r\n * @param {string} url The URL to test\r\n * @returns {boolean} True if the specified URL is absolute, otherwise false\r\n */\r\nexport default function isAbsoluteURL(url) {\r\n    // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\r\n    // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\r\n    // by any combination of letters, digits, plus, period, or hyphen.\r\n    return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url)\r\n}\r\n", "'use strict'\r\n\r\n/**\r\n * Creates a new URL by combining the specified URLs\r\n *\r\n * @param {string} baseURL The base URL\r\n * @param {string} relativeURL The relative URL\r\n * @returns {string} The combined URL\r\n */\r\nexport default function combineURLs(baseURL, relativeURL) {\r\n    return relativeURL\r\n        ? `${baseURL.replace(/\\/+$/, '')}/${relativeURL.replace(/^\\/+/, '')}`\r\n        : baseURL\r\n}\r\n", "'use strict'\r\n\r\nimport isAbsoluteURL from '../helpers/isAbsoluteURL'\r\nimport combineURLs from '../helpers/combineURLs'\r\n\r\n/**\r\n * Creates a new URL by combining the baseURL with the requestedURL,\r\n * only when the requestedURL is not already an absolute URL.\r\n * If the requestURL is absolute, this function returns the requestedURL untouched.\r\n *\r\n * @param {string} baseURL The base URL\r\n * @param {string} requestedURL Absolute or relative URL to combine\r\n * @returns {string} The combined full path\r\n */\r\nexport default function buildFullPath(baseURL, requestedURL) {\r\n    if (baseURL && !isAbsoluteURL(requestedURL)) {\r\n        return combineURLs(baseURL, requestedURL)\r\n    }\r\n    return requestedURL\r\n}\r\n", "/**\r\n * Resolve or reject a Promise based on response status.\r\n *\r\n * @param {Function} resolve A function that resolves the promise.\r\n * @param {Function} reject A function that rejects the promise.\r\n * @param {object} response The response.\r\n */\r\nexport default function settle(resolve, reject, response) {\r\n    const { validateStatus } = response.config\r\n    const status = response.statusCode\r\n    if (status && (!validateStatus || validateStatus(status))) {\r\n        resolve(response)\r\n    } else {\r\n        reject(response)\r\n    }\r\n}\r\n", "import buildURL from '../helpers/buildURL'\r\nimport buildFullPath from '../core/buildFullPath'\r\nimport settle from '../core/settle'\r\nimport { isUndefined } from '../utils'\r\n\r\n/**\r\n * 返回可选值存在的配置\r\n * @param {Array} keys - 可选值数组\r\n * @param {Object} config2 - 配置\r\n * @return {{}} - 存在的配置项\r\n */\r\nconst mergeKeys = (keys, config2) => {\r\n    const config = {}\r\n    keys.forEach((prop) => {\r\n        if (!isUndefined(config2[prop])) {\r\n            config[prop] = config2[prop]\r\n        }\r\n    })\r\n    return config\r\n}\r\nexport default (config) => new Promise((resolve, reject) => {\r\n    const fullPath = buildURL(buildFullPath(config.baseURL, config.url), config.params)\r\n    const _config = {\r\n        url: fullPath,\r\n        header: config.header,\r\n        complete: (response) => {\r\n            config.fullPath = fullPath\r\n            response.config = config\r\n            try {\r\n                // 对可能字符串不是json 的情况容错\r\n                if (typeof response.data === 'string') {\r\n                    response.data = JSON.parse(response.data)\r\n                }\r\n                // eslint-disable-next-line no-empty\r\n            } catch (e) {\r\n            }\r\n            settle(resolve, reject, response)\r\n        }\r\n    }\r\n    let requestTask\r\n    if (config.method === 'UPLOAD') {\r\n        delete _config.header['content-type']\r\n        delete _config.header['Content-Type']\r\n        const otherConfig = {\r\n\r\n\r\n\r\n            filePath: config.filePath,\r\n            name: config.name\r\n        }\r\n        const optionalKeys = [\r\n\r\n            'files',\r\n\r\n\r\n            'file',\r\n\r\n\r\n            'timeout',\r\n\r\n            'formData'\r\n        ]\r\n        requestTask = uni.uploadFile({ ..._config, ...otherConfig, ...mergeKeys(optionalKeys, config) })\r\n    } else if (config.method === 'DOWNLOAD') {\r\n\r\n        if (!isUndefined(config.timeout)) {\r\n            _config.timeout = config.timeout\r\n        }\r\n\r\n        requestTask = uni.downloadFile(_config)\r\n    } else {\r\n        const optionalKeys = [\r\n            'data',\r\n            'method',\r\n\r\n            'timeout',\r\n\r\n            'dataType',\r\n\r\n            'responseType',\r\n\r\n\r\n\r\n\r\n\r\n            'withCredentials',\r\n\r\n\r\n\r\n\r\n        ]\r\n        requestTask = uni.request({ ..._config, ...mergeKeys(optionalKeys, config) })\r\n    }\r\n    if (config.getTask) {\r\n        config.getTask(requestTask, config)\r\n    }\r\n})\r\n", "import adapter from '../adapters/index'\r\n\r\nexport default (config) => adapter(config)\r\n", "'use strict'\r\n\r\nfunction InterceptorManager() {\r\n    this.handlers = []\r\n}\r\n\r\n/**\r\n * Add a new interceptor to the stack\r\n *\r\n * @param {Function} fulfilled The function to handle `then` for a `Promise`\r\n * @param {Function} rejected The function to handle `reject` for a `Promise`\r\n *\r\n * @return {Number} An ID used to remove interceptor later\r\n */\r\nInterceptorManager.prototype.use = function use(fulfilled, rejected) {\r\n    this.handlers.push({\r\n        fulfilled,\r\n        rejected\r\n    })\r\n    return this.handlers.length - 1\r\n}\r\n\r\n/**\r\n * Remove an interceptor from the stack\r\n *\r\n * @param {Number} id The ID that was returned by `use`\r\n */\r\nInterceptorManager.prototype.eject = function eject(id) {\r\n    if (this.handlers[id]) {\r\n        this.handlers[id] = null\r\n    }\r\n}\r\n\r\n/**\r\n * Iterate over all the registered interceptors\r\n *\r\n * This method is particularly useful for skipping over any\r\n * interceptors that may have become `null` calling `eject`.\r\n *\r\n * @param {Function} fn The function to call for each interceptor\r\n */\r\nInterceptorManager.prototype.forEach = function forEach(fn) {\r\n    this.handlers.forEach((h) => {\r\n        if (h !== null) {\r\n            fn(h)\r\n        }\r\n    })\r\n}\r\n\r\nexport default InterceptorManager\r\n", "import { deepMerge, isUndefined } from '../utils'\r\n\r\n/**\r\n * 合并局部配置优先的配置，如果局部有该配置项则用局部，如果全局有该配置项则用全局\r\n * @param {Array} keys - 配置项\r\n * @param {Object} globalsConfig - 当前的全局配置\r\n * @param {Object} config2 - 局部配置\r\n * @return {{}}\r\n */\r\nconst mergeKeys = (keys, globalsConfig, config2) => {\r\n    const config = {}\r\n    keys.forEach((prop) => {\r\n        if (!isUndefined(config2[prop])) {\r\n            config[prop] = config2[prop]\r\n        } else if (!isUndefined(globalsConfig[prop])) {\r\n            config[prop] = globalsConfig[prop]\r\n        }\r\n    })\r\n    return config\r\n}\r\n/**\r\n *\r\n * @param globalsConfig - 当前实例的全局配置\r\n * @param config2 - 当前的局部配置\r\n * @return - 合并后的配置\r\n */\r\nexport default (globalsConfig, config2 = {}) => {\r\n    const method = config2.method || globalsConfig.method || 'GET'\r\n    let config = {\r\n        baseURL: globalsConfig.baseURL || '',\r\n        method,\r\n        url: config2.url || '',\r\n        params: config2.params || {},\r\n        custom: { ...(globalsConfig.custom || {}), ...(config2.custom || {}) },\r\n        header: deepMerge(globalsConfig.header || {}, config2.header || {})\r\n    }\r\n    const defaultToConfig2Keys = ['getTask', 'validateStatus']\r\n    config = { ...config, ...mergeKeys(defaultToConfig2Keys, globalsConfig, config2) }\r\n\r\n    // eslint-disable-next-line no-empty\r\n    if (method === 'DOWNLOAD') {\r\n\r\n        if (!isUndefined(config2.timeout)) {\r\n            config.timeout = config2.timeout\r\n        } else if (!isUndefined(globalsConfig.timeout)) {\r\n            config.timeout = globalsConfig.timeout\r\n        }\r\n\r\n    } else if (method === 'UPLOAD') {\r\n        delete config.header['content-type']\r\n        delete config.header['Content-Type']\r\n        const uploadKeys = [\r\n\r\n            'files',\r\n\r\n\r\n\r\n\r\n\r\n            'file',\r\n\r\n            'filePath',\r\n            'name',\r\n\r\n            'timeout',\r\n\r\n            'formData'\r\n        ]\r\n        uploadKeys.forEach((prop) => {\r\n            if (!isUndefined(config2[prop])) {\r\n                config[prop] = config2[prop]\r\n            }\r\n        })\r\n\r\n        if (isUndefined(config.timeout) && !isUndefined(globalsConfig.timeout)) {\r\n            config.timeout = globalsConfig.timeout\r\n        }\r\n\r\n    } else {\r\n        const defaultsKeys = [\r\n            'data',\r\n\r\n            'timeout',\r\n\r\n            'dataType',\r\n\r\n            'responseType',\r\n\r\n\r\n\r\n\r\n\r\n            'withCredentials',\r\n\r\n\r\n\r\n\r\n        ]\r\n        config = { ...config, ...mergeKeys(defaultsKeys, globalsConfig, config2) }\r\n    }\r\n\r\n    return config\r\n}\r\n", "/**\r\n * 默认的全局配置\r\n */\r\n\r\nexport default {\r\n    baseURL: '',\r\n    header: {},\r\n    method: 'GET',\r\n    dataType: 'json',\r\n\r\n    responseType: 'text',\r\n\r\n    custom: {},\r\n\r\n    timeout: 60000,\r\n\r\n\r\n\r\n\r\n\r\n    withCredentials: false,\r\n\r\n\r\n\r\n\r\n    validateStatus: function validateStatus(status) {\r\n        return status >= 200 && status < 300\r\n    }\r\n}\r\n", "/* eslint-disable */\r\nvar clone = (function() {\r\n  'use strict';\r\n\r\n  function _instanceof(obj, type) {\r\n    return type != null && obj instanceof type;\r\n  }\r\n\r\n  var nativeMap;\r\n  try {\r\n    nativeMap = Map;\r\n  } catch(_) {\r\n    // maybe a reference error because no `Map`. Give it a dummy value that no\r\n    // value will ever be an instanceof.\r\n    nativeMap = function() {};\r\n  }\r\n\r\n  var nativeSet;\r\n  try {\r\n    nativeSet = Set;\r\n  } catch(_) {\r\n    nativeSet = function() {};\r\n  }\r\n\r\n  var nativePromise;\r\n  try {\r\n    nativePromise = Promise;\r\n  } catch(_) {\r\n    nativePromise = function() {};\r\n  }\r\n\r\n  /**\r\n   * Clones (copies) an Object using deep copying.\r\n   *\r\n   * This function supports circular references by default, but if you are certain\r\n   * there are no circular references in your object, you can save some CPU time\r\n   * by calling clone(obj, false).\r\n   *\r\n   * Caution: if `circular` is false and `parent` contains circular references,\r\n   * your program may enter an infinite loop and crash.\r\n   *\r\n   * @param `parent` - the object to be cloned\r\n   * @param `circular` - set to true if the object to be cloned may contain\r\n   *    circular references. (optional - true by default)\r\n   * @param `depth` - set to a number if the object is only to be cloned to\r\n   *    a particular depth. (optional - defaults to Infinity)\r\n   * @param `prototype` - sets the prototype to be used when cloning an object.\r\n   *    (optional - defaults to parent prototype).\r\n   * @param `includeNonEnumerable` - set to true if the non-enumerable properties\r\n   *    should be cloned as well. Non-enumerable properties on the prototype\r\n   *    chain will be ignored. (optional - false by default)\r\n   */\r\n  function clone(parent, circular, depth, prototype, includeNonEnumerable) {\r\n    if (typeof circular === 'object') {\r\n      depth = circular.depth;\r\n      prototype = circular.prototype;\r\n      includeNonEnumerable = circular.includeNonEnumerable;\r\n      circular = circular.circular;\r\n    }\r\n    // maintain two arrays for circular references, where corresponding parents\r\n    // and children have the same index\r\n    var allParents = [];\r\n    var allChildren = [];\r\n\r\n    var useBuffer = typeof Buffer != 'undefined';\r\n\r\n    if (typeof circular == 'undefined')\r\n      circular = true;\r\n\r\n    if (typeof depth == 'undefined')\r\n      depth = Infinity;\r\n\r\n    // recurse this function so we don't reset allParents and allChildren\r\n    function _clone(parent, depth) {\r\n      // cloning null always returns null\r\n      if (parent === null)\r\n        return null;\r\n\r\n      if (depth === 0)\r\n        return parent;\r\n\r\n      var child;\r\n      var proto;\r\n      if (typeof parent != 'object') {\r\n        return parent;\r\n      }\r\n\r\n      if (_instanceof(parent, nativeMap)) {\r\n        child = new nativeMap();\r\n      } else if (_instanceof(parent, nativeSet)) {\r\n        child = new nativeSet();\r\n      } else if (_instanceof(parent, nativePromise)) {\r\n        child = new nativePromise(function (resolve, reject) {\r\n          parent.then(function(value) {\r\n            resolve(_clone(value, depth - 1));\r\n          }, function(err) {\r\n            reject(_clone(err, depth - 1));\r\n          });\r\n        });\r\n      } else if (clone.__isArray(parent)) {\r\n        child = [];\r\n      } else if (clone.__isRegExp(parent)) {\r\n        child = new RegExp(parent.source, __getRegExpFlags(parent));\r\n        if (parent.lastIndex) child.lastIndex = parent.lastIndex;\r\n      } else if (clone.__isDate(parent)) {\r\n        child = new Date(parent.getTime());\r\n      } else if (useBuffer && Buffer.isBuffer(parent)) {\r\n        if (Buffer.from) {\r\n          // Node.js >= 5.10.0\r\n          child = Buffer.from(parent);\r\n        } else {\r\n          // Older Node.js versions\r\n          child = new Buffer(parent.length);\r\n          parent.copy(child);\r\n        }\r\n        return child;\r\n      } else if (_instanceof(parent, Error)) {\r\n        child = Object.create(parent);\r\n      } else {\r\n        if (typeof prototype == 'undefined') {\r\n          proto = Object.getPrototypeOf(parent);\r\n          child = Object.create(proto);\r\n        }\r\n        else {\r\n          child = Object.create(prototype);\r\n          proto = prototype;\r\n        }\r\n      }\r\n\r\n      if (circular) {\r\n        var index = allParents.indexOf(parent);\r\n\r\n        if (index != -1) {\r\n          return allChildren[index];\r\n        }\r\n        allParents.push(parent);\r\n        allChildren.push(child);\r\n      }\r\n\r\n      if (_instanceof(parent, nativeMap)) {\r\n        parent.forEach(function(value, key) {\r\n          var keyChild = _clone(key, depth - 1);\r\n          var valueChild = _clone(value, depth - 1);\r\n          child.set(keyChild, valueChild);\r\n        });\r\n      }\r\n      if (_instanceof(parent, nativeSet)) {\r\n        parent.forEach(function(value) {\r\n          var entryChild = _clone(value, depth - 1);\r\n          child.add(entryChild);\r\n        });\r\n      }\r\n\r\n      for (var i in parent) {\r\n        var attrs = Object.getOwnPropertyDescriptor(parent, i);\r\n        if (attrs) {\r\n          child[i] = _clone(parent[i], depth - 1);\r\n        }\r\n\r\n        try {\r\n          var objProperty = Object.getOwnPropertyDescriptor(parent, i);\r\n          if (objProperty.set === 'undefined') {\r\n            // no setter defined. Skip cloning this property\r\n            continue;\r\n          }\r\n          child[i] = _clone(parent[i], depth - 1);\r\n        } catch(e){\r\n          if (e instanceof TypeError) {\r\n            // when in strict mode, TypeError will be thrown if child[i] property only has a getter\r\n            // we can't do anything about this, other than inform the user that this property cannot be set.\r\n            continue\r\n          } else if (e instanceof ReferenceError) {\r\n            //this may happen in non strict mode\r\n            continue\r\n          }\r\n        }\r\n\r\n      }\r\n\r\n      if (Object.getOwnPropertySymbols) {\r\n        var symbols = Object.getOwnPropertySymbols(parent);\r\n        for (var i = 0; i < symbols.length; i++) {\r\n          // Don't need to worry about cloning a symbol because it is a primitive,\r\n          // like a number or string.\r\n          var symbol = symbols[i];\r\n          var descriptor = Object.getOwnPropertyDescriptor(parent, symbol);\r\n          if (descriptor && !descriptor.enumerable && !includeNonEnumerable) {\r\n            continue;\r\n          }\r\n          child[symbol] = _clone(parent[symbol], depth - 1);\r\n          Object.defineProperty(child, symbol, descriptor);\r\n        }\r\n      }\r\n\r\n      if (includeNonEnumerable) {\r\n        var allPropertyNames = Object.getOwnPropertyNames(parent);\r\n        for (var i = 0; i < allPropertyNames.length; i++) {\r\n          var propertyName = allPropertyNames[i];\r\n          var descriptor = Object.getOwnPropertyDescriptor(parent, propertyName);\r\n          if (descriptor && descriptor.enumerable) {\r\n            continue;\r\n          }\r\n          child[propertyName] = _clone(parent[propertyName], depth - 1);\r\n          Object.defineProperty(child, propertyName, descriptor);\r\n        }\r\n      }\r\n\r\n      return child;\r\n    }\r\n\r\n    return _clone(parent, depth);\r\n  }\r\n\r\n  /**\r\n   * Simple flat clone using prototype, accepts only objects, usefull for property\r\n   * override on FLAT configuration object (no nested props).\r\n   *\r\n   * USE WITH CAUTION! This may not behave as you wish if you do not know how this\r\n   * works.\r\n   */\r\n  clone.clonePrototype = function clonePrototype(parent) {\r\n    if (parent === null)\r\n      return null;\r\n\r\n    var c = function () {};\r\n    c.prototype = parent;\r\n    return new c();\r\n  };\r\n\r\n// private utility functions\r\n\r\n  function __objToStr(o) {\r\n    return Object.prototype.toString.call(o);\r\n  }\r\n  clone.__objToStr = __objToStr;\r\n\r\n  function __isDate(o) {\r\n    return typeof o === 'object' && __objToStr(o) === '[object Date]';\r\n  }\r\n  clone.__isDate = __isDate;\r\n\r\n  function __isArray(o) {\r\n    return typeof o === 'object' && __objToStr(o) === '[object Array]';\r\n  }\r\n  clone.__isArray = __isArray;\r\n\r\n  function __isRegExp(o) {\r\n    return typeof o === 'object' && __objToStr(o) === '[object RegExp]';\r\n  }\r\n  clone.__isRegExp = __isRegExp;\r\n\r\n  function __getRegExpFlags(re) {\r\n    var flags = '';\r\n    if (re.global) flags += 'g';\r\n    if (re.ignoreCase) flags += 'i';\r\n    if (re.multiline) flags += 'm';\r\n    return flags;\r\n  }\r\n  clone.__getRegExpFlags = __getRegExpFlags;\r\n\r\n  return clone;\r\n})();\r\n\r\nexport default clone\r\n", "/**\r\n * @Class Request\r\n * @description luch-request http请求插件\r\n * @version 3.0.7\r\n * <AUTHOR>\r\n * @Date 2021-09-04\r\n * @Email <EMAIL>\r\n * 文档: https://www.quanzhan.co/luch-request/\r\n * github: https://github.com/lei-mu/luch-request\r\n * DCloud: http://ext.dcloud.net.cn/plugin?id=392\r\n * HBuilderX: beat-3.0.4 alpha-3.0.4\r\n */\r\n\r\nimport dispatchRequest from './dispatchRequest'\r\nimport InterceptorManager from './InterceptorManager'\r\nimport mergeConfig from './mergeConfig'\r\nimport defaults from './defaults'\r\nimport { isPlainObject } from '../utils'\r\nimport clone from '../utils/clone'\r\n\r\nexport default class Request {\r\n    /**\r\n   * @param {Object} arg - 全局配置\r\n   * @param {String} arg.baseURL - 全局根路径\r\n   * @param {Object} arg.header - 全局header\r\n   * @param {String} arg.method = [GET|POST|PUT|DELETE|CONNECT|HEAD|OPTIONS|TRACE] - 全局默认请求方式\r\n   * @param {String} arg.dataType = [json] - 全局默认的dataType\r\n   * @param {String} arg.responseType = [text|arraybuffer] - 全局默认的responseType。支付宝小程序不支持\r\n   * @param {Object} arg.custom - 全局默认的自定义参数\r\n   * @param {Number} arg.timeout - 全局默认的超时时间，单位 ms。默认60000。H5(HBuilderX 2.9.9+)、APP(HBuilderX 2.9.9+)、微信小程序（2.10.0）、支付宝小程序\r\n   * @param {Boolean} arg.sslVerify - 全局默认的是否验证 ssl 证书。默认true.仅App安卓端支持（HBuilderX 2.3.3+）\r\n   * @param {Boolean} arg.withCredentials - 全局默认的跨域请求时是否携带凭证（cookies）。默认false。仅H5支持（HBuilderX 2.6.15+）\r\n   * @param {Boolean} arg.firstIpv4 - 全DNS解析时优先使用ipv4。默认false。仅 App-Android 支持 (HBuilderX 2.8.0+)\r\n   * @param {Function(statusCode):Boolean} arg.validateStatus - 全局默认的自定义验证器。默认statusCode >= 200 && statusCode < 300\r\n   */\r\n    constructor(arg = {}) {\r\n\t\t// console.info('初始化luch-request')\r\n        if (!isPlainObject(arg)) {\r\n            arg = {}\r\n            console.warn('设置全局参数必须接收一个Object')\r\n        }\r\n        this.config = clone({ ...defaults, ...arg })\r\n        this.interceptors = {\r\n            request: new InterceptorManager(),\r\n            response: new InterceptorManager()\r\n        }\r\n    }\r\n\r\n    /**\r\n   * @Function\r\n   * @param {Request~setConfigCallback} f - 设置全局默认配置\r\n   */\r\n    setConfig(f) {\r\n        this.config = f(this.config)\r\n    }\r\n\r\n    middleware(config) {\r\n        config = mergeConfig(this.config, config)\r\n        const chain = [dispatchRequest, undefined]\r\n        let promise = Promise.resolve(config)\r\n\r\n        this.interceptors.request.forEach((interceptor) => {\r\n            chain.unshift(interceptor.fulfilled, interceptor.rejected)\r\n        })\r\n\r\n        this.interceptors.response.forEach((interceptor) => {\r\n            chain.push(interceptor.fulfilled, interceptor.rejected)\r\n        })\r\n\r\n        while (chain.length) {\r\n            promise = promise.then(chain.shift(), chain.shift())\r\n        }\r\n\r\n        return promise\r\n    }\r\n\r\n    /**\r\n   * @Function\r\n   * @param {Object} config - 请求配置项\r\n   * @prop {String} options.url - 请求路径\r\n   * @prop {Object} options.data - 请求参数\r\n   * @prop {Object} [options.responseType = config.responseType] [text|arraybuffer] - 响应的数据类型\r\n   * @prop {Object} [options.dataType = config.dataType] - 如果设为 json，会尝试对返回的数据做一次 JSON.parse\r\n   * @prop {Object} [options.header = config.header] - 请求header\r\n   * @prop {Object} [options.method = config.method] - 请求方法\r\n   * @returns {Promise<unknown>}\r\n   */\r\n    request(config = {}) {\r\n        return this.middleware(config)\r\n    }\r\n\r\n    get(url, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            method: 'GET',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    post(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'POST',\r\n            ...options\r\n        })\r\n    }\r\n\r\n\r\n    put(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'PUT',\r\n            ...options\r\n        })\r\n    }\r\n\r\n\r\n\r\n\r\n    delete(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'DELETE',\r\n            ...options\r\n        })\r\n    }\r\n\r\n\r\n\r\n\r\n    connect(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'CONNECT',\r\n            ...options\r\n        })\r\n    }\r\n\r\n\r\n\r\n\r\n    head(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'HEAD',\r\n            ...options\r\n        })\r\n    }\r\n\r\n\r\n\r\n\r\n    options(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'OPTIONS',\r\n            ...options\r\n        })\r\n    }\r\n\r\n\r\n\r\n\r\n    trace(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'TRACE',\r\n            ...options\r\n        })\r\n    }\r\n\r\n\r\n\r\n    upload(url, config = {}) {\r\n        config.url = url\r\n        config.method = 'UPLOAD'\r\n        return this.middleware(config)\r\n    }\r\n\r\n    download(url, config = {}) {\r\n        config.url = url\r\n        config.method = 'DOWNLOAD'\r\n        return this.middleware(config)\r\n    }\r\n}\r\n\r\n/**\r\n * setConfig回调\r\n * @return {Object} - 返回操作后的config\r\n * @callback Request~setConfigCallback\r\n * @param {Object} config - 全局默认config\r\n */\r\n", "import Request from './core/Request'\n\r\nexport default Request\r\n", "// 全局挂载引入http相关请求拦截插件\nimport Request from '../luch-request'\nconst http = new Request()\nexport default http\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:44:35\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/actionSheet.js\n */\nexport default {\n    // action-sheet组件\n    actionSheet: {\n        show: false,\n        title: '',\n        description: '',\n        actions: [],\n        index: '',\n        cancelText: '',\n        closeOnClickAction: true,\n        safeAreaInsetBottom: true,\n        openType: '',\n        closeOnClickOverlay: true,\n        round: 0,\n        wrapMaxHeight: '600px'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:47:24\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/album.js\n */\nexport default {\n    // album 组件\n    album: {\n        urls: [],\n        keyName: '',\n        singleSize: 180,\n        multipleSize: 70,\n        space: 6,\n        singleMode: 'scaleToFill',\n        multipleMode: 'aspectFill',\n        maxCount: 9,\n        previewFullImage: true,\n        rowCount: 3,\n        showMore: true,\n        autoWrap: false,\n        unit: 'px',\n        stop: true,\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:48:53\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/alert.js\n */\nexport default {\n    // alert警告组件\n    alert: {\n        title: '',\n        type: 'warning',\n        description: '',\n        closable: false,\n        showIcon: false,\n        effect: 'light',\n        center: false,\n        fontSize: 14\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:49:22\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/avatar.js\n */\nexport default {\n    // avatar 组件\n    avatar: {\n        src: '',\n        shape: 'circle',\n        size: 40,\n        mode: 'scaleToFill',\n        text: '',\n        bgColor: '#c0c4cc',\n        color: '#ffffff',\n        fontSize: 18,\n        icon: '',\n        mpAvatar: false,\n        randomBgColor: false,\n        defaultUrl: '',\n        colorIndex: '',\n        name: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:49:55\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/avatarGroup.js\n */\nexport default {\n    // avatarGroup 组件\n    avatarGroup: {\n        urls: [],\n        maxCount: 5,\n        shape: 'circle',\n        mode: 'scaleToFill',\n        showMore: true,\n        size: 40,\n        keyName: '',\n        gap: 0.5,\n\t\textraValue: 0\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:50:18\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/backtop.js\n */\nexport default {\n    // backtop组件\n    backtop: {\n        mode: 'circle',\n        icon: 'arrow-upward',\n        text: '',\n        duration: 100,\n        scrollTop: 0,\n        top: 400,\n        bottom: 100,\n        right: 20,\n        zIndex: 9,\n        iconStyle: {\n            color: '#909399',\n            fontSize: '19px'\n        }\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-23 19:51:50\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/badge.js\n */\nexport default {\n    // 徽标数组件\n    badge: {\n        isDot: false,\n        value: '',\n        show: true,\n        max: 999,\n        type: 'error',\n        showZero: false,\n        bgColor: null,\n        color: null,\n        shape: 'circle',\n        numberType: 'overflow',\n        offset: [],\n        inverted: false,\n        absolute: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:51:27\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/button.js\n */\nexport default {\n    // button组件\n    button: {\n        hairline: false,\n        type: 'info',\n        size: 'normal',\n        shape: 'square',\n        plain: false,\n        disabled: false,\n        loading: false,\n        loadingText: '',\n        loadingMode: 'spinner',\n        loadingSize: 15,\n        openType: '',\n        formType: '',\n        appParameter: '',\n        hoverStopPropagation: true,\n        lang: 'en',\n        sessionFrom: '',\n        sendMessageTitle: '',\n        sendMessagePath: '',\n        sendMessageImg: '',\n        showMessageCard: false,\n        dataName: '',\n        throttleTime: 0,\n        hoverStartTime: 0,\n        hoverStayTime: 200,\n        text: '',\n        icon: '',\n        iconColor: '',\n        color: '',\n        stop: true,\n    }\n}\n", "/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:52:43\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/calendar.js\r\n */\r\nexport default {\r\n    // calendar 组件\r\n    calendar: {\r\n        title: '日期选择',\r\n        showTitle: true,\r\n        showSubtitle: true,\r\n        mode: 'single',\r\n        startText: '开始',\r\n        endText: '结束',\r\n        customList: [],\r\n        color: '#3c9cff',\r\n        minDate: 0,\r\n        maxDate: 0,\r\n        defaultDate: null,\r\n        maxCount: Number.MAX_SAFE_INTEGER, // Infinity\r\n        rowHeight: 56,\r\n        formatter: null,\r\n        showLunar: false,\r\n        showMark: true,\r\n        confirmText: '确定',\r\n        confirmDisabledText: '确定',\r\n        show: false,\r\n        closeOnClickOverlay: false,\r\n        readonly: false,\r\n        showConfirm: true,\r\n        maxRange: Number.MAX_SAFE_INTEGER, // Infinity\r\n        rangePrompt: '',\r\n        showRangePrompt: true,\r\n        allowSameDay: false,\r\n\t\tround: 0,\r\n\t\tmonthNum: 3,\r\n        weekText: ['一', '二', '三', '四', '五', '六', '日'],\r\n        forbidDays: [],\r\n        forbidDaysToast: '该日期已禁用',\r\n    }\r\n}\r\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:53:20\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/carKeyboard.js\n */\nexport default {\n    // 车牌号键盘\n    carKeyboard: {\n        random: false\n    }\n}\n", "/*\r\n * <AUTHOR> jry\r\n * @Description  :\r\n * @version      : 3.0\r\n * @Date         : 2025-04-26 16:37:21\r\n * @LastAuthor   : jry\r\n * @lastTime     : 2025-04-26 16:37:21\r\n * @FilePath     : /uview-plus/libs/config/props/card.js\r\n */\r\nexport default {\r\n\t// card组件的props\r\n\tcard: {\r\n\t\tfull: false,\r\n\t\ttitle: '',\r\n\t\ttitleColor: '#303133',\r\n\t\ttitleSize: '15px',\r\n\t\tsubTitle: '',\r\n\t\tsubTitleColor: '#909399',\r\n\t\tsubTitleSize: '13px',\r\n\t\tborder: true,\r\n\t\tindex: '',\r\n\t\tmargin: '15px',\r\n\t\tborderRadius: '8px',\r\n\t\theadStyle: {},\r\n\t\tbodyStyle: {},\r\n\t\tfootStyle: {},\r\n\t\theadBorderBottom: true,\r\n\t\tfootBorderTop: true,\r\n\t\tthumb: '',\r\n\t\tthumbWidth: '30px',\r\n\t\tthumbCircle: false,\r\n\t\tpadding: '15px',\r\n\t\tpaddingHead: '',\r\n        paddingBody: '',\r\n        paddingFoot: '',\r\n        showHead: true,\r\n        showFoot: true,\r\n        boxShadow: 'none'\r\n\t}\r\n}\r\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-23 20:53:09\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/cell.js\n */\nexport default {\n\t// cell组件的props\n\tcell: {\n\t\tcustomClass: '',\n\t\ttitle: '',\n\t\tlabel: '',\n\t\tvalue: '',\n\t\ticon: '',\n\t\tdisabled: false,\n\t\tborder: true,\n\t\tcenter: false,\n\t\turl: '',\n\t\tlinkType: 'navigateTo',\n\t\tclickable: false,\n\t\tisLink: false,\n\t\trequired: false,\n\t\tarrowDirection: '',\n\t\ticonStyle: {},\n\t\trightIconStyle: {},\n\t\trightIcon: 'arrow-right',\n\t\ttitleStyle: {},\n\t\tsize: '',\n\t\tstop: true,\n\t\tname: ''\n\t}\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:54:16\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/cellGroup.js\n */\nexport default {\n    // cell-group组件的props\n    cellGroup: {\n        title: '',\n        border: true,\n        customStyle: {}\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-23 21:06:59\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/checkbox.js\n */\nexport default {\n    // checkbox组件\n    checkbox: {\n        name: '',\n        shape: '',\n        size: '',\n        checkbox: false,\n        disabled: '',\n        activeColor: '',\n        inactiveColor: '',\n        iconSize: '',\n        iconColor: '',\n        label: '',\n        labelSize: '',\n        labelColor: '',\n        labelDisabled: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:54:47\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/checkboxGroup.js\n */\nexport default {\n    // checkbox-group组件\n    checkboxGroup: {\n        name: '',\n        value: [],\n        shape: 'square',\n        disabled: false,\n        activeColor: '#2979ff',\n        inactiveColor: '#c8c9cc',\n        size: 18,\n        placement: 'row',\n        labelSize: 14,\n        labelColor: '#303133',\n        labelDisabled: false,\n        iconColor: '#ffffff',\n        iconSize: 12,\n        iconPlacement: 'left',\n        borderBottom: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:55:02\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/circleProgress.js\n */\nexport default {\n    // circleProgress 组件\n    circleProgress: {\n        percentage: 30\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:55:27\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/code.js\n */\n\nexport default {\n    // code 组件\n    code: {\n        seconds: 60,\n        startText: '获取验证码',\n        changeText: 'X秒重新获取',\n        endText: '重新获取',\n        keepRunning: false,\n        uniqueKey: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:55:58\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/codeInput.js\n */\nexport default {\n    // codeInput 组件\n    codeInput: {\n\t\tadjustPosition: true,\n        maxlength: 6,\n        dot: false,\n        mode: 'box',\n        hairline: false,\n        space: 10,\n        value: '',\n        focus: false,\n        bold: false,\n        color: '#606266',\n        fontSize: 18,\n        size: 35,\n        disabledKeyboard: false,\n        borderColor: '#c9cacc',\n\t\tdisabledDot: true\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:56:12\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/col.js\n */\nexport default {\n    // col 组件\n    col: {\n        span: 12,\n        offset: 0,\n        justify: 'start',\n        align: 'stretch',\n        textAlign: 'left'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:56:30\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/collapse.js\n */\nexport default {\n    // collapse 组件\n    collapse: {\n        value: null,\n        accordion: false,\n        border: true\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:56:42\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/collapseItem.js\n */\nexport default {\n    // collapseItem 组件\n    collapseItem: {\n        title: '',\n        value: '',\n        label: '',\n        disabled: false,\n        isLink: true,\n        clickable: true,\n        border: true,\n        align: 'left',\n        name: '',\n        icon: '',\n        duration: 300,\n        showRight: true,\n        titleStyle: {},\n        iconStyle: {},\n\t\trightIconStyle: {},\n        cellCustomStyle: {},\n        cellCustomClass: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:57:16\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/columnNotice.js\n */\nexport default {\n    // columnNotice 组件\n    columnNotice: {\n        text: '',\n        icon: 'volume',\n        mode: '',\n        color: '#f9ae3d',\n        bgColor: '#fdf6ec',\n        fontSize: 14,\n        speed: 80,\n        step: false,\n        duration: 1500,\n        disableTouch: true,\n\t\tjustifyContent: 'flex-start'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:11:29\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/countDown.js\n */\nexport default {\n    // u-count-down 计时器组件\n    countDown: {\n        time: 0,\n        format: 'HH:mm:ss',\n        autoStart: true,\n        millisecond: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:57:32\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/countTo.js\n */\nexport default {\n    // countTo 组件\n    countTo: {\n        startVal: 0,\n        endVal: 0,\n        duration: 2000,\n        autoplay: true,\n        decimals: 0,\n        useEasing: true,\n        decimal: '.',\n        color: '#606266',\n        fontSize: 22,\n        bold: false,\n        separator: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:57:48\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/datetimePicker.js\n */\nexport default {\n    // datetimePicker 组件\n    datetimePicker: {\n        show: false,\n\t\tpopupMode: 'bottom',\n        showToolbar: true,\n        value: '',\n        title: '',\n        mode: 'datetime',\n        maxDate: new Date(new Date().getFullYear() + 10, 0, 1).getTime(),\n        minDate: new Date(new Date().getFullYear() - 10, 0, 1).getTime(),\n        minHour: 0,\n        maxHour: 23,\n        minMinute: 0,\n        maxMinute: 59,\n        filter: null,\n        formatter: null,\n        loading: false,\n        itemHeight: 44,\n        cancelText: '取消',\n        confirmText: '确认',\n        cancelColor: '#909193',\n        confirmColor: '#3c9cff',\n        visibleItemCount: 5,\n        closeOnClickOverlay: false,\n        defaultIndex: [],\n        inputBorder: 'surround',\n        disabled: false,\n        disabledColor: '',\n        placeholder: '请选择',\n        inputProps: {},\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:58:03\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/divider.js\n */\nexport default {\n    // divider组件\n    divider: {\n        dashed: false,\n        hairline: true,\n        dot: false,\n        textPosition: 'center',\n        text: '',\n        textSize: 14,\n        textColor: '#909399',\n        lineColor: '#dcdfe6'\n    }\n\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:03:27\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/empty.js\n */\nexport default {\n    // empty组件\n    empty: {\n        icon: '',\n        text: '',\n        textColor: '#c0c4cc',\n        textSize: 14,\n        iconColor: '#c0c4cc',\n        iconSize: 90,\n        mode: 'data',\n        width: 160,\n        height: 160,\n        show: true,\n        marginTop: 0\n    }\n\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:03:49\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/form.js\n */\nexport default {\n    // form 组件\n    form: {\n        model: {},\n        rules: {},\n        errorType: 'message',\n        borderBottom: true,\n        labelPosition: 'left',\n        labelWidth: 45,\n        labelAlign: 'left',\n        labelStyle: {}\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:04:32\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/formItem.js\n */\nexport default {\n    // formItem 组件\n    formItem: {\n        label: '',\n        prop: '',\n        rules: [],\n        borderBottom: '',\n        labelPosition: '',\n        labelWidth: '',\n        rightIcon: '',\n        leftIcon: '',\n        required: false,\n        leftIconStyle: '',\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:05:25\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/gap.js\n */\nexport default {\n    // gap组件\n    gap: {\n        bgColor: 'transparent',\n        height: 20,\n        marginTop: 0,\n        marginBottom: 0,\n        customStyle: {}\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:05:57\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/grid.js\n */\nexport default {\n    // grid组件\n    grid: {\n        col: 3,\n        border: false,\n        align: 'left'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:06:13\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/gridItem.js\n */\nexport default {\n    // grid-item组件\n    gridItem: {\n        name: null,\n        bgColor: 'transparent'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 18:00:14\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/icon.js\n */\nimport config from '../../libs/config/config'\n\nconst {\n    color\n} = config\nexport default {\n    // icon组件\n    icon: {\n        name: '',\n        color: color['u-content-color'],\n        size: '16px',\n        bold: false,\n        index: '',\n        hoverClass: '',\n        customPrefix: 'uicon',\n        label: '',\n        labelPos: 'right',\n        labelSize: '15px',\n        labelColor: color['u-content-color'],\n        space: '3px',\n        imgMode: '',\n        width: '',\n        height: '',\n        top: 0,\n        stop: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:01:51\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/image.js\n */\nexport default {\n    // image组件\n    image: {\n        src: '',\n        mode: 'aspectFill',\n        width: '300',\n        height: '225',\n        shape: 'square',\n        radius: 0,\n        lazyLoad: true,\n        showMenuByLongpress: true,\n        loadingIcon: 'photo',\n        errorIcon: 'error-circle',\n        showLoading: true,\n        showError: true,\n        fade: true,\n        webp: false,\n        duration: 500,\n        bgColor: '#f3f4f6'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:13:15\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/indexAnchor.js\n */\nexport default {\n    // indexAnchor 组件\n    indexAnchor: {\n        text: '',\n        color: '#606266',\n        size: 14,\n        bgColor: '#f1f1f1',\n        height: 32\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:13:35\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/indexList.js\n */\nexport default {\n    // indexList 组件\n    indexList: {\n        inactiveColor: '#606266',\n        activeColor: '#5677fc',\n        indexList: [],\n        sticky: true,\n        customNavHeight: 0,\n        safeBottomFix: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:13:55\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/input.js\n */\nexport default {\n\t// index 组件\n\tinput: {\n\t\tvalue: '',\n\t\ttype: 'text',\n\t\tfixed: false,\n\t\tdisabled: false,\n\t\tdisabledColor: '#f5f7fa',\n\t\tclearable: false,\n\t\tpassword: false,\n\t\tmaxlength: 140,\n\t\tplaceholder: null,\n\t\tplaceholderClass: 'input-placeholder',\n\t\tplaceholderStyle: 'color: #c0c4cc',\n\t\tshowWordLimit: false,\n\t\tconfirmType: 'done',\n\t\tconfirmHold: false,\n\t\tholdKeyboard: false,\n\t\tfocus: false,\n\t\tautoBlur: false,\n\t\tdisableDefaultPadding: false,\n\t\tcursor: -1,\n\t\tcursorSpacing: 30,\n\t\tselectionStart: -1,\n\t\tselectionEnd: -1,\n\t\tadjustPosition: true,\n\t\tinputAlign: 'left',\n\t\tfontSize: '15px',\n\t\tcolor: '#303133',\n\t\tprefixIcon: '',\n\t\tprefixIconStyle: '',\n\t\tsuffixIcon: '',\n\t\tsuffixIconStyle: '',\n\t\tborder: 'surround',\n\t\treadonly: false,\n\t\tshape: 'square',\n\t\tformatter: null\n\t}\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:07:49\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/keyboard.js\n */\nexport default {\n    // 键盘组件\n    keyboard: {\n        mode: 'number',\n        dotDisabled: false,\n        tooltip: true,\n        showTips: true,\n        tips: '',\n        showCancel: true,\n        showConfirm: true,\n        random: false,\n        safeAreaInsetBottom: true,\n        closeOnClickOverlay: true,\n        show: false,\n        overlay: true,\n        zIndex: 10075,\n        cancelText: '取消',\n        confirmText: '确定',\n        autoChange: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:04:49\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/line.js\n */\nexport default {\n    // line组件\n    line: {\n        color: '#d6d7d9',\n        length: '100%',\n        direction: 'row',\n        hairline: true,\n        margin: 0,\n        dashed: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:14:11\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/lineProgress.js\n */\nexport default {\n    // lineProgress 组件\n    lineProgress: {\n        activeColor: '#19be6b',\n        inactiveColor: '#ececec',\n        percentage: 0,\n        showText: true,\n        height: 12,\n\t\tfromRight: false,\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:45:36\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/link.js\n */\nimport config from '../../libs/config/config'\n\nconst {\n    color\n} = config\nexport default {\n    // link超链接组件props参数\n    link: {\n        color: color['u-primary'],\n        fontSize: 15,\n        underLine: false,\n        href: '',\n        mpTips: '链接已复制，请在浏览器打开',\n        lineColor: '',\n        text: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:14:53\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/list.js\n */\nexport default {\n    // list 组件\n    list: {\n        showScrollbar: false,\n        lowerThreshold: 50,\n        upperThreshold: 0,\n        scrollTop: 0,\n        offsetAccuracy: 10,\n        enableFlex: false,\n        pagingEnabled: false,\n        scrollable: true,\n        scrollIntoView: '',\n        scrollWithAnimation: false,\n        enableBackToTop: false,\n        height: 0,\n        width: 0,\n        preLoadScreen: 1\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:15:40\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/listItem.js\n */\nexport default {\n    // listItem 组件\n    listItem: {\n        anchor: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:45:47\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/loadingIcon.js\n */\nimport config from '../../libs/config/config'\n\nconst {\n    color\n} = config\nexport default {\n    // loading-icon加载中图标组件\n    loadingIcon: {\n        show: true,\n        color: color['u-tips-color'],\n        textColor: color['u-tips-color'],\n        vertical: false,\n        mode: 'spinner',\n        size: 24,\n        textSize: 15,\n        text: '',\n        timingFunction: 'ease-in-out',\n        duration: 1200,\n        inactiveColor: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:00:23\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/loadingPage.js\n */\nexport default {\n    // loading-page组件\n    loadingPage: {\n        loadingText: '正在加载',\n        image: '',\n        loadingMode: 'circle',\n        loading: false,\n        bgColor: '#ffffff',\n        color: '#C8C8C8',\n        fontSize: 19,\n        iconSize: 28,\n        loadingColor: '#C8C8C8',\n        zIndex: 10\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:15:26\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/loadmore.js\n */\nexport default {\n    // loadmore 组件\n    loadmore: {\n        status: 'loadmore',\n        bgColor: 'transparent',\n        icon: true,\n        fontSize: 14,\n\t\ticonSize: 17,\n        color: '#606266',\n        loadingIcon: 'spinner',\n        loadmoreText: '加载更多',\n        loadingText: '正在加载...',\n        nomoreText: '没有更多了',\n        isDot: false,\n        iconColor: '#b7b7b7',\n        marginTop: 10,\n        marginBottom: 10,\n        height: 'auto',\n        line: false,\n\t\tlineColor: '#E6E8EB',\n\t\tdashed: false,\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:15:59\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/modal.js\n */\nexport default {\n    // modal 组件\n    modal: {\n        show: false,\n        title: '',\n        content: '',\n        confirmText: '确认',\n        cancelText: '取消',\n        showConfirmButton: true,\n        showCancelButton: false,\n        confirmColor: '#2979ff',\n        cancelColor: '#606266',\n        buttonReverse: false,\n        zoom: true,\n        asyncClose: false,\n        closeOnClickOverlay: false,\n        negativeTop: 0,\n        width: '650rpx',\n        confirmButtonShape: '',\n        duration: 400,\n        contentTextAlign: 'left',\n        asyncCloseTip: '操作中...',\n        asyncCancelClose: false,\n        contentStyle: {}\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:16:18\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/navbar.js\n */\nimport color from '../../libs/config/color'\nexport default {\n    // navbar 组件\n    navbar: {\n        safeAreaInsetTop: true,\n        placeholder: false,\n        fixed: true,\n        border: false,\n        leftIcon: 'arrow-left',\n        leftText: '',\n        rightText: '',\n        rightIcon: '',\n        title: '',\n        titleColor: '',\n        bgColor: '#ffffff',\n        titleWidth: '400rpx',\n        height: '44px',\n\t\tleftIconSize: 20,\n\t\tleftIconColor: color.mainColor,\n\t\tautoBack: false,\n\t\ttitleStyle: ''\n    }\n\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:16:39\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/noNetwork.js\n */\nexport default {\n    // noNetwork\n    noNetwork: {\n        tips: '哎呀，网络信号丢失',\n        zIndex: '',\n        image: 'data:image/png;base64,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'\n    }\n\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:17:13\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/noticeBar.js\n */\nexport default {\n    // noticeBar\n    noticeBar: {\n        text: [],\n        direction: 'row',\n        step: false,\n        icon: 'volume',\n        mode: '',\n        color: '#f9ae3d',\n        bgColor: '#fdf6ec',\n        speed: 80,\n        fontSize: 14,\n        duration: 2000,\n        disableTouch: true,\n        url: '',\n        linkType: 'navigateTo',\n\t\tjustifyContent: 'flex-start'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:10:21\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/notify.js\n */\nexport default {\n    // notify组件\n    notify: {\n        top: 0,\n        type: 'primary',\n        color: '#ffffff',\n        bgColor: '',\n        message: '',\n        duration: 3000,\n        fontSize: 15,\n        safeAreaInsetTop: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:11:46\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/numberBox.js\n */\nexport default {\n    // 步进器组件\n    numberBox: {\n        name: '',\n        value: 0,\n        min: 1,\n        max: Number.MAX_SAFE_INTEGER,\n        step: 1,\n        integer: false,\n        disabled: false,\n        disabledInput: false,\n        asyncChange: false,\n        inputWidth: 35,\n        showMinus: true,\n        showPlus: true,\n        decimalLength: null,\n        longPress: true,\n        color: '#323233',\n        buttonWidth: 30,\n        buttonSize: 30,\n        buttonRadius: '0px',\n        bgColor: '#EBECEE',\n        inputBgColor: '#EBECEE',\n        cursorSpacing: 100,\n        disableMinus: false,\n        disablePlus: false,\n        iconStyle: '',\n        miniMode: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:08:05\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/numberKeyboard.js\n */\nexport default {\n    // 数字键盘\n    numberKeyboard: {\n        mode: 'number',\n        dotDisabled: false,\n        random: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:06:50\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/overlay.js\n */\nexport default {\n    // overlay组件\n    overlay: {\n        show: false,\n        zIndex: 10070,\n        duration: 300,\n        opacity: 0.5\n    }\n}\n", "/*\n * <AUTHOR> jry\n * @Description  :\n * @version      : 3.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : jry\n * @lastTime     : 2025-05-17 17:17:33\n * @FilePath     : /uview-plus/libs/config/props/parse.js\n */\nexport default {\n    // parse\n    parse: {\n        copyLink: true,\n        errorImg: '',\n        lazyLoad: false,\n        loadingImg: '',\n        pauseVideo: true,\n        previewImg: true,\n        setTitle: true,\n        showImgMenu: true\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:18:20\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/picker.js\n */\nexport default {\n    // picker\n    picker: {\n        show: false,\n\t\tpopupMode: 'bottom',\n        showToolbar: true,\n        title: '',\n        columns: [],\n        loading: false,\n        itemHeight: 44,\n        cancelText: '取消',\n        confirmText: '确定',\n        cancelColor: '#909193',\n        confirmColor: '',\n        visibleItemCount: 5,\n        keyName: 'text',\n\t\tvalueName: 'value',\n        closeOnClickOverlay: false,\n        defaultIndex: [],\n\t\timmediateChange: true,\n\t\tzIndex: 10076,\n        disabled: false,\n        disabledColor: '',\n        placeholder: '请选择',\n        inputProps: {},\n        bgColor: '',\n        round: 0,\n        duration: 300,\n        overlayOpacity: 0.5\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:06:33\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/popup.js\n */\nexport default {\n    // popup组件\n    popup: {\n        show: false,\n        overlay: true,\n        mode: 'bottom',\n        duration: 300,\n        closeable: false,\n        overlayStyle: {},\n        closeOnClickOverlay: true,\n        zIndex: 10075,\n        safeAreaInsetBottom: true,\n        safeAreaInsetTop: false,\n        closeIconPos: 'top-right',\n        round: 0,\n        zoom: true,\n        bgColor: '',\n        overlayOpacity: 0.5\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:02:34\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/radio.js\n */\nexport default {\n    // radio组件\n    radio: {\n        name: '',\n        shape: '',\n        disabled: '',\n        labelDisabled: '',\n        activeColor: '',\n        inactiveColor: '',\n        iconSize: '',\n        labelSize: '',\n        label: '',\n        labelColor: '',\n        size: '',\n        iconColor: '',\n        placement: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : CPS\n * @lastTime     : 2024-11-05 16:01:12\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/radioGroup.js\n */\nexport default {\n    // radio-group组件\n    radioGroup: {\n        value: '',\n        disabled: false,\n        shape: 'circle',\n        activeColor: '#2979ff',\n        inactiveColor: '#c8c9cc',\n        name: '',\n        size: 18,\n        placement: 'row',\n        label: '',\n        labelColor: '#303133',\n        labelSize: 14,\n        labelDisabled: false,\n        iconColor: '#ffffff',\n        iconSize: 12,\n        borderBottom: false,\n        iconPlacement: 'left',\n        gap: \"10px\"\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:05:09\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/rate.js\n */\nexport default {\n    // rate组件\n    rate: {\n        value: 1,\n        count: 5,\n        disabled: false,\n        size: 18,\n        inactiveColor: '#b2b2b2',\n        activeColor: '#FA3534',\n        gutter: 4,\n        minCount: 1,\n        allowHalf: false,\n        activeIcon: 'star-fill',\n        inactiveIcon: 'star',\n        touchable: true\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:18:41\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/readMore.js\n */\nexport default {\n    // readMore\n    readMore: {\n        showHeight: 400,\n        toggle: false,\n        closeText: '展开阅读全文',\n        openText: '收起',\n        color: '#2979ff',\n        fontSize: 14,\n        textIndent: '2em',\n        name: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:18:58\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/row.js\n */\nexport default {\n    // row\n    row: {\n        gutter: 0,\n        justify: 'start',\n        align: 'center'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:19:13\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/rowNotice.js\n */\nexport default {\n    // rowNotice\n    rowNotice: {\n        text: '',\n        icon: 'volume',\n        mode: '',\n        color: '#f9ae3d',\n        bgColor: '#fdf6ec',\n        fontSize: 14,\n        speed: 80\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:19:28\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/scrollList.js\n */\nexport default {\n    // scrollList\n    scrollList: {\n        indicatorWidth: 50,\n        indicatorBarWidth: 20,\n        indicator: true,\n        indicatorColor: '#f2f2f2',\n        indicatorActiveColor: '#3c9cff',\n        indicatorStyle: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:19:45\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/search.js\n */\nexport default {\n    // search\n    search: {\n        shape: 'round',\n        bgColor: '#f2f2f2',\n        placeholder: '请输入关键字',\n        clearabled: true,\n        focus: false,\n        showAction: true,\n        actionStyle: {},\n        actionText: '搜索',\n        inputAlign: 'left',\n        inputStyle: {},\n        disabled: false,\n        borderColor: 'transparent',\n        searchIconColor: '#909399',\n        searchIconSize: 22,\n        color: '#606266',\n        placeholderColor: '#909399',\n        searchIcon: 'search',\n        iconPosition: 'left',\n        margin: '0',\n        animation: false,\n        value: '',\n        maxlength: '-1',\n        height: 32,\n        label: null\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:07:33\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/section.js\n */\nexport default {\n    // u-section组件\n    section: {\n        title: '',\n        subTitle: '更多',\n        right: true,\n        fontSize: 15,\n        bold: true,\n        color: '#303133',\n        subColor: '#909399',\n        showLine: true,\n        lineColor: '',\n        arrow: true\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:20:14\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/skeleton.js\n */\nexport default {\n    // skeleton\n    skeleton: {\n        loading: true,\n        animate: true,\n        rows: 0,\n        rowsWidth: '100%',\n        rowsHeight: 18,\n        title: true,\n        titleWidth: '50%',\n        titleHeight: 18,\n        avatar: false,\n        avatarSize: 32,\n        avatarShape: 'circle'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:08:25\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/slider.js\n */\nexport default {\n    // slider组件\n    slider: {\n        value: 0,\n        blockSize: 18,\n        min: 0,\n        max: 100,\n        step: 1,\n        activeColor: '#2979ff',\n        inactiveColor: '#c0c4cc',\n        blockColor: '#ffffff',\n        showValue: false,\n\t\tdisabled:false,\n        blockStyle: {},\n        useNative: false,\n        height: '2px'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:20:39\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/statusBar.js\n */\nexport default {\n    // statusBar\n    statusBar: {\n        bgColor: 'transparent',\n\t\theight: 0\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:12:37\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/steps.js\n */\nexport default {\n    // steps组件\n    steps: {\n        direction: 'row',\n        current: 0,\n        activeColor: '#3c9cff',\n        inactiveColor: '#969799',\n        activeIcon: '',\n        inactiveIcon: '',\n        dot: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:12:55\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/stepsItem.js\n */\nexport default {\n    // steps-item组件\n    stepsItem: {\n        title: '',\n        desc: '',\n        iconSize: 17,\n        error: false\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:01:30\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/sticky.js\n */\nexport default {\n    // sticky组件\n    sticky: {\n        offsetTop: 0,\n        customNavHeight: 0,\n        disabled: false,\n        bgColor: 'transparent',\n        zIndex: '',\n        index: ''\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:12:20\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/subsection.js\n */\nexport default {\n    // subsection组件\n    subsection: {\n        list: [],\n        current: 0,\n        activeColor: '#3c9cff',\n        inactiveColor: '#303133',\n        mode: 'button',\n        fontSize: 12,\n        bold: true,\n        bgColor: '#eeeeef',\n        keyName: 'name',\n        activeColorKeyName: 'activeColorKey',\n        inactiveColorKeyName: 'inactiveColorKey',\n        disabled: false,\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:00:42\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swipeAction.js\n */\nexport default {\n    // swipe-action组件\n    swipeAction: {\n        autoClose: true\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:01:13\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swipeActionItem.js\n */\nexport default {\n    // swipeActionItem 组件\n    swipeActionItem: {\n        show: false,\n        closeOnClick: true,\n        name: '',\n        disabled: false,\n        threshold: 20,\n        autoClose: true,\n        options: [],\n        duration: 300\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:21:38\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swiper.js\n */\nexport default {\n    // swiper 组件\n    swiper: {\n        list: [],\n        indicator: false,\n        indicatorActiveColor: '#FFFFFF',\n        indicatorInactiveColor: 'rgba(255, 255, 255, 0.35)',\n        indicatorStyle: '',\n        indicatorMode: 'line',\n        autoplay: true,\n        current: 0,\n        currentItemId: '',\n        interval: 3000,\n        duration: 300,\n        circular: false,\n        previousMargin: 0,\n        nextMargin: 0,\n        acceleration: false,\n        displayMultipleItems: 1,\n        easingFunction: 'default',\n        keyName: 'url',\n        imgMode: 'aspectFill',\n        height: 130,\n        bgColor: '#f3f4f6',\n        radius: 4,\n        loading: false,\n        showTitle: false\n    }\n\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:22:07\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swiperIndicator.js\n */\nexport default {\n    // swiperIndicator 组件\n    swiperIndicator: {\n        length: 0,\n        current: 0,\n        indicatorActiveColor: '',\n        indicatorInactiveColor: '',\n\t\tindicatorMode: 'line'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:22:24\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/switch.js\n */\nexport default {\n    // switch\n    switch: {\n        loading: false,\n        disabled: false,\n        size: 25,\n        activeColor: '#2979ff',\n        inactiveColor: '#ffffff',\n        value: false,\n        activeValue: true,\n        inactiveValue: false,\n        asyncChange: false,\n        space: 0\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:22:40\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tabbar.js\n */\nexport default {\n    // tabbar\n    tabbar: {\n        value: null,\n        safeAreaInsetBottom: true,\n        border: true,\n        zIndex: 1,\n        activeColor: '#1989fa',\n        inactiveColor: '#7d7e80',\n        fixed: true,\n        placeholder: true\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:22:55\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tabbarItem.js\n */\nexport default {\n    //\n    tabbarItem: {\n        name: null,\n        icon: '',\n        badge: null,\n        dot: false,\n        text: '',\n        badgeStyle: 'top: 6px;right:2px;'\n    }\n}\n", "/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:23:14\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tabs.js\r\n */\r\nexport default {\r\n    //\r\n    tabs: {\r\n        duration: 300,\r\n        list: [],\r\n        lineColor: '#3c9cff',\r\n        activeStyle: {\r\n            color: '#303133'\r\n        },\r\n        inactiveStyle: {\r\n            color: '#606266'\r\n        },\r\n        lineWidth: 20,\r\n        lineHeight: 3,\r\n        lineBgSize: 'cover',\r\n        itemStyle: {\r\n            height: '44px'\r\n        },\r\n        scrollable: true,\r\n\t\tcurrent: 0,\r\n\t\tkeyName: 'name',\r\n        iconStyle: {}\r\n    }\r\n}\r\n", "/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:23:37\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tag.js\r\n */\r\nexport default {\r\n\t// tag 组件\r\n\ttag: {\r\n\t\ttype: 'primary',\r\n\t\tdisabled: false,\r\n\t\tsize: 'medium',\r\n\t\tshape: 'square',\r\n\t\ttext: '',\r\n\t\tbgColor: '',\r\n\t\tcolor: '',\r\n\t\tborderColor: '',\r\n\t\tcloseColor: '#C6C7CB',\r\n\t\tname: '',\r\n\t\tplainFill: false,\r\n\t\tplain: false,\r\n\t\tclosable: false,\r\n\t\tshow: true,\r\n\t\ticon: '',\r\n\t\ticonColor: '',\r\n\t\ttextSize: '',\r\n\t\theight: '',\r\n\t\tpadding: '',\r\n\t\tborderRadius: '',\r\n\t\tautoBgColor: 0\r\n\t}\r\n}", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:23:58\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/text.js\n */\nexport default {\n    // text 组件\n    text: {\n        type: '',\n        show: true,\n        text: '',\n        prefixIcon: '',\n        suffixIcon: '',\n        mode: '',\n        href: '',\n        format: '',\n        call: false,\n        openType: '',\n        bold: false,\n        block: false,\n        lines: '',\n        color: '#303133',\n        size: 15,\n        iconStyle: {\n            fontSize: '15px'\n        },\n        decoration: 'none',\n        margin: 0,\n        lineHeight: '',\n        align: 'left',\n        wordWrap: 'normal',\n\t\tflex1: true\n    }\n\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:24:32\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/textarea.js\n */\nexport default {\n\t// textarea 组件\n\ttextarea: {\n\t\tvalue: '',\n\t\tplaceholder: '',\n\t\tplaceholderClass: 'textarea-placeholder',\n\t\tplaceholderStyle: 'color: #c0c4cc',\n\t\theight: 70,\n\t\tconfirmType: 'done',\n\t\tdisabled: false,\n\t\tcount: false,\n\t\tfocus: false,\n\t\tautoHeight: false,\n\t\tfixed: false,\n\t\tcursorSpacing: 0,\n\t\tcursor: '',\n\t\tshowConfirmBar: true,\n\t\tselectionStart: -1,\n\t\tselectionEnd: -1,\n\t\tadjustPosition: true,\n\t\tdisableDefaultPadding: false,\n\t\tholdKeyboard: false,\n\t\tmaxlength: 140,\n\t\tborder: 'surround',\n\t\tformatter: null\n\t}\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:07:07\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/toast.js\n */\nexport default {\n    // toast组件\n    toast: {\n        zIndex: 10090,\n        loading: false,\n        message: '',\n        icon: '',\n        type: '',\n        loadingMode: '',\n        show: '',\n        overlay: false,\n        position: 'center',\n        params: {},\n        duration: 2000,\n        isTab: false,\n        url: '',\n        callback: null,\n        back: false\n    }\n\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:24:55\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/toolbar.js\n */\nexport default {\n    // toolbar 组件\n    toolbar: {\n        show: true,\n        cancelText: '取消',\n        confirmText: '确认',\n        cancelColor: '#909193',\n        confirmColor: '',\n        title: ''\n    }\n\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:25:14\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tooltip.js\n */\nexport default {\n    // tooltip 组件\n    tooltip: {\n        text: '',\n        copyText: '',\n        size: 14,\n        color: '#606266',\n        bgColor: 'transparent',\n        direction: 'top',\n        zIndex: 10071,\n        showCopy: true,\n        buttons: [],\n        overlay: true,\n        showToast: true\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:59:00\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/transition.js\n */\nexport default {\n    // transition动画组件的props\n    transition: {\n        show: false,\n        mode: 'fade',\n        duration: '300',\n        timingFunction: 'ease-out'\n    }\n}\n", "/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:09:50\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/upload.js\n */\nexport default {\n\t// upload组件\n\tupload: {\n\t\taccept: 'image',\n\t\textension: [],\n\t\tcapture: ['album', 'camera'],\n\t\tcompressed: true,\n\t\tcamera: 'back',\n\t\tmaxDuration: 60,\n\t\tuploadIcon: 'camera-fill',\n\t\tuploadIconColor: '#D3D4D6',\n\t\tuseBeforeRead: false,\n\t\tpreviewFullImage: true,\n\t\tmaxCount: 52,\n\t\tdisabled: false,\n\t\timageMode: 'aspectFill',\n\t\tname: '',\n\t\tsizeType: ['original', 'compressed'],\n\t\tmultiple: false,\n\t\tdeletable: true,\n\t\tmaxSize: Number.MAX_VALUE,\n\t\tfileList: [],\n\t\tuploadText: '',\n\t\twidth: 80,\n\t\theight: 80,\n\t\tpreviewImage: true,\n\t\tautoDelete: false,\n\t\tautoUpload: false,\n\t\tautoUploadApi: '',\n\t\tautoUploadAuthUrl: '',\n\t\tautoUploadDriver: '',\n\t\tautoUploadHeader: {},\n\t\tgetVideoThumb: false,\n\t\tcustomAfterAutoUpload: false,\n\t\tvideoPreviewObjectFit: 'cover'\n\t}\n}\n", "/**\n * 此文件的作用为统一配置所有组件的props参数\n * 借此用户可以全局覆盖组件的props默认值\n * 无需在每个引入组件的页面中都配置一次\n */\nimport config from './config'\n// 各个需要fixed的地方的z-index配置文件\nimport zIndex from './zIndex.js'\n// 关于颜色的配置，特殊场景使用\nimport color from './color.js'\n// http\nimport http from '../function/http.js'\nimport { shallowMerge } from '../function/index.js'\n// 组件props\nimport ActionSheet from '../../components/u-action-sheet/actionSheet'\nimport Album from '../../components/u-album/album'\nimport Alert from '../../components/u-alert/alert'\nimport Avatar from '../../components/u-avatar/avatar'\nimport AvatarGroup from '../../components/u-avatar-group/avatarGroup'\nimport Backtop from '../../components/u-back-top/backtop'\nimport Badge from '../../components/u-badge/badge'\nimport Button from '../../components/u-button/button'\nimport Calendar from '../../components/u-calendar/calendar'\nimport CarKeyboard from '../../components/u-car-keyboard/carKeyboard'\nimport Card from '../../components/u-card/card'\nimport Cell from '../../components/u-cell/cell'\nimport CellGroup from '../../components/u-cell-group/cellGroup'\nimport Checkbox from '../../components/u-checkbox/checkbox'\nimport CheckboxGroup from '../../components/u-checkbox-group/checkboxGroup'\nimport CircleProgress from '../../components/u-circle-progress/circleProgress'\nimport Code from '../../components/u-code/code'\nimport CodeInput from '../../components/u-code-input/codeInput'\nimport Col from '../../components/u-col/col'\nimport Collapse from '../../components/u-collapse/collapse'\nimport CollapseItem from '../../components/u-collapse-item/collapseItem'\nimport ColumnNotice from '../../components/u-column-notice/columnNotice'\nimport CountDown from '../../components/u-count-down/countDown'\nimport CountTo from '../../components/u-count-to/countTo'\nimport DatetimePicker from '../../components/u-datetime-picker/datetimePicker'\nimport Divider from '../../components/u-divider/divider'\nimport Empty from '../../components/u-empty/empty'\nimport Form from '../../components/u-form/form'\nimport GormItem from '../../components/u-form-item/formItem'\nimport Gap from '../../components/u-gap/gap'\nimport Grid from '../../components/u-grid/grid'\nimport GridItem from '../../components/u-grid-item/gridItem'\nimport Icon from '../../components/u-icon/icon'\nimport Image from '../../components/u-image/image'\nimport IndexAnchor from '../../components/u-index-anchor/indexAnchor'\nimport IndexList from '../../components/u-index-list/indexList'\nimport Input from '../../components/u-input/input'\nimport Keyboard from '../../components/u-keyboard/keyboard'\nimport Line from '../../components/u-line/line'\nimport LineProgress from '../../components/u-line-progress/lineProgress'\nimport Link from '../../components/u-link/link'\nimport List from '../../components/u-list/list'\nimport ListItem from '../../components/u-list-item/listItem'\nimport LoadingIcon from '../../components/u-loading-icon/loadingIcon'\nimport LoadingPage from '../../components/u-loading-page/loadingPage'\nimport Loadmore from '../../components/u-loadmore/loadmore'\nimport Modal from '../../components/u-modal/modal'\nimport Navbar from '../../components/u-navbar/navbar'\nimport NoNetwork from '../../components/u-no-network/noNetwork'\nimport NoticeBar from '../../components/u-notice-bar/noticeBar'\nimport Notify from '../../components/u-notify/notify'\nimport NumberBox from '../../components/u-number-box/numberBox'\nimport NumberKeyboard from '../../components/u-number-keyboard/numberKeyboard'\nimport Overlay from '../../components/u-overlay/overlay'\nimport Parse from '../../components/u-parse/parse'\nimport Picker from '../../components/u-picker/picker'\nimport Popup from '../../components/u-popup/popup'\nimport Radio from '../../components/u-radio/radio'\nimport RadioGroup from '../../components/u-radio-group/radioGroup'\nimport Rate from '../../components/u-rate/rate'\nimport ReadMore from '../../components/u-read-more/readMore'\nimport Row from '../../components/u-row/row'\nimport RowNotice from '../../components/u-row-notice/rowNotice'\nimport ScrollList from '../../components/u-scroll-list/scrollList'\nimport Search from '../../components/u-search/search'\nimport Section from '../../components/u-section/section'\nimport Skeleton from '../../components/u-skeleton/skeleton'\nimport Slider from '../../components/u-slider/slider'\nimport StatusBar from '../../components/u-status-bar/statusBar'\nimport Steps from '../../components/u-steps/steps'\nimport StepsItem from '../../components/u-steps-item/stepsItem'\nimport Sticky from '../../components/u-sticky/sticky'\nimport Subsection from '../../components/u-subsection/subsection'\nimport SwipeAction from '../../components/u-swipe-action/swipeAction'\nimport SwipeActionItem from '../../components/u-swipe-action-item/swipeActionItem'\nimport Swiper from '../../components/u-swiper/swiper'\nimport SwipterIndicator from '../../components/u-swiper-indicator/swipterIndicator'\nimport Switch from '../../components/u-switch/switch'\nimport Tabbar from '../../components/u-tabbar/tabbar'\nimport TabbarItem from '../../components/u-tabbar-item/tabbarItem'\nimport Tabs from '../../components/u-tabs/tabs'\nimport Tag from '../../components/u-tag/tag'\nimport Text from '../../components/u-text/text'\nimport Textarea from '../../components/u-textarea/textarea'\nimport Toast from '../../components/u-toast/toast'\nimport Toolbar from '../../components/u-toolbar/toolbar'\nimport Tooltip from '../../components/u-tooltip/tooltip'\nimport Transition from '../../components/u-transition/transition'\nimport Upload from '../../components/u-upload/upload'\n\nconst props = {\n    ...ActionSheet,\n    ...Album,\n    ...Alert,\n    ...Avatar,\n    ...AvatarGroup,\n    ...Backtop,\n    ...Badge,\n    ...Button,\n    ...Calendar,\n    ...CarKeyboard,\n    ...Card,\n    ...Cell,\n    ...CellGroup,\n    ...Checkbox,\n    ...CheckboxGroup,\n    ...CircleProgress,\n    ...Code,\n    ...CodeInput,\n    ...Col,\n    ...Collapse,\n    ...CollapseItem,\n    ...ColumnNotice,\n    ...CountDown,\n    ...CountTo,\n    ...DatetimePicker,\n    ...Divider,\n    ...Empty,\n    ...Form,\n    ...GormItem,\n    ...Gap,\n    ...Grid,\n    ...GridItem,\n    ...Icon,\n    ...Image,\n    ...IndexAnchor,\n    ...IndexList,\n    ...Input,\n    ...Keyboard,\n    ...Line,\n    ...LineProgress,\n    ...Link,\n    ...List,\n    ...ListItem,\n    ...LoadingIcon,\n    ...LoadingPage,\n    ...Loadmore,\n    ...Modal,\n    ...Navbar,\n    ...NoNetwork,\n    ...NoticeBar,\n    ...Notify,\n    ...NumberBox,\n    ...NumberKeyboard,\n    ...Overlay,\n    ...Parse,\n    ...Picker,\n    ...Popup,\n    ...Radio,\n    ...RadioGroup,\n    ...Rate,\n    ...ReadMore,\n    ...Row,\n    ...RowNotice,\n    ...ScrollList,\n    ...Search,\n    ...Section,\n    ...Skeleton,\n    ...Slider,\n    ...StatusBar,\n    ...Steps,\n    ...StepsItem,\n    ...Sticky,\n    ...Subsection,\n    ...SwipeAction,\n    ...SwipeActionItem,\n    ...Swiper,\n    ...SwipterIndicator,\n    ...Switch,\n    ...Tabbar,\n    ...TabbarItem,\n    ...Tabs,\n    ...Tag,\n    ...Text,\n    ...Textarea,\n    ...Toast,\n    ...Toolbar,\n    ...Tooltip,\n    ...Transition,\n    ...Upload\n}\n\nfunction setConfig(configs) {\n\tshallowMerge(config, configs.config || {})\n\tshallowMerge(props, configs.props || {})\n\tshallowMerge(color, configs.color || {})\n\tshallowMerge(zIndex, configs.zIndex || {})\n}\n\n// 初始化自定义配置\nif (uni && uni.upuiParams) {\n\tconsole.log('setting uview-plus')\n\tlet temp = uni.upuiParams()\n\tif (temp.httpIns) {\n\t\ttemp.httpIns(http)\n\t}\n\tif (temp.options) {\n\t\tsetConfig(temp.options)\n\t}\n}\n\nexport default props\n", "/**\n * 注意：\n * 此部分内容，在vue-cli模式下，需要在vue.config.js加入如下内容才有效：\n * module.exports = {\n *     transpileDependencies: ['uview-v2']\n * }\n */\n\nlet platform = 'none'\n\n\nplatform = 'vue3'\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nplatform = 'h5'\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nexport default platform\n", "import config from '../../libs/config/config';\n// 定义高阶函数\nfunction once(fn) {\n    let called = false;\n    let result;\n\n    return function(...args) {\n        if (!called) {\n            result = fn.apply(this, args);\n            called = true;\n        }\n        return result;\n    };\n}\n\n// 使用高阶函数\nconst loadFont = once(() => {\n    // console.log('这个函数只能执行一次');\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    uni.loadFontFace({\n        global: true, // 是否全局生效。微信小程序 '2.10.0'起支持全局生效，需在 app.vue 中调用。\n        family: 'uicon-iconfont',\n        source: 'url(\"' + config.iconUrl + '\")',\n        success() {\n            // console.log('内置字体图标加载成功');\n        },\n        fail() {\n            // console.error('内置字体图标加载出错');\n        }\n    });\n    if (config.customIcon.family) {\n        uni.loadFontFace({\n            global: true, // 是否全局生效。微信小程序 '2.10.0'起支持全局生效，需在 app.vue 中调用。\n            family: config.customIcon.family,\n            source: 'url(\"' + config.customIcon.url + '\")',\n            success() {\n                // console.log('扩展字体图标加载成功');\n            },\n            fail() {\n                // console.error('扩展字体图标加载出错');\n            }\n        });\n    }\n\n\n\n\n\n\n\n\n\n    return true;\n});\n\nlet fontUtil = {\n    loadFont\n}\n\nexport default fontUtil\n", "// 看到此报错，是因为没有配置vite.config.js的【transpileDependencies】\n// const pleaseSetTranspileDependencies = {}, babelTest = pleaseSetTranspileDependencies?.test\n\n// 引入全局mixin\nimport { mixin } from './libs/mixin/mixin.js'\n// 小程序特有的mixin\nimport { mpMixin } from './libs/mixin/mpMixin.js'\n\n// 路由封装\nimport route from './libs/util/route.js'\n// 颜色渐变相关,colorGradient-颜色渐变,hexToRgb-十六进制颜色转rgb颜色,rgbToHex-rgb转十六进制\nimport colorGradient from './libs/function/colorGradient.js'\n\n// 规则检验\nimport test from './libs/function/test.js'\n// 防抖方法\nimport debounce from './libs/function/debounce.js'\n// 节流方法\nimport throttle from './libs/function/throttle.js'\n// 浮点计算\nimport calc from './libs/function/calc.js'\n// 浮点计算\nimport digit from './libs/function/digit.js'\n// 公共文件写入的方法\nimport index from './libs/function/index.js'\n\n// 配置信息\nimport config from './libs/config/config.js'\n// props配置信息\nimport props from './libs/config/props.js'\n// 各个需要fixed的地方的z-index配置文件\nimport zIndex from './libs/config/zIndex.js'\n// 关于颜色的配置，特殊场景使用\nimport color from './libs/config/color.js'\n// 平台\nimport platform from './libs/function/platform'\n\n// http\nimport http from './libs/function/http.js'\n\n// fontUtil\nimport fontUtil from './components/u-icon/util.js';\n\n// 导出\nlet themeType = ['primary', 'success', 'error', 'warning', 'info'];\nexport { route, http, debounce, throttle, calc, digit, platform, themeType, mixin, mpMixin, props, color, test, zIndex, fontUtil }\nexport * from './libs/function/index.js'\nexport * from './libs/function/colorGradient.js'\n\n/**\n * @description 修改uView内置属性值\n * @param {object} props 修改内置props属性\n * @param {object} config 修改内置config属性\n * @param {object} color 修改内置color属性\n * @param {object} zIndex 修改内置zIndex属性\n */\nexport function setConfig(configs) {\n\tindex.shallowMerge(config, configs.config || {})\n\tindex.shallowMerge(props, configs.props || {})\n\tindex.shallowMerge(color, configs.color || {})\n\tindex.shallowMerge(zIndex, configs.zIndex || {})\n}\nindex.setConfig = setConfig\n\nconst $u = {\n    route,\n    date: index.timeFormat, // 另名date\n    colorGradient: colorGradient.colorGradient,\n    hexToRgb: colorGradient.hexToRgb,\n    rgbToHex: colorGradient.rgbToHex,\n    colorToRgba: colorGradient.colorToRgba,\n    test,\n    type: themeType,\n    http,\n    config, // uview-plus配置信息相关，比如版本号\n    zIndex,\n    debounce,\n    throttle,\n\tcalc,\n    mixin,\n    mpMixin,\n    props,\n    ...index,\n    color,\n    platform\n}\n\nexport const mount$u = function() {\n    uni.$u = $u\n}\n\nfunction toCamelCase(str) {\n    return str.replace(/-([a-z])/g, function(match, group1) {\n      return group1.toUpperCase();\n    }).replace(/^[a-z]/, function(match) {\n      return match.toUpperCase();\n    });\n}\n\n\nconst importFn = import.meta.glob('./components/u-*/u-*.vue', { eager: true })\nlet components = [];\n\n// 批量注册全局组件\nfor (const key in importFn) {\n    let component = importFn[key].default;\n    if (component.name && component.name.indexOf('u--') !== 0) {\n        component.install = function (Vue) {\n            Vue.component(name, component);\n        };\n        \n        // 导入组件\n        components.push(component);\n    }\n}\n\n\nconst install = (Vue, upuiParams = '') => {\n\n    components.forEach(function(component) {\n        const name = component.name.replace(/u-([a-zA-Z0-9-_]+)/g, 'up-$1');\n\t\tif (name != component.name) {\n\t\t\tVue.component(component.name, component); \n\t\t}\n        Vue.component(name, component); \n    });\n\n\t\n\t// 初始化\n\tif (upuiParams) {\n\t\tuni.upuiParams = upuiParams\n\t\tlet temp = upuiParams()\n\t\tif (temp.httpIns) {\n\t\t\ttemp.httpIns(http)\n\t\t}\n\t\tif (temp.options) {\n\t\t\tsetConfig(temp.options)\n\t\t}\n\t}\n\n    // 同时挂载到uni和Vue.prototype中\n    // $u挂载到uni对象上\n    uni.$u = $u\n\n\n    // 只有vue，挂载到Vue.prototype才有意义，因为nvue中全局Vue.prototype和Vue.mixin是无效的\n    Vue.config.globalProperties.$u = $u\n    Vue.mixin(mixin)\n\n}\n\nexport default {\n    install\n}\n"], "mappings": ";;;AAAO,IAAM,cAAc,CAAC,YAAY;AACtC,SAAO;AACT;;;ACCO,SAAS,MAAM,OAAO;AACzB,SAAO,0EAA0E,KAAK,KAAK;AAC/F;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,qBAAqB,KAAK,KAAK;AAC1C;AAKO,SAAS,IAAI,OAAO;AACvB,SAAO,8QACF,KAAK,KAAK;AACnB;AAMO,SAAS,KAAK,OAAO;AACxB,MAAI,CAAC;AAAO,WAAO;AAEnB,MAAI,OAAO,UAAU,UAAU;AAE3B,QAAI,MAAM,SAAS,EAAE,WAAW,MAAM,MAAM,SAAS,EAAE,WAAW,IAAI;AAClE,aAAO;AAAA,IACX;AACA,WAAO,CAAC,MAAM,IAAI,KAAK,KAAK,EAAE,QAAQ,CAAC;AAAA,EAC3C;AACA,MAAI,OAAO,UAAU,UAAU;AAE3B,UAAM,OAAO,OAAO,KAAK;AACzB,QAAI,CAAC,MAAM,IAAI,GAAG;AACd,UACI,KAAK,SAAS,EAAE,WAAW,MAC3B,KAAK,SAAS,EAAE,WAAW,IAC7B;AACE,eAAO,CAAC,MAAM,IAAI,KAAK,IAAI,EAAE,QAAQ,CAAC;AAAA,MAC1C;AAAA,IACJ;AAEA,QAAI,MAAM,SAAS,MAAM,MAAM,SAAS,IAAI;AACxC,aAAO;AAAA,IACX;AACA,UAAM,YACF;AACJ,QAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AACxB,aAAO;AAAA,IACX;AAEA,UAAM,YAAY,IAAI,KAAK,KAAK;AAChC,WAAO,CAAC,MAAM,UAAU,QAAQ,CAAC;AAAA,EACrC;AAEA,SAAO;AACX;AAKO,SAAS,QAAQ,OAAO;AAC3B,SAAO,+DAA+D,KAAK,KAAK;AACpF;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,0CAA0C,KAAK,KAAK;AAC/D;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,OAAO,UAAU;AAC5B;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,QAAQ,KAAK,KAAK;AAC7B;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,2EAA2E;AAAA,IAC9E;AAAA,EACJ;AACJ;AAKO,SAAS,MAAM,OAAO;AAEzB,QAAM,OAAO;AAEb,QAAM,OAAO;AACb,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO,KAAK,KAAK,KAAK;AAAA,EAC1B;AAAE,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,KAAK,KAAK,KAAK;AAAA,EAC1B;AACA,SAAO;AACX;AAKO,SAAS,OAAO,OAAO;AAE1B,SAAO,+CAA+C,KAAK,KAAK;AACpE;AAKO,SAAS,QAAQ,OAAO;AAC3B,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,cAAc,KAAK,KAAK;AACnC;AAKO,SAAS,QAAQ,OAAO;AAE3B,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKO,SAAS,SAAS,OAAO,OAAO;AACnC,SAAO,MAAM,QAAQ,KAAK,KAAK;AACnC;AAKO,SAAS,MAAM,OAAO,OAAO;AAChC,SAAO,SAAS,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC;AAChD;AAKO,SAAS,YAAY,OAAO,OAAO;AACtC,SAAO,MAAM,UAAU,MAAM,CAAC,KAAK,MAAM,UAAU,MAAM,CAAC;AAC9D;AAKO,SAAS,SAAS,OAAO;AAC5B,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKO,SAAS,MAAM,OAAO;AACzB,UAAQ,OAAO,OAAO;AAAA,IACtB,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,UAAI,MAAM,QAAQ,gCAAgC,EAAE,EAAE,UAAU;AAAG,eAAO;AAC1E;AAAA,IACJ,KAAK;AACD,UAAI,CAAC;AAAO,eAAO;AACnB;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,KAAK,MAAM,KAAK;AAAG,eAAO;AACxC;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,QAAQ,MAAM,WAAW;AAAG,eAAO;AACjD,iBAAW,KAAK,OAAO;AACnB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,EACX;AACA,SAAO;AACX;AAKO,SAAS,WAAW,OAAO;AAC9B,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI;AACA,YAAM,MAAM,KAAK,MAAM,KAAK;AAC5B,UAAI,OAAO,QAAQ,YAAY,KAAK;AAChC,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,SAAS,GAAG;AACR,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAKO,SAAS,MAAM,OAAO;AACzB,MAAI,OAAO,MAAM,YAAY,YAAY;AACrC,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC9B;AACA,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;AAKO,SAAS,cAAc,OAAO;AACjC,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;AAKO,SAAS,KAAK,OAAO,MAAM,GAAG;AACjC,SAAO,IAAI,OAAO,QAAQ,GAAG,IAAI,EAAE,KAAK,KAAK;AACjD;AAMO,SAAS,KAAK,OAAO;AACxB,SAAO,OAAO,UAAU;AAC5B;AAMO,SAAS,QAAQ,OAAO;AAC3B,SAAO,cAAc,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,KAAK;AACvE;AAKO,SAAS,MAAM,OAAO;AACzB,QAAM,WAAW,MAAM,MAAM,GAAG,EAAE,CAAC;AACnC,QAAM,eAAe;AACrB,SAAO,aAAa,KAAK,QAAQ;AACrC;AAMO,SAAS,MAAM,OAAO;AACzB,QAAM,eAAe;AACrB,SAAO,aAAa,KAAK,KAAK;AAClC;AAOO,SAAS,OAAO,GAAG;AACtB,SAAO,KAAK,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AACtD;AAEA,IAAO,eAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;ACtUA,IAAI,yBAAyB;AAOtB,SAAS,MAAM,KAAK,YAAY,IAAI;AACzC,SAAO,CAAC,WAAW,OAAO,GAAG,EAAE,YAAY,SAAS,CAAC;AACvD;AAOO,SAAS,YAAY,KAAK;AAE/B,QAAM,SAAS,IAAI,SAAS,EAAE,MAAM,MAAM;AAC1C,QAAM,OAAO,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI,SAAS,EAAE,OAAO,CAAC,KAAK;AACpE,SAAO,MAAM,IAAI,MAAM;AACzB;AAOO,SAAS,YAAY,KAAK;AAC/B,MAAI,IAAI,SAAS,EAAE,QAAQ,GAAG,MAAM,IAAI;AACtC,WAAO,OAAO,IAAI,SAAS,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,EAC/C;AACA,QAAM,OAAO,YAAY,GAAG;AAC5B,SAAO,OAAO,IAAI,MAAM,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,OAAO,GAAG;AACxE;AAOO,SAAS,cAAc,KAAK;AACjC,MAAI,wBAAwB;AAC1B,QAAI,MAAM,OAAO,oBAAoB,MAAM,OAAO,kBAAkB;AAClE,cAAQ,KAAK,GAAG,GAAG,kBAAkB;AAAA,IACvC;AAAA,EACF;AACF;AAQO,SAAS,kBAAkB,KAAK,WAAW;AAChD,QAAM,CAAC,MAAM,MAAM,GAAG,MAAM,IAAI;AAChC,MAAI,MAAM,UAAU,MAAM,IAAI;AAE9B,SAAO,QAAQ,CAAC,QAAQ;AACtB,UAAM,UAAU,KAAK,GAAG;AAAA,EAC1B,CAAC;AAED,SAAO;AACT;AAMO,SAAS,SAAS,MAAM;AAC7B,MAAI,KAAK,SAAS,GAAG;AACnB,WAAO,kBAAkB,MAAM,KAAK;AAAA,EACtC;AAEA,QAAM,CAAC,MAAM,IAAI,IAAI;AACrB,QAAM,cAAc,YAAY,IAAI;AACpC,QAAM,cAAc,YAAY,IAAI;AACpC,QAAM,UAAU,YAAY,IAAI,IAAI,YAAY,IAAI;AACpD,QAAM,YAAY,cAAc;AAEhC,gBAAc,SAAS;AAEvB,SAAO,YAAY,KAAK,IAAI,IAAI,OAAO;AACzC;AAMO,SAAS,QAAQ,MAAM;AAC5B,MAAI,KAAK,SAAS,GAAG;AACnB,WAAO,kBAAkB,MAAM,IAAI;AAAA,EACrC;AAEA,QAAM,CAAC,MAAM,IAAI,IAAI;AAErB,QAAM,UAAU,KAAK,IAAI,IAAI,KAAK,IAAI,YAAY,IAAI,GAAG,YAAY,IAAI,CAAC,CAAC;AAE3E,UAAQ,MAAM,MAAM,OAAO,IAAI,MAAM,MAAM,OAAO,KAAK;AACzD;AAMO,SAAS,SAAS,MAAM;AAC7B,MAAI,KAAK,SAAS,GAAG;AACnB,WAAO,kBAAkB,MAAM,KAAK;AAAA,EACtC;AAEA,QAAM,CAAC,MAAM,IAAI,IAAI;AACrB,QAAM,UAAU,KAAK,IAAI,IAAI,KAAK,IAAI,YAAY,IAAI,GAAG,YAAY,IAAI,CAAC,CAAC;AAC3E,UAAQ,MAAM,MAAM,OAAO,IAAI,MAAM,MAAM,OAAO,KAAK;AACzD;AAMO,SAAS,UAAU,MAAM;AAC9B,MAAI,KAAK,SAAS,GAAG;AACnB,WAAO,kBAAkB,MAAM,MAAM;AAAA,EACvC;AAEA,QAAM,CAAC,MAAM,IAAI,IAAI;AACrB,QAAM,cAAc,YAAY,IAAI;AACpC,QAAM,cAAc,YAAY,IAAI;AACpC,gBAAc,WAAW;AACzB,gBAAc,WAAW;AAEzB,SAAO,MAAM,cAAc,aAAa,MAAM,KAAK,IAAI,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,CAAC,CAAC;AACpG;AAMO,SAAS,MAAM,KAAK,OAAO;AAChC,QAAM,OAAO,KAAK,IAAI,IAAI,KAAK;AAC/B,MAAI,SAAS,OAAO,KAAK,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI;AAChE,MAAI,MAAM,KAAK,WAAW,GAAG;AAC3B,aAAS,MAAM,QAAQ,EAAE;AAAA,EAC3B;AAEA,SAAO;AACT;AAOO,SAAS,uBAAuBA,QAAO,MAAM;AAClD,2BAAyBA;AAC3B;AAGA,IAAO,gBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACrKA,IAAM,UAAU;AAGhB,IAAI,MAAwC;AAC3C,UAAQ,IAAI;AAAA,kBAAqB,OAAO;AAAA;AAAA,GAA+C,uDAAuD,oDAAoD;AACnM;AAEA,IAAO,iBAAQ;AAAA,EACX,GAAG;AAAA,EACH;AAAA;AAAA,EAEA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,OAAO;AAAA,IACH,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA,IACX,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACtB;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,YAAY;AAAA,IACR,QAAQ;AAAA,IACR,KAAK;AAAA,EACT;AAAA,EACA,aAAa,CAAC;AAAA;AAAA;AAAA,EAEjB,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,IACZ,iBAAiB;AAAA,EAClB;AACD;;;ACxCO,SAASC,OAAM,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG;AAClD,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,CAAC,CAAC;AAClD;AAQO,SAAS,MAAM,OAAO,OAAO,OAAO;AAC1C,MAAI,OAAW,KAAK,GAAG;AACtB,WAAO,OAAO,GAAG,KAAK,OAAO,OAAO,KAAK;AAAA,EAC1C;AAEA,MAAI,aAAa,KAAK,KAAK,GAAG;AAC7B,WAAO,OAAO,GAAG,IAAI,OAAO,SAAS,KAAK,CAAC,CAAC,OAAO,OAAO,IAAI,OAAO,SAAS,KAAK,CAAC,CAAC;AAAA,EACtF;AACA,SAAO,OAAO,GAAG,SAAS,KAAK,CAAC,OAAO,SAAS,KAAK;AACtD;AAOO,SAAS,MAAM,QAAQ,IAAI;AACjC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC/B,eAAW,MAAM;AAChB,cAAQ;AAAA,IACT,GAAG,KAAK;AAAA,EACT,CAAC;AACF;AAMO,SAAS,KAAK;AAEpB,SAAO,IAAI,cAAc,EAAE,SAAS,YAAY;AAKjD;AAKO,SAAS,MAAM;AACrB,SAAO,IAAI,kBAAkB;AAC9B;AACO,SAAS,gBAAgB;AAC/B,MAAI,MAAM,CAAC;AAEX,QAAM,IAAI,cAAc;AAKxB,SAAO;AACR;AACO,SAAS,gBAAgB;AAC/B,MAAI,MAAM,CAAC;AAEX,QAAM,IAAI,cAAc;AAKxB,SAAO;AACR;AAOO,SAAS,OAAO,KAAK,KAAK;AAChC,MAAI,OAAO,KAAK,MAAM,KAAK,OAAO,KAAK;AACtC,UAAM,MAAM,MAAM,MAAM;AACxB,WAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,GAAG;AAAA,EAC5C;AACA,SAAO;AACR;AAOO,SAAS,KAAK,MAAM,IAAI,SAAS,MAAM,QAAQ,MAAM;AAC3D,QAAM,QAAQ,iEAAiE,MAAM,EAAE;AACvF,QAAM,OAAO,CAAC;AACd,UAAQ,SAAS,MAAM;AAEvB,MAAI,KAAK;AAER,aAAS,IAAI,GAAG,IAAI,KAAK;AAAK,WAAK,CAAC,IAAI,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK;AAAA,EACxE,OAAO;AACN,QAAI;AAEJ,SAAK,CAAC,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI;AAC3C,SAAK,EAAE,IAAI;AAEX,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,UAAI,CAAC,KAAK,CAAC,GAAG;AACb,YAAI,IAAI,KAAK,OAAO,IAAI;AACxB,aAAK,CAAC,IAAI,MAAO,KAAK,KAAO,IAAI,IAAO,IAAM,CAAC;AAAA,MAChD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,QAAQ;AACX,SAAK,MAAM;AACX,WAAO,IAAI,KAAK,KAAK,EAAE,CAAC;AAAA,EACzB;AACA,SAAO,KAAK,KAAK,EAAE;AACpB;AASO,SAAS,QAAQC,QAAO,QAAW;AACzC,MAAI,SAAS,KAAK;AAElB,SAAO,QAAQ;AAER,IAAAA,QAAOA,MAAK,QAAQ,wBAAwB,MAAM;AACxD,QAAI,OAAO,YAAY,OAAO,SAAS,SAASA,OAAM;AAErD,eAAS,OAAO;AAAA,IACjB,OAAO;AACN,aAAO;AAAA,IACR;AAAA,EACD;AACA,SAAO;AACR;AASO,SAAS,SAAS,aAAa,SAAS,UAAU;AAExD,MAAI,MAAU,WAAW,KAAK,OAAO,gBAAiB,YAAY,WAAW,YAAY,WAAW,YACnG,OAAO,gBAAiB,UAAU;AAClC,WAAO;AAAA,EACR;AAEA,MAAI,WAAW,UAAU;AAExB,kBAAc,KAAK,WAAW;AAE9B,UAAM,aAAa,YAAY,MAAM,GAAG;AACxC,UAAM,QAAQ,CAAC;AAEf,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAE3C,UAAI,WAAW,CAAC,GAAG;AAClB,cAAM,OAAO,WAAW,CAAC,EAAE,MAAM,GAAG;AACpC,cAAM,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,MACpC;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAEA,MAAIC,UAAS;AACb,MAAI,OAAO,gBAAgB,UAAU;AACpC,gBAAY,QAAQ,CAAC,KAAK,MAAM;AAE/B,YAAM,MAAM,EAAE,QAAQ,YAAY,KAAK,EAAE,YAAY;AACrD,MAAAA,WAAU,GAAG,GAAG,IAAI,GAAG;AAAA,IACxB,CAAC;AAAA,EACF;AAEA,SAAO,KAAKA,OAAM;AACnB;AAOO,SAAS,QAAQ,QAAQ,QAAQ,OAAO,IAAI;AAClD,MAAI,CAAC,MAAM;AACV,WAAO,eAAO,QAAQ;AAAA,EACvB;AACA,MAAI,QAAQ,SAAS,OAAW,OAAO,KAAK,CAAC,GAAG;AAC/C,YAAQ,QAAQ;AAAA,EACjB;AACA,UAAQ,OAAO,KAAK;AAEpB,SAAO,OAAW,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI,KAAK;AAChD;AAOO,SAAS,UAAU,KAAK;AAE9B,MAAI,CAAC,MAAM,QAAW,KAAK,KAAK,EAAE,SAAS,GAAG;AAAG,WAAO;AACxD,MAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;AAEzD,WAAO;AAAA,EACR;AACA,QAAM,IAAI,MAAU,GAAG,IAAI,CAAC,IAAI,CAAC;AACjC,aAAW,KAAK,KAAK;AACpB,QAAI,IAAI,eAAe,CAAC,GAAG;AAC1B,QAAE,CAAC,IAAI,OAAO,IAAI,CAAC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC;AAAA,IAC9D;AAAA,EACD;AACA,SAAO;AACR;AAQO,SAAS,UAAU,eAAe,CAAC,GAAG,SAAS,CAAC,GAAG;AACzD,MAAI,SAAS,UAAU,YAAY;AACnC,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW;AAAU,WAAO;AACrE,aAAW,QAAQ,QAAQ;AAC1B,QAAI,CAAC,OAAO,eAAe,IAAI;AAAG;AAClC,QAAI,QAAQ,QAAQ;AACnB,UAAI,OAAO,IAAI,KAAK,MAAM;AACzB,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAC3B,WAAU,OAAO,OAAO,IAAI,MAAM,UAAU;AAC3C,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAC3B,WAAW,OAAO,OAAO,IAAI,MAAM,UAAU;AAC5C,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAC3B,WAAW,OAAO,IAAI,EAAE,UAAU,OAAO,IAAI,EAAE,QAAQ;AACtD,eAAO,IAAI,IAAI,OAAO,IAAI,EAAE,OAAO,OAAO,IAAI,CAAC;AAAA,MAChD,OAAO;AACN,eAAO,IAAI,IAAI,UAAU,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC;AAAA,MACpD;AAAA,IACD,OAAO;AACN,aAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC3B;AAAA,EACD;AACA,SAAO;AACR;AAOO,SAAS,aAAa,QAAQ,SAAS,CAAC,GAAG;AACjD,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW;AAAU,WAAO;AACrE,aAAW,QAAQ,QAAQ;AAC1B,QAAI,CAAC,OAAO,eAAe,IAAI;AAAG;AAClC,QAAI,QAAQ,QAAQ;AACnB,UAAI,OAAO,IAAI,KAAK,MAAM;AACzB,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAC3B,WAAU,OAAO,OAAO,IAAI,MAAM,UAAU;AAC3C,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAC3B,WAAW,OAAO,OAAO,IAAI,MAAM,UAAU;AAC5C,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAC3B,WAAW,OAAO,IAAI,EAAE,UAAU,OAAO,IAAI,EAAE,QAAQ;AACtD,eAAO,IAAI,IAAI,OAAO,IAAI,EAAE,OAAO,OAAO,IAAI,CAAC;AAAA,MAChD,OAAO;AACN,eAAO,IAAI,IAAI,aAAa,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC;AAAA,MACvD;AAAA,IACD,OAAO;AACN,aAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC3B;AAAA,EACD;AACA,SAAO;AACR;AAMO,SAAS,MAAM,KAAK;AAE1B,MAAI,MAAwC;AAC3C,YAAQ,MAAM,WAAW,GAAG,EAAE;AAAA,EAC/B;AACD;AAOO,SAAS,YAAYC,SAAQ,CAAC,GAAG;AAEvC,SAAOA,OAAM,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG;AAC5C;AAIA,IAAI,CAAC,OAAO,UAAU,UAAU;AAE/B,SAAO,UAAU,WAAW,SAAS,WAAW,aAAa,KAAK;AACjE,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,MAAM,mBAAmB;AACrE,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AACA,UAAM,MAAM;AAEZ,QAAI,IAAI,UAAU;AAAW,aAAO,OAAO,GAAG;AAE9C,UAAM,aAAa,YAAY,IAAI;AACnC,QAAIC,SAAQ,KAAK,KAAK,aAAa,WAAW,MAAM;AACpD,WAAOA,WAAU,GAAG;AACnB,oBAAc;AACd,UAAIA,WAAU,GAAG;AAChB,sBAAc;AAAA,MACf;AAAA,IACD;AACA,WAAO,WAAW,MAAM,GAAG,UAAU,IAAI;AAAA,EAC1C;AACD;AAQO,SAAS,WAAW,WAAW,MAAM,YAAY,cAAc;AACpE,MAAIC;AAEJ,MAAI,CAAC,UAAU;AACb,IAAAA,QAAO,oBAAI,KAAK;AAAA,EAClB,WAES,WAAW,KAAK,SAAS,SAAS,EAAE,KAAK,CAAC,GAAG;AACpD,IAAAA,QAAO,IAAI,KAAK,WAAW,GAAI;AAAA,EACjC,WAES,OAAO,aAAa,YAAY,QAAQ,KAAK,SAAS,KAAK,CAAC,GAAG;AACtE,IAAAA,QAAO,IAAI,KAAK,OAAO,QAAQ,CAAC;AAAA,EAClC,OAEK;AAEH,IAAAA,QAAO,IAAI;AAAA,MACT,OAAO,aAAa,WAChB,SAAS,QAAQ,MAAM,GAAG,IAC1B;AAAA,IACN;AAAA,EACF;AAED,QAAM,aAAa;AAAA,IAClB,KAAKA,MAAK,YAAY,EAAE,SAAS;AAAA;AAAA,IACjC,MAAMA,MAAK,SAAS,IAAI,GAAG,SAAS,EAAE,SAAS,GAAG,GAAG;AAAA;AAAA,IACrD,KAAKA,MAAK,QAAQ,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG;AAAA;AAAA,IAC9C,KAAKA,MAAK,SAAS,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG;AAAA;AAAA,IAC/C,KAAKA,MAAK,WAAW,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG;AAAA;AAAA,IACjD,KAAKA,MAAK,WAAW,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG;AAAA;AAAA;AAAA,EAElD;AAEC,aAAW,OAAO,YAAY;AAC5B,UAAM,CAAC,GAAG,IAAI,IAAI,OAAO,GAAG,GAAG,GAAG,EAAE,KAAK,SAAS,KAAK,CAAC;AACxD,QAAI,KAAK;AAEP,YAAM,aAAa,QAAQ,OAAO,IAAI,WAAW,IAAI,IAAI;AACzD,kBAAY,UAAU,QAAQ,KAAK,WAAW,GAAG,EAAE,MAAM,UAAU,CAAC;AAAA,IACtE;AAAA,EACF;AAEA,SAAO;AACT;AAUO,SAAS,SAAS,YAAY,MAAM,SAAS,cAAc;AACjE,MAAI,aAAa;AAAM,gBAAY,OAAO,oBAAI,KAAK,CAAC;AACpD,cAAY,SAAS,SAAS;AAE9B,MAAI,UAAU,SAAS,EAAE,UAAU;AAAI,iBAAa;AACpD,MAAIC,UAAS,oBAAI,KAAK,GAAG,QAAQ,IAAI;AACrC,EAAAA,SAAQ,SAASA,SAAQ,GAAI;AAE7B,MAAI,OAAO;AACX,UAAQ,MAAM;AAAA,IACb,KAAKA,SAAQ;AACZ,aAAO;AACP;AAAA,IACD,MAAKA,UAAS,OAAOA,SAAQ;AAC5B,aAAO,GAAG,SAASA,SAAQ,EAAE,CAAC;AAC9B;AAAA,IACD,MAAKA,UAAS,QAAQA,SAAQ;AAC7B,aAAO,GAAG,SAASA,SAAQ,IAAI,CAAC;AAChC;AAAA,IACD,MAAKA,UAAS,SAASA,SAAQ;AAC9B,aAAO,GAAG,SAASA,SAAQ,KAAK,CAAC;AACjC;AAAA,IACD;AAEC,UAAI,WAAW,OAAO;AACrB,YAAIA,UAAS,UAAWA,SAAQ,MAAM,OAAO;AAC5C,iBAAO,GAAG,SAASA,UAAS,QAAQ,GAAG,CAAC;AAAA,QACzC,OAAO;AACN,iBAAO,GAAG,SAASA,UAAS,QAAQ,IAAI,CAAC;AAAA,QAC1C;AAAA,MACD,OAAO;AACN,eAAO,WAAW,WAAW,MAAM;AAAA,MACpC;AAAA,EACF;AACA,SAAO;AACR;AAOO,SAAS,KAAK,KAAK,MAAM,QAAQ;AACvC,QAAM,OAAO,GAAG;AAChB,MAAI,OAAO,QAAQ;AAClB,WAAO,IAAI,QAAQ,cAAc,EAAE;AAAA,EACpC;AACA,MAAI,OAAO,QAAQ;AAClB,WAAO,IAAI,QAAQ,QAAQ,EAAE;AAAA,EAC9B;AACA,MAAI,OAAO,SAAS;AACnB,WAAO,IAAI,QAAQ,WAAW,EAAE;AAAA,EACjC;AACA,MAAI,OAAO,OAAO;AACjB,WAAO,IAAI,QAAQ,QAAQ,EAAE;AAAA,EAC9B;AACA,SAAO;AACR;AAQO,SAAS,YAAY,OAAO,CAAC,GAAG,WAAW,MAAM,cAAc,YAAY;AACjF,QAAM,SAAS,WAAW,MAAM;AAChC,QAAM,UAAU,CAAC;AACjB,MAAI,CAAC,WAAW,YAAY,UAAU,OAAO,EAAE,QAAQ,WAAW,KAAK;AAAI,kBAAc;AACzF,aAAW,OAAO,MAAM;AACvB,UAAM,QAAQ,KAAK,GAAG;AAEtB,QAAI,CAAC,IAAI,QAAW,IAAI,EAAE,QAAQ,KAAK,KAAK,GAAG;AAC9C;AAAA,IACD;AAEA,QAAI,MAAM,gBAAgB,OAAO;AAEhC,cAAQ,aAAa;AAAA,QACpB,KAAK;AAEJ,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,oBAAQ,KAAK,GAAG,GAAG,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE;AAAA,UACxC;AACA;AAAA,QACD,KAAK;AAEJ,gBAAM,QAAQ,CAAC,WAAW;AACzB,oBAAQ,KAAK,GAAG,GAAG,MAAM,MAAM,EAAE;AAAA,UAClC,CAAC;AACD;AAAA,QACD,KAAK;AAEJ,gBAAM,QAAQ,CAAC,WAAW;AACzB,oBAAQ,KAAK,GAAG,GAAG,IAAI,MAAM,EAAE;AAAA,UAChC,CAAC;AACD;AAAA,QACD,KAAK;AAEJ,cAAI,WAAW;AACf,gBAAM,QAAQ,CAAC,WAAW;AACzB,yBAAa,WAAW,MAAM,MAAM;AAAA,UACrC,CAAC;AACD,kBAAQ,KAAK,GAAG,GAAG,IAAI,QAAQ,EAAE;AACjC;AAAA,QACD;AACC,gBAAM,QAAQ,CAAC,WAAW;AACzB,oBAAQ,KAAK,GAAG,GAAG,MAAM,MAAM,EAAE;AAAA,UAClC,CAAC;AAAA,MACH;AAAA,IACD,OAAO;AACN,cAAQ,KAAK,GAAG,GAAG,IAAI,KAAK,EAAE;AAAA,IAC/B;AAAA,EACD;AACA,SAAO,QAAQ,SAAS,SAAS,QAAQ,KAAK,GAAG,IAAI;AACtD;AAOO,SAAS,MAAM,OAAO,WAAW,KAAM;AAC7C,MAAI,UAAU;AAAA,IACb,OAAO,OAAO,KAAK;AAAA,IACnB,MAAM;AAAA,IACN;AAAA,EACD,CAAC;AACF;AAOO,SAAS,UAAU,OAAO,WAAW,OAAO,OAAO;AAEzD,MAAI,CAAC,WAAW,QAAQ,SAAS,WAAW,SAAS,EAAE,QAAQ,IAAI,KAAK;AAAI,WAAO;AACnF,MAAI,WAAW;AAEf,UAAQ,MAAM;AAAA,IACb,KAAK;AACJ,iBAAW;AACX;AAAA,IACD,KAAK;AACJ,iBAAW;AACX;AAAA,IACD,KAAK;AACJ,iBAAW;AACX;AAAA,IACD,KAAK;AACJ,iBAAW;AACX;AAAA,IACD,KAAK;AACJ,iBAAW;AACX;AAAA,IACD;AACC,iBAAW;AAAA,EACb;AAEA,MAAI;AAAM,gBAAY;AACtB,SAAO;AACR;AAUO,SAAS,YAAYC,SAAQ,WAAW,GAAG,eAAe,KAAK,qBAAqB,KAAK;AAC/F,EAAAA,UAAU,GAAGA,OAAM,GAAI,QAAQ,gBAAgB,EAAE;AACjD,QAAM,IAAI,CAAC,SAAS,CAACA,OAAM,IAAI,IAAI,CAACA;AACpC,QAAM,OAAO,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,QAAQ;AACzD,QAAM,MAAO,OAAO,uBAAuB,cAAe,MAAM;AAChE,QAAM,MAAO,OAAO,iBAAiB,cAAe,MAAM;AAC1D,MAAI,IAAI;AAER,OAAK,OAAO,MAAM,GAAG,IAAI,IAAI,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,IAAI,MAAM,GAAG;AAC/D,QAAM,KAAK;AACX,SAAO,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG;AACrB,MAAE,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,IAAI,KAAK,GAAG,IAAI;AAAA,EACrC;AAEA,OAAK,EAAE,CAAC,KAAK,IAAI,SAAS,MAAM;AAC/B,MAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACf,MAAE,CAAC,KAAK,IAAI,MAAM,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AAAA,EACnD;AACA,SAAO,EAAE,KAAK,GAAG;AAClB;AAUO,SAAS,YAAY,OAAO,OAAO,MAAM;AAC/C,QAAM,WAAW,SAAS,KAAK;AAC/B,MAAI,MAAM;AACT,QAAI,KAAK,KAAK,KAAK;AAAG,aAAO;AAC7B,WAAO,QAAQ,KAAK,GAAG,KAAK,OAAO,GAAG,KAAK;AAAA,EAC5C;AACA,MAAI,MAAM,KAAK,KAAK;AAAG,WAAO;AAC9B,MAAI,KAAK,KAAK,KAAK;AAAG,WAAO,WAAW,KAAK,WAAW,WAAW;AACnE,SAAO;AACR;AAMO,SAAS,QAAQ,OAAO;AAC9B,SAAO,KAAK,KAAK,GAAG,MAAM,EAAE;AAC7B;AAOO,SAAS,aAAa,UAAU,OAAO;AAC7C,QAAM,WAAW,QAAQ,KAAK,UAAU,aAAa;AACrD,QAAM,OAAO,QAAQ,KAAK,UAAU,QAAQ;AAG5C,MAAI,YAAY,MAAM;AACrB,SAAK,cAAc,SAAS,MAAM,MAAM;AAAA,IAAC,GAAG,KAAK;AAAA,EAClD;AACD;AAQO,SAAS,YAAY,KAAK,KAAK;AACrC,MAAI,OAAO,QAAQ,YAAY,QAAQ,KAAK;AACrC,WAAO;AAAA,EACX;AACH,MAAI,OAAO,QAAQ,YAAY,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACR;AACA,MAAI,IAAI,QAAQ,GAAG,MAAM,IAAI;AAC5B,UAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,QAAI,WAAW,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC;AAEhC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,UAAI,UAAU;AACb,mBAAW,SAAS,KAAK,CAAC,CAAC;AAAA,MAC5B;AAAA,IACD;AACA,WAAO;AAAA,EACR;AACA,SAAO,IAAI,GAAG;AACf;AAQO,SAAS,YAAY,KAAK,KAAK,OAAO;AAC5C,MAAI,OAAO,QAAQ,YAAY,QAAQ,KAAK;AAC3C;AAAA,EACD;AAEA,QAAM,OAAO,SAAS,MAAM,MAAM,GAAG;AAEpC,QAAI,KAAK,WAAW,GAAG;AACtB,WAAK,KAAK,CAAC,CAAC,IAAI;AAChB;AAAA,IACD;AAEA,WAAO,KAAK,SAAS,GAAG;AACvB,YAAM,IAAI,KAAK,CAAC;AAChB,UAAI,CAAC,KAAK,CAAC,KAAM,OAAO,KAAK,CAAC,MAAM,UAAW;AAC9C,aAAK,CAAC,IAAI,CAAC;AAAA,MACZ;AACA,YAAMC,OAAM,KAAK,MAAM;AAEvB,WAAK,KAAK,CAAC,GAAG,MAAM,CAAC;AAAA,IACtB;AAAA,EACD;AAEA,MAAI,OAAO,QAAQ,YAAY,QAAQ,IAAI;AAAA,EAE3C,WAAW,IAAI,QAAQ,GAAG,MAAM,IAAI;AACnC,UAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,SAAK,KAAK,MAAM,KAAK;AAAA,EACtB,OAAO;AACN,QAAI,GAAG,IAAI;AAAA,EACZ;AACD;AAKO,SAAS,OAAO;AACtB,QAAMC,SAAQ,gBAAgB;AAE9B,SAAO,IAAIA,OAAMA,OAAM,SAAS,CAAC,EAAE,SAAS,EAAE;AAC/C;AAKO,SAAS,QAAQ;AACvB,QAAMA,SAAQ,gBAAgB;AAC9B,SAAOA;AACR;AAEO,SAAS,eAAe,KAAK,MAAM;AAEtC,QAAM,UAAU,KAAK,MAAM,GAAG;AAE9B,SAAO,QAAQ,OAAO,CAAC,KAAK,SAAS;AAGjC,WAAO,OAAO,IAAI,IAAI,MAAM,SAAY,IAAI,IAAI,IAAI;AAAA,EACxD,GAAG,GAAG;AACV;AAQO,SAAS,cAAc,WAAW,YAAY,IAAI;AAExD,QAAM,MAAM,qBAAqB,SAAS;AAG1C,QAAM,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAGxC,QAAM,QAAQ;AAAA,IACZ,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,GAAG,KAAK,IAAI,WAAW,EAAE;AAAA,EAC3B;AAEA,SAAO,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC;AAGA,SAAS,qBAAqB,UAAU;AAEzC,QAAM,MAAM,SAAS,YAAY,EAAE,KAAK;AAGxC,MAAI,IAAI,WAAW,GAAG,GAAG;AACvB,UAAM,MAAM,IAAI,QAAQ,KAAK,EAAE;AAC/B,UAAM,UAAU,IAAI,WAAW,IAChC,IAAI,MAAM,EAAE,EAAE,IAAI,OAAK,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI;AAExC,WAAO;AAAA,MACR,GAAG,SAAS,QAAQ,UAAU,GAAE,CAAC,GAAG,EAAE;AAAA,MACtC,GAAG,SAAS,QAAQ,UAAU,GAAE,CAAC,GAAG,EAAE;AAAA,MACtC,GAAG,SAAS,QAAQ,UAAU,GAAE,CAAC,GAAG,EAAE;AAAA,IACrC;AAAA,EACF;AAGA,QAAM,WAAW,IAAI,MAAM,gCAAgC;AAC3D,MAAI,UAAU;AACZ,WAAO;AAAA,MACR,GAAG,CAAC,SAAS,CAAC;AAAA,MACd,GAAG,CAAC,SAAS,CAAC;AAAA,MACd,GAAG,CAAC,SAAS,CAAC;AAAA,IACb;AAAA,EACF;AAEA,QAAM,IAAI,MAAM,sBAAsB;AACrC;AAGF,SAAS,SAAS,GAAG,GAAG,GAAG;AAC1B,OAAK,KAAK,KAAK,KAAK,KAAK;AACzB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AACrD,MAAI,GAAG,GAAG,KAAK,MAAM,OAAO;AAE5B,MAAI,QAAQ,KAAK;AACf,QAAI,IAAI;AAAA,EACV,OAAO;AACL,UAAM,IAAI,MAAM;AAChB,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,YAAQ,KAAK;AAAA,MACd,KAAK;AAAG,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAAI;AAAA,MAC3C,KAAK;AAAG,aAAK,IAAI,KAAK,IAAI;AAAG;AAAA,MAC7B,KAAK;AAAG,aAAK,IAAI,KAAK,IAAI;AAAG;AAAA,IAC5B;AACA,SAAK,IAAI,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,SAAO,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,KAAK,QAAQ,CAAC,GAAG,GAAG,EAAE,IAAI,KAAK,QAAQ,CAAC,EAAE;AACpE;AAGA,SAAS,SAAS,GAAG,GAAG,GAAG;AAC1B,OAAK;AACL,QAAM,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI;AACnC,QAAM,IAAI,OAAK;AACb,UAAM,KAAK,IAAI,IAAI,MAAM;AACzB,UAAMC,SAAQ,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE;AAC5D,WAAO,KAAK,MAAM,MAAMA,MAAK,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAAA,EAC7D;AACA,SAAO,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B;AAEA,IAAO,mBAAQ;AAAA,EACd,OAAAV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;;AC/0BA,IAAM,SAAN,MAAa;AAAA,EACT,cAAc;AAEV,SAAK,SAAS;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA;AAAA,MACP,QAAQ,CAAC;AAAA;AAAA,MACT,eAAe;AAAA;AAAA,MACf,mBAAmB;AAAA;AAAA,MACnB,WAAW;AAAA;AAAA,IACf;AAGA,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EACrC;AAAA;AAAA,EAGA,YAAYW,MAAK;AACb,WAAOA,KAAI,CAAC,MAAM,MAAMA,OAAM,IAAIA,IAAG;AAAA,EACzC;AAAA;AAAA,EAGA,WAAWA,MAAK,QAAQ;AACpB,IAAAA,OAAMA,QAAO,KAAK,YAAYA,IAAG;AAIjC,QAAI,QAAQ;AACZ,QAAI,gBAAgB,KAAKA,IAAG,GAAG;AAE3B,cAAQ,YAAY,QAAQ,KAAK;AAEjC,aAAOA,QAAO,IAAI,KAAK;AAAA,IAC3B;AAEA,YAAQ,YAAY,MAAM;AAC1B,WAAOA,QAAO;AAAA,EAClB;AAAA;AAAA,EAGA,MAAM,MAAM,UAAU,CAAC,GAAG,SAAS,CAAC,GAAG;AAEnC,QAAI,cAAc,CAAC;AAEnB,QAAI,OAAO,YAAY,UAAU;AAE7B,kBAAY,MAAM,KAAK,WAAW,SAAS,MAAM;AACjD,kBAAY,OAAO;AAAA,IACvB,OAAO;AACH,oBAAc,UAAU,KAAK,QAAQ,OAAO;AAE5C,kBAAY,MAAM,KAAK,WAAW,QAAQ,KAAK,QAAQ,MAAM;AAAA,IACjE;AAGA,QAAI,YAAY,QAAQ,KAAK;AAAG;AAEhC,QAAI,OAAO,WAAW;AAClB,WAAK,OAAO,YAAY,OAAO;AAAA,IACnC;AAEA,gBAAY,SAAS;AAErB,kBAAc,UAAU,KAAK,QAAQ,WAAW;AAEhD,QAAI,OAAO,IAAI,GAAG,mBAAmB,YAAY;AAE7C,YAAM,SAAS,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAClD,YAAI,GAAG,eAAe,aAAa,OAAO;AAAA,MAC9C,CAAC;AAED,gBAAU,KAAK,SAAS,WAAW;AAAA,IACvC,OAAO;AACH,WAAK,SAAS,WAAW;AAAA,IAC7B;AAAA,EACJ;AAAA;AAAA,EAGA,SAAS,QAAQ;AAEb,UAAM;AAAA,MACF,KAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,IAAI;AACJ,QAAI,OAAO,QAAQ,gBAAgB,OAAO,QAAQ,MAAM;AACpD,UAAI,WAAW;AAAA,QACX,KAAAA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,OAAO,QAAQ,gBAAgB,OAAO,QAAQ,YAAY;AAC1D,UAAI,WAAW;AAAA,QACX,KAAAA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,OAAO,QAAQ,eAAe,OAAO,QAAQ,OAAO;AACpD,UAAI,UAAU;AAAA,QACV,KAAAA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,OAAO,QAAQ,cAAc,OAAO,QAAQ,UAAU;AACtD,UAAI,SAAS;AAAA,QACT,KAAAA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,OAAO,QAAQ,kBAAkB,OAAO,QAAQ,QAAQ;AACxD,UAAI,aAAa;AAAA,QACb;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAEA,IAAO,gBAAS,IAAI,OAAO,EAAG;;;AClHvB,IAAM,QAAQ,YAAY;AAAA;AAAA,EAE7B,OAAO;AAAA;AAAA,IAEH,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,OAAO,CAAC;AAAA,IACrB;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA;AAAA,IAEA,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA;AAAA,IAEA,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,OAAO;AACH,WAAO,CAAC;AAAA,EACZ;AAAA,EACA,SAAS;AAEL,SAAK,GAAG,UAAU,KAAK;AAAA,EAC3B;AAAA,EACA,UAAU;AAEN,SAAK,GAAG,UAAU,KAAK;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA;AAAA;AAAA;AAAA,IAIN,KAAK;AAGD,aAAO,UAAU,IAAI,IAAI;AAAA,QACrB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IAKL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,MAAM;AACF,aAAO,SAAUC,OAAM,OAAO,QAAQ;AAElC,cAAM,SAAS,KAAKA,KAAI;AACxB,cAAM,UAAU,CAAC;AACjB,YAAI,OAAO;AACP,gBAAM,IAAI,CAAC,SAAS;AAEhB,oBAAQ,SAAS,KAAK,IAAI,CAAC,IAAI;AAAA,UACnC,CAAC;AAAA,QACL;AACA,YAAI,QAAQ;AACR,iBAAO,IAAI,CAAC,SAAS;AAEjB,iBAAK,IAAI,IAAK,QAAQ,SAAS,IAAI,IAAI,KAAK,IAAI,IAAM,OAAO,QAAQ,SAAS,IAAI;AAAA,UACtF,CAAC;AAAA,QACL;AACA,eAAO,OAAO,KAAK,OAAO;AAAA,MAK9B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AAAA;AAAA,IAEL,SAAS,SAAS,OAAO;AACrB,YAAMC,OAAM,KAAK,MAAM;AACvB,UAAIA,MAAK;AAGL,sBAAM,EAAE,MAAM,KAAK,UAAU,KAAAA,KAAI,CAAC;AAAA,MAKtC;AAAA,IACJ;AAAA,IACA,MAAMA,OAAM,IAAI,WAAW,cAAc;AACrC,oBAAM,EAAE,MAAM,KAAK,UAAU,KAAAA,KAAI,CAAC;AAAA,IACtC;AAAA;AAAA;AAAA;AAAA,IAIA,UAAU,UAAU,KAAK;AACrB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAE5B,YAAI,oBAAoB,EACnB,GAAG,IAAI,EAAE,MAAM,cAAc,QAAQ,EAAE,QAAQ,EAC/C,mBAAmB,CAAC,SAAS;AAC1B,cAAI,OAAO,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAC3C,oBAAQ,IAAI;AAAA,UAChB;AACA,cAAI,CAAC,OAAO,MAAM;AACd,oBAAQ,IAAI;AAAA,UAChB;AAAA,QACJ,CAAC,EACA,KAAK;AAAA,MAwBd,CAAC;AAAA,IACL;AAAA,IACA,cAAc,aAAa,IAAI;AAE3B,UAAI,CAAC,KAAK;AAAQ,aAAK,SAAS,CAAC;AAKjC,WAAK,SAAS,QAAQ,KAAK,MAAM,UAAU;AAC3C,UAAI,KAAK,OAAO,UAAU;AAEtB,aAAK,OAAO,SAAS,QAAQ,IAAI,MAAM,MAAM,KAAK,OAAO,SAAS,KAAK,IAAI;AAAA,MAC/E;AACA,UAAI,KAAK,UAAU,KAAK,YAAY;AAEhC,eAAO,KAAK,KAAK,UAAU,EAAE,IAAI,CAAC,QAAQ;AACtC,eAAK,WAAW,GAAG,IAAI,KAAK,OAAO,GAAG;AAAA,QAC1C,CAAC;AAAA,MACL;AAAA,IACJ;AAAA;AAAA,IAEA,aAAa,GAAG;AACZ,WAAK,OAAQ,EAAE,oBAAqB,cAAc,EAAE,gBAAgB;AAAA,IACxE;AAAA;AAAA,IAEA,KAAK,GAAG;AACJ,WAAK,aAAa,CAAC;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,QAAI,MAAM,gBAAgB;AAAA,EACjC;AAAA,EACA,gBAAgB;AAGT,QAAI,KAAK,UAAU,aAAK,MAAM,KAAK,OAAO,QAAQ,GAAG;AAEjD,YAAM,eAAe,KAAK,OAAO;AACjC,mBAAa,IAAI,CAAC,OAAO,UAAU;AAE/B,YAAI,UAAU,MAAM;AAChB,uBAAa,OAAO,OAAO,CAAC;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,CAAC;;;ACpMM,IAAM,UAAU,YAAY,CAOnC,CAAC;;;ACHM,SAAS,cAAc,aAAa,gBAAgB,WAAW,sBAAsB,OAAO,IAAI;AACnG,QAAM,WAAW,SAAS,YAAY,KAAK;AAC3C,QAAM,SAAS,SAAS,CAAC;AACzB,QAAM,SAAS,SAAS,CAAC;AACzB,QAAM,SAAS,SAAS,CAAC;AAEzB,QAAM,SAAS,SAAS,UAAU,KAAK;AACvC,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AAErB,QAAM,MAAM,OAAO,UAAU;AAC7B,QAAM,MAAM,OAAO,UAAU;AAC7B,QAAM,MAAM,OAAO,UAAU;AAC7B,QAAM,WAAW,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAE3B,QAAI,MAAM,SAAS,OAAO,KAAK,MAAO,KAAK,IAAI,MAAO,CAAC,IAAI,KAAK,MAAO,KAAK,IAAI,MAAO,CAAC,IAAI,KAAK,MAAO,KAC3G,IAAI,MAAO,CAAC,GAAG;AAEZ,QAAI,MAAM;AAAG,YAAM,SAAS,UAAU;AAEtC,QAAI,MAAM,OAAO;AAAG,YAAM,SAAS,QAAQ;AAC3C,aAAS,KAAK,GAAG;AAAA,EACrB;AACA,SAAO;AACX;AAGO,SAAS,SAAS,QAAQ,MAAM,MAAM;AACzC,QAAM,MAAM;AACZ,WAAS,OAAO,MAAM,EAAE,YAAY;AACpC,MAAI,UAAU,IAAI,KAAK,MAAM,GAAG;AAC5B,QAAI,OAAO,WAAW,GAAG;AACrB,UAAI,YAAY;AAChB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,qBAAa,OAAO,MAAM,GAAG,IAAI,CAAC,EAAE,OAAO,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,MACrE;AACA,eAAS;AAAA,IACb;AAEA,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,mBAAa,KAAK,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;AAAA,IAC7D;AACA,QAAI,CAAC,KAAK;AACN,aAAO;AAAA,IACX;AACA,WAAO,OAAO,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC;AAAA,EACvE;AAAE,MAAI,aAAa,KAAK,MAAM,GAAG;AAC7B,UAAM,MAAM,OAAO,QAAQ,uBAAuB,EAAE,EAAE,MAAM,GAAG;AAC/D,WAAO,IAAI,IAAI,CAAC,QAAQ,OAAO,GAAG,CAAC;AAAA,EACvC;AACA,SAAO;AACX;AAGO,SAAS,SAAS,KAAK;AAC1B,QAAM,QAAQ;AACd,QAAM,MAAM;AACZ,MAAI,aAAa,KAAK,KAAK,GAAG;AAC1B,UAAM,SAAS,MAAM,QAAQ,uBAAuB,EAAE,EAAE,MAAM,GAAG;AACjE,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,MAAM,OAAO,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE;AACvC,YAAM,OAAO,GAAG,EAAE,UAAU,IAAI,GAAG,CAAC,GAAG,GAAG,KAAK;AAC/C,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX;AACA,gBAAU;AAAA,IACd;AACA,QAAI,OAAO,WAAW,GAAG;AACrB,eAAS;AAAA,IACb;AACA,WAAO;AAAA,EACX;AAAE,MAAI,IAAI,KAAK,KAAK,GAAG;AACnB,UAAM,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE;AAC5C,QAAI,KAAK,WAAW,GAAG;AACnB,aAAO;AAAA,IACX;AAAE,QAAI,KAAK,WAAW,GAAG;AACrB,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACrC,kBAAW,KAAK,CAAC,IAAI,KAAK,CAAC;AAAA,MAC/B;AACA,aAAO;AAAA,IACX;AAAA,EACJ,OAAO;AACH,WAAO;AAAA,EACX;AACJ;AAOO,SAAS,YAAYC,QAAO,OAAO;AACtC,EAAAA,SAAQ,SAASA,MAAK;AAEtB,QAAM,MAAM;AAEZ,MAAI,SAAS,OAAOA,MAAK,EAAE,YAAY;AACvC,MAAI,UAAU,IAAI,KAAK,MAAM,GAAG;AAC5B,QAAI,OAAO,WAAW,GAAG;AACrB,UAAI,YAAY;AAChB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,qBAAa,OAAO,MAAM,GAAG,IAAI,CAAC,EAAE,OAAO,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,MACrE;AACA,eAAS;AAAA,IACb;AAEA,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,mBAAa,KAAK,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;AAAA,IAC7D;AAEA,WAAO,QAAQ,aAAa,KAAK,GAAG,CAAC,IAAI,KAAK;AAAA,EAClD;AAEA,SAAO;AACX;AAEA,IAAO,wBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;ACrIA,IAAI,UAAU;AAUP,SAAS,SAASC,OAAM,OAAO,KAAK,YAAY,OAAO;AAE1D,MAAI,YAAY;AAAM,iBAAa,OAAO;AAE1C,MAAI,WAAW;AACX,UAAM,UAAU,CAAC;AACjB,cAAU,WAAW,MAAM;AACvB,gBAAU;AAAA,IACd,GAAG,IAAI;AACP,QAAI;AAAS,aAAOA,UAAS,cAAcA,MAAK;AAAA,EACpD,OAAO;AAEH,cAAU,WAAW,MAAM;AACvB,aAAOA,UAAS,cAAcA,MAAK;AAAA,IACvC,GAAG,IAAI;AAAA,EACX;AACJ;AAEA,IAAO,mBAAQ;;;AC5Bf,IAAI;AACJ,IAAI;AASG,SAAS,SAASC,OAAM,OAAO,KAAK,YAAY,MAAM;AACzD,MAAI,WAAW;AACX,QAAI,CAAC,MAAM;AACP,aAAO;AAEP,aAAOA,UAAS,cAAcA,MAAK;AACnC,cAAQ,WAAW,MAAM;AACrB,eAAO;AAAA,MACX,GAAG,IAAI;AAAA,IACX;AAAA,EACJ,WAAW,CAAC,MAAM;AACd,WAAO;AAEP,YAAQ,WAAW,MAAM;AACrB,aAAO;AACP,aAAOA,UAAS,cAAcA,MAAK;AAAA,IACvC,GAAG,IAAI;AAAA,EACX;AACJ;AACA,IAAO,mBAAQ;;;AC5BR,SAAS,IAAK,MAAM,MAAM;AAChC,MAAI,IAAI,IAAI;AACX,MAAI;AACH,SAAK,KAAK,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACpC,SAAS,GAAG;AACX,SAAK;AAAA,EACN;AACA,MAAI;AACH,SAAK,KAAK,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACpC,SAAS,GAAG;AACX,SAAK;AAAA,EACN;AACA,MAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AAClC,UAAQ,OAAO,IAAI,OAAO,KAAK;AAChC;AAEO,SAAS,IAAK,MAAM,MAAM;AAChC,MAAI,IAAI,IAAI,GAAG;AACd,MAAI;AACF,SAAK,KAAK,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACrC,SAAS,GAAG;AACV,SAAK;AAAA,EACP;AACA,MAAI;AACF,SAAK,KAAK,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACrC,SAAS,GAAG;AACV,SAAK;AAAA,EACP;AACA,MAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AACjC,MAAK,MAAM,KAAM,KAAK;AACvB,SAAO,KAAK,MAAM,OAAO,IAAI,OAAO,KAAK,GAAG,QAAQ,CAAC,CAAC;AACvD;AAEO,SAAS,IAAK,GAAG,GAAG;AAC1B,MAAI,IAAI,GACP,IAAI,EAAE,SAAS,GACf,IAAI,EAAE,SAAS;AAChB,MAAI;AACH,SAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACtB,SAAS,GAAG;AAAA,EAAC;AACb,MAAI;AACH,SAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACtB,SAAS,GAAG;AAAA,EAAC;AACb,SAAO,OAAO,EAAE,QAAQ,KAAK,EAAE,CAAC,IAAI,OAAO,EAAE,QAAQ,KAAK,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC;AAChF;AAEO,SAAS,IAAK,GAAG,GAAG;AAC1B,MAAI,GAAG,GAAG,IAAI,GACb,IAAI;AACL,MAAI;AACH,QAAI,EAAE,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EAChC,SAAS,GAAG;AAAA,EAAC;AACb,MAAI;AACH,QAAI,EAAE,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EAChC,SAAS,GAAG;AAAA,EAAC;AACb,SAAO,IAAI,OAAO,EAAE,SAAS,EAAE,QAAQ,KAAK,EAAE,CAAC,GAAG,IAAI,OAAO,EAAE,SAAS,EAAE,QAAQ,KAAK,EAAE,CAAC,GAAG,OAAO,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC;AACnI;AACA,IAAO,eAAQ;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;;ACtDA,IAAO,iBAAQ;AAAA,EACX,OAAO;AAAA,EACP,WAAW;AAAA;AAAA,EAEX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,iBAAiB;AACrB;;;ACjBA,IAAM,QAAQ;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AACjB;AAEA,IAAO,gBAAQ;;;ACZf,IAAM,EAAE,SAAS,IAAI,OAAO;AAQrB,SAAS,QAAQ,KAAK;AACzB,SAAO,SAAS,KAAK,GAAG,MAAM;AAClC;AAQO,SAAS,SAAS,KAAK;AAC1B,SAAO,QAAQ,QAAQ,OAAO,QAAQ;AAC1C;AAQO,SAAS,OAAO,KAAK;AACxB,SAAO,SAAS,KAAK,GAAG,MAAM;AAClC;AAQO,SAAS,kBAAkB,KAAK;AACnC,SAAO,OAAO,oBAAoB,eAAe,eAAe;AACpE;AAcO,SAAS,QAAQ,KAAK,IAAI;AAE7B,MAAI,QAAQ,QAAQ,OAAO,QAAQ,aAAa;AAC5C;AAAA,EACJ;AAGA,MAAI,OAAO,QAAQ,UAAU;AAEzB,UAAM,CAAC,GAAG;AAAA,EACd;AAEA,MAAI,QAAQ,GAAG,GAAG;AAEd,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACxC,SAAG,KAAK,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,IAChC;AAAA,EACJ,OAAO;AAEH,eAAW,OAAO,KAAK;AACnB,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAChD,WAAG,KAAK,MAAM,IAAI,GAAG,GAAG,KAAK,GAAG;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AACJ;AAgBO,SAAS,cAAc,KAAK;AAC/B,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACnD;AAUO,SAASC,aAAuC;AACnD,QAAM,SAAS,CAAC;AAChB,WAAS,YAAY,KAAK,KAAK;AAC3B,QAAI,OAAO,OAAO,GAAG,MAAM,YAAY,OAAO,QAAQ,UAAU;AAC5D,aAAO,GAAG,IAAIA,WAAU,OAAO,GAAG,GAAG,GAAG;AAAA,IAC5C,WAAW,OAAO,QAAQ,UAAU;AAChC,aAAO,GAAG,IAAIA,WAAU,CAAC,GAAG,GAAG;AAAA,IACnC,OAAO;AACH,aAAO,GAAG,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAC9C,YAAQ,UAAU,CAAC,GAAG,WAAW;AAAA,EACrC;AACA,SAAO;AACX;AAEO,SAAS,YAAY,KAAK;AAC7B,SAAO,OAAO,QAAQ;AAC1B;;;AC9HA,SAAS,OAAO,KAAK;AACjB,SAAO,mBAAmB,GAAG,EACxB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,QAAQ,GAAG,EACnB,QAAQ,SAAS,GAAG,EACpB,QAAQ,QAAQ,GAAG,EACnB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG;AAC7B;AASe,SAAR,SAA0BC,MAAK,QAAQ;AAE1C,MAAI,CAAC,QAAQ;AACT,WAAOA;AAAA,EACX;AAEA,MAAI;AACJ,MAAU,kBAAkB,MAAM,GAAG;AACjC,uBAAmB,OAAO,SAAS;AAAA,EACvC,OAAO;AACH,UAAM,QAAQ,CAAC;AAEf,IAAM,QAAQ,QAAQ,CAAC,KAAK,QAAQ;AAChC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,aAAa;AAC5C;AAAA,MACJ;AAEA,UAAU,QAAQ,GAAG,GAAG;AACpB,cAAM,GAAG,GAAG;AAAA,MAChB,OAAO;AACH,cAAM,CAAC,GAAG;AAAA,MACd;AAEA,MAAM,QAAQ,KAAK,CAAC,MAAM;AACtB,YAAU,OAAO,CAAC,GAAG;AACjB,cAAI,EAAE,YAAY;AAAA,QACtB,WAAiB,SAAS,CAAC,GAAG;AAC1B,cAAI,KAAK,UAAU,CAAC;AAAA,QACxB;AACA,cAAM,KAAK,GAAG,OAAO,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE;AAAA,MAC5C,CAAC;AAAA,IACL,CAAC;AAED,uBAAmB,MAAM,KAAK,GAAG;AAAA,EACrC;AAEA,MAAI,kBAAkB;AAClB,UAAM,gBAAgBA,KAAI,QAAQ,GAAG;AACrC,QAAI,kBAAkB,IAAI;AACtB,MAAAA,OAAMA,KAAI,MAAM,GAAG,aAAa;AAAA,IACpC;AAEA,IAAAA,SAAQA,KAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO;AAAA,EACnD;AAEA,SAAOA;AACX;;;AC5De,SAAR,cAA+BC,MAAK;AAIvC,SAAO,8BAA8B,KAAKA,IAAG;AACjD;;;ACJe,SAAR,YAA6B,SAAS,aAAa;AACtD,SAAO,cACD,GAAG,QAAQ,QAAQ,QAAQ,EAAE,CAAC,IAAI,YAAY,QAAQ,QAAQ,EAAE,CAAC,KACjE;AACV;;;ACCe,SAAR,cAA+B,SAAS,cAAc;AACzD,MAAI,WAAW,CAAC,cAAc,YAAY,GAAG;AACzC,WAAO,YAAY,SAAS,YAAY;AAAA,EAC5C;AACA,SAAO;AACX;;;ACZe,SAAR,OAAwB,SAAS,QAAQ,UAAU;AACtD,QAAM,EAAE,gBAAAC,gBAAe,IAAI,SAAS;AACpC,QAAM,SAAS,SAAS;AACxB,MAAI,WAAW,CAACA,mBAAkBA,gBAAe,MAAM,IAAI;AACvD,YAAQ,QAAQ;AAAA,EACpB,OAAO;AACH,WAAO,QAAQ;AAAA,EACnB;AACJ;;;ACJA,IAAM,YAAY,CAAC,MAAM,YAAY;AACjC,QAAM,SAAS,CAAC;AAChB,OAAK,QAAQ,CAAC,SAAS;AACnB,QAAI,CAAC,YAAY,QAAQ,IAAI,CAAC,GAAG;AAC7B,aAAO,IAAI,IAAI,QAAQ,IAAI;AAAA,IAC/B;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,IAAO,mBAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,QAAM,WAAW,SAAS,cAAc,OAAO,SAAS,OAAO,GAAG,GAAG,OAAO,MAAM;AAClF,QAAM,UAAU;AAAA,IACZ,KAAK;AAAA,IACL,QAAQ,OAAO;AAAA,IACf,UAAU,CAAC,aAAa;AACpB,aAAO,WAAW;AAClB,eAAS,SAAS;AAClB,UAAI;AAEA,YAAI,OAAO,SAAS,SAAS,UAAU;AACnC,mBAAS,OAAO,KAAK,MAAM,SAAS,IAAI;AAAA,QAC5C;AAAA,MAEJ,SAAS,GAAG;AAAA,MACZ;AACA,aAAO,SAAS,QAAQ,QAAQ;AAAA,IACpC;AAAA,EACJ;AACA,MAAI;AACJ,MAAI,OAAO,WAAW,UAAU;AAC5B,WAAO,QAAQ,OAAO,cAAc;AACpC,WAAO,QAAQ,OAAO,cAAc;AACpC,UAAM,cAAc;AAAA,MAIhB,UAAU,OAAO;AAAA,MACjB,MAAM,OAAO;AAAA,IACjB;AACA,UAAM,eAAe;AAAA,MAEjB;AAAA,MAGA;AAAA,MAGA;AAAA,MAEA;AAAA,IACJ;AACA,kBAAc,IAAI,WAAW,EAAE,GAAG,SAAS,GAAG,aAAa,GAAG,UAAU,cAAc,MAAM,EAAE,CAAC;AAAA,EACnG,WAAW,OAAO,WAAW,YAAY;AAErC,QAAI,CAAC,YAAY,OAAO,OAAO,GAAG;AAC9B,cAAQ,UAAU,OAAO;AAAA,IAC7B;AAEA,kBAAc,IAAI,aAAa,OAAO;AAAA,EAC1C,OAAO;AACH,UAAM,eAAe;AAAA,MACjB;AAAA,MACA;AAAA,MAEA;AAAA,MAEA;AAAA,MAEA;AAAA,MAMA;AAAA,IAKJ;AACA,kBAAc,IAAI,QAAQ,EAAE,GAAG,SAAS,GAAG,UAAU,cAAc,MAAM,EAAE,CAAC;AAAA,EAChF;AACA,MAAI,OAAO,SAAS;AAChB,WAAO,QAAQ,aAAa,MAAM;AAAA,EACtC;AACJ,CAAC;;;AC9FD,IAAO,0BAAQ,CAAC,WAAW,iBAAQ,MAAM;;;ACAzC,SAAS,qBAAqB;AAC1B,OAAK,WAAW,CAAC;AACrB;AAUA,mBAAmB,UAAU,MAAM,SAAS,IAAI,WAAW,UAAU;AACjE,OAAK,SAAS,KAAK;AAAA,IACf;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO,KAAK,SAAS,SAAS;AAClC;AAOA,mBAAmB,UAAU,QAAQ,SAAS,MAAM,IAAI;AACpD,MAAI,KAAK,SAAS,EAAE,GAAG;AACnB,SAAK,SAAS,EAAE,IAAI;AAAA,EACxB;AACJ;AAUA,mBAAmB,UAAU,UAAU,SAASC,SAAQ,IAAI;AACxD,OAAK,SAAS,QAAQ,CAAC,MAAM;AACzB,QAAI,MAAM,MAAM;AACZ,SAAG,CAAC;AAAA,IACR;AAAA,EACJ,CAAC;AACL;AAEA,IAAO,6BAAQ;;;ACxCf,IAAMC,aAAY,CAAC,MAAM,eAAe,YAAY;AAChD,QAAM,SAAS,CAAC;AAChB,OAAK,QAAQ,CAAC,SAAS;AACnB,QAAI,CAAC,YAAY,QAAQ,IAAI,CAAC,GAAG;AAC7B,aAAO,IAAI,IAAI,QAAQ,IAAI;AAAA,IAC/B,WAAW,CAAC,YAAY,cAAc,IAAI,CAAC,GAAG;AAC1C,aAAO,IAAI,IAAI,cAAc,IAAI;AAAA,IACrC;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAOA,IAAO,sBAAQ,CAAC,eAAe,UAAU,CAAC,MAAM;AAC5C,QAAM,SAAS,QAAQ,UAAU,cAAc,UAAU;AACzD,MAAI,SAAS;AAAA,IACT,SAAS,cAAc,WAAW;AAAA,IAClC;AAAA,IACA,KAAK,QAAQ,OAAO;AAAA,IACpB,QAAQ,QAAQ,UAAU,CAAC;AAAA,IAC3B,QAAQ,EAAE,GAAI,cAAc,UAAU,CAAC,GAAI,GAAI,QAAQ,UAAU,CAAC,EAAG;AAAA,IACrE,QAAQC,WAAU,cAAc,UAAU,CAAC,GAAG,QAAQ,UAAU,CAAC,CAAC;AAAA,EACtE;AACA,QAAM,uBAAuB,CAAC,WAAW,gBAAgB;AACzD,WAAS,EAAE,GAAG,QAAQ,GAAGD,WAAU,sBAAsB,eAAe,OAAO,EAAE;AAGjF,MAAI,WAAW,YAAY;AAEvB,QAAI,CAAC,YAAY,QAAQ,OAAO,GAAG;AAC/B,aAAO,UAAU,QAAQ;AAAA,IAC7B,WAAW,CAAC,YAAY,cAAc,OAAO,GAAG;AAC5C,aAAO,UAAU,cAAc;AAAA,IACnC;AAAA,EAEJ,WAAW,WAAW,UAAU;AAC5B,WAAO,OAAO,OAAO,cAAc;AACnC,WAAO,OAAO,OAAO,cAAc;AACnC,UAAM,aAAa;AAAA,MAEf;AAAA,MAMA;AAAA,MAEA;AAAA,MACA;AAAA,MAEA;AAAA,MAEA;AAAA,IACJ;AACA,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,CAAC,YAAY,QAAQ,IAAI,CAAC,GAAG;AAC7B,eAAO,IAAI,IAAI,QAAQ,IAAI;AAAA,MAC/B;AAAA,IACJ,CAAC;AAED,QAAI,YAAY,OAAO,OAAO,KAAK,CAAC,YAAY,cAAc,OAAO,GAAG;AACpE,aAAO,UAAU,cAAc;AAAA,IACnC;AAAA,EAEJ,OAAO;AACH,UAAM,eAAe;AAAA,MACjB;AAAA,MAEA;AAAA,MAEA;AAAA,MAEA;AAAA,MAMA;AAAA,IAKJ;AACA,aAAS,EAAE,GAAG,QAAQ,GAAGA,WAAU,cAAc,eAAe,OAAO,EAAE;AAAA,EAC7E;AAEA,SAAO;AACX;;;AClGA,IAAO,mBAAQ;AAAA,EACX,SAAS;AAAA,EACT,QAAQ,CAAC;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EAEV,cAAc;AAAA,EAEd,QAAQ,CAAC;AAAA,EAET,SAAS;AAAA,EAMT,iBAAiB;AAAA,EAKjB,gBAAgB,SAAS,eAAe,QAAQ;AAC5C,WAAO,UAAU,OAAO,SAAS;AAAA,EACrC;AACJ;;;AC3BA,IAAI,QAAS,WAAW;AACtB;AAEA,WAAS,YAAY,KAAK,MAAM;AAC9B,WAAO,QAAQ,QAAQ,eAAe;AAAA,EACxC;AAEA,MAAI;AACJ,MAAI;AACF,gBAAY;AAAA,EACd,SAAQ,GAAG;AAGT,gBAAY,WAAW;AAAA,IAAC;AAAA,EAC1B;AAEA,MAAI;AACJ,MAAI;AACF,gBAAY;AAAA,EACd,SAAQ,GAAG;AACT,gBAAY,WAAW;AAAA,IAAC;AAAA,EAC1B;AAEA,MAAI;AACJ,MAAI;AACF,oBAAgB;AAAA,EAClB,SAAQ,GAAG;AACT,oBAAgB,WAAW;AAAA,IAAC;AAAA,EAC9B;AAuBA,WAASE,OAAM,QAAQ,UAAU,OAAO,WAAW,sBAAsB;AACvE,QAAI,OAAO,aAAa,UAAU;AAChC,cAAQ,SAAS;AACjB,kBAAY,SAAS;AACrB,6BAAuB,SAAS;AAChC,iBAAW,SAAS;AAAA,IACtB;AAGA,QAAI,aAAa,CAAC;AAClB,QAAI,cAAc,CAAC;AAEnB,QAAI,YAAY,OAAO,UAAU;AAEjC,QAAI,OAAO,YAAY;AACrB,iBAAW;AAEb,QAAI,OAAO,SAAS;AAClB,cAAQ;AAGV,aAAS,OAAOC,SAAQC,QAAO;AAE7B,UAAID,YAAW;AACb,eAAO;AAET,UAAIC,WAAU;AACZ,eAAOD;AAET,UAAI;AACJ,UAAI;AACJ,UAAI,OAAOA,WAAU,UAAU;AAC7B,eAAOA;AAAA,MACT;AAEA,UAAI,YAAYA,SAAQ,SAAS,GAAG;AAClC,gBAAQ,IAAI,UAAU;AAAA,MACxB,WAAW,YAAYA,SAAQ,SAAS,GAAG;AACzC,gBAAQ,IAAI,UAAU;AAAA,MACxB,WAAW,YAAYA,SAAQ,aAAa,GAAG;AAC7C,gBAAQ,IAAI,cAAc,SAAU,SAAS,QAAQ;AACnD,UAAAA,QAAO,KAAK,SAAS,OAAO;AAC1B,oBAAQ,OAAO,OAAOC,SAAQ,CAAC,CAAC;AAAA,UAClC,GAAG,SAAS,KAAK;AACf,mBAAO,OAAO,KAAKA,SAAQ,CAAC,CAAC;AAAA,UAC/B,CAAC;AAAA,QACH,CAAC;AAAA,MACH,WAAWF,OAAM,UAAUC,OAAM,GAAG;AAClC,gBAAQ,CAAC;AAAA,MACX,WAAWD,OAAM,WAAWC,OAAM,GAAG;AACnC,gBAAQ,IAAI,OAAOA,QAAO,QAAQ,iBAAiBA,OAAM,CAAC;AAC1D,YAAIA,QAAO;AAAW,gBAAM,YAAYA,QAAO;AAAA,MACjD,WAAWD,OAAM,SAASC,OAAM,GAAG;AACjC,gBAAQ,IAAI,KAAKA,QAAO,QAAQ,CAAC;AAAA,MACnC,WAAW,aAAa,OAAO,SAASA,OAAM,GAAG;AAC/C,YAAI,OAAO,MAAM;AAEf,kBAAQ,OAAO,KAAKA,OAAM;AAAA,QAC5B,OAAO;AAEL,kBAAQ,IAAI,OAAOA,QAAO,MAAM;AAChC,UAAAA,QAAO,KAAK,KAAK;AAAA,QACnB;AACA,eAAO;AAAA,MACT,WAAW,YAAYA,SAAQ,KAAK,GAAG;AACrC,gBAAQ,OAAO,OAAOA,OAAM;AAAA,MAC9B,OAAO;AACL,YAAI,OAAO,aAAa,aAAa;AACnC,kBAAQ,OAAO,eAAeA,OAAM;AACpC,kBAAQ,OAAO,OAAO,KAAK;AAAA,QAC7B,OACK;AACH,kBAAQ,OAAO,OAAO,SAAS;AAC/B,kBAAQ;AAAA,QACV;AAAA,MACF;AAEA,UAAI,UAAU;AACZ,YAAI,QAAQ,WAAW,QAAQA,OAAM;AAErC,YAAI,SAAS,IAAI;AACf,iBAAO,YAAY,KAAK;AAAA,QAC1B;AACA,mBAAW,KAAKA,OAAM;AACtB,oBAAY,KAAK,KAAK;AAAA,MACxB;AAEA,UAAI,YAAYA,SAAQ,SAAS,GAAG;AAClC,QAAAA,QAAO,QAAQ,SAAS,OAAO,KAAK;AAClC,cAAI,WAAW,OAAO,KAAKC,SAAQ,CAAC;AACpC,cAAI,aAAa,OAAO,OAAOA,SAAQ,CAAC;AACxC,gBAAM,IAAI,UAAU,UAAU;AAAA,QAChC,CAAC;AAAA,MACH;AACA,UAAI,YAAYD,SAAQ,SAAS,GAAG;AAClC,QAAAA,QAAO,QAAQ,SAAS,OAAO;AAC7B,cAAI,aAAa,OAAO,OAAOC,SAAQ,CAAC;AACxC,gBAAM,IAAI,UAAU;AAAA,QACtB,CAAC;AAAA,MACH;AAEA,eAAS,KAAKD,SAAQ;AACpB,YAAI,QAAQ,OAAO,yBAAyBA,SAAQ,CAAC;AACrD,YAAI,OAAO;AACT,gBAAM,CAAC,IAAI,OAAOA,QAAO,CAAC,GAAGC,SAAQ,CAAC;AAAA,QACxC;AAEA,YAAI;AACF,cAAI,cAAc,OAAO,yBAAyBD,SAAQ,CAAC;AAC3D,cAAI,YAAY,QAAQ,aAAa;AAEnC;AAAA,UACF;AACA,gBAAM,CAAC,IAAI,OAAOA,QAAO,CAAC,GAAGC,SAAQ,CAAC;AAAA,QACxC,SAAQ,GAAE;AACR,cAAI,aAAa,WAAW;AAG1B;AAAA,UACF,WAAW,aAAa,gBAAgB;AAEtC;AAAA,UACF;AAAA,QACF;AAAA,MAEF;AAEA,UAAI,OAAO,uBAAuB;AAChC,YAAI,UAAU,OAAO,sBAAsBD,OAAM;AACjD,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAGvC,cAAI,SAAS,QAAQ,CAAC;AACtB,cAAI,aAAa,OAAO,yBAAyBA,SAAQ,MAAM;AAC/D,cAAI,cAAc,CAAC,WAAW,cAAc,CAAC,sBAAsB;AACjE;AAAA,UACF;AACA,gBAAM,MAAM,IAAI,OAAOA,QAAO,MAAM,GAAGC,SAAQ,CAAC;AAChD,iBAAO,eAAe,OAAO,QAAQ,UAAU;AAAA,QACjD;AAAA,MACF;AAEA,UAAI,sBAAsB;AACxB,YAAI,mBAAmB,OAAO,oBAAoBD,OAAM;AACxD,iBAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,cAAI,eAAe,iBAAiB,CAAC;AACrC,cAAI,aAAa,OAAO,yBAAyBA,SAAQ,YAAY;AACrE,cAAI,cAAc,WAAW,YAAY;AACvC;AAAA,UACF;AACA,gBAAM,YAAY,IAAI,OAAOA,QAAO,YAAY,GAAGC,SAAQ,CAAC;AAC5D,iBAAO,eAAe,OAAO,cAAc,UAAU;AAAA,QACvD;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,OAAO,QAAQ,KAAK;AAAA,EAC7B;AASA,EAAAF,OAAM,iBAAiB,SAAS,eAAe,QAAQ;AACrD,QAAI,WAAW;AACb,aAAO;AAET,QAAI,IAAI,WAAY;AAAA,IAAC;AACrB,MAAE,YAAY;AACd,WAAO,IAAI,EAAE;AAAA,EACf;AAIA,WAAS,WAAW,GAAG;AACrB,WAAO,OAAO,UAAU,SAAS,KAAK,CAAC;AAAA,EACzC;AACA,EAAAA,OAAM,aAAa;AAEnB,WAAS,SAAS,GAAG;AACnB,WAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,EACpD;AACA,EAAAA,OAAM,WAAW;AAEjB,WAAS,UAAU,GAAG;AACpB,WAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,EACpD;AACA,EAAAA,OAAM,YAAY;AAElB,WAAS,WAAW,GAAG;AACrB,WAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,EACpD;AACA,EAAAA,OAAM,aAAa;AAEnB,WAAS,iBAAiB,IAAI;AAC5B,QAAI,QAAQ;AACZ,QAAI,GAAG;AAAQ,eAAS;AACxB,QAAI,GAAG;AAAY,eAAS;AAC5B,QAAI,GAAG;AAAW,eAAS;AAC3B,WAAO;AAAA,EACT;AACA,EAAAA,OAAM,mBAAmB;AAEzB,SAAOA;AACT,EAAG;AAEH,IAAO,gBAAQ;;;ACnPf,IAAqB,UAArB,MAA6B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAezB,YAAY,MAAM,CAAC,GAAG;AAElB,QAAI,CAAC,cAAc,GAAG,GAAG;AACrB,YAAM,CAAC;AACP,cAAQ,KAAK,oBAAoB;AAAA,IACrC;AACA,SAAK,SAAS,cAAM,EAAE,GAAG,kBAAU,GAAG,IAAI,CAAC;AAC3C,SAAK,eAAe;AAAA,MAChB,SAAS,IAAI,2BAAmB;AAAA,MAChC,UAAU,IAAI,2BAAmB;AAAA,IACrC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG;AACT,SAAK,SAAS,EAAE,KAAK,MAAM;AAAA,EAC/B;AAAA,EAEA,WAAW,QAAQ;AACf,aAAS,oBAAY,KAAK,QAAQ,MAAM;AACxC,UAAM,QAAQ,CAAC,yBAAiB,MAAS;AACzC,QAAIG,WAAU,QAAQ,QAAQ,MAAM;AAEpC,SAAK,aAAa,QAAQ,QAAQ,CAAC,gBAAgB;AAC/C,YAAM,QAAQ,YAAY,WAAW,YAAY,QAAQ;AAAA,IAC7D,CAAC;AAED,SAAK,aAAa,SAAS,QAAQ,CAAC,gBAAgB;AAChD,YAAM,KAAK,YAAY,WAAW,YAAY,QAAQ;AAAA,IAC1D,CAAC;AAED,WAAO,MAAM,QAAQ;AACjB,MAAAA,WAAUA,SAAQ,KAAK,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC;AAAA,IACvD;AAEA,WAAOA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,QAAQ,SAAS,CAAC,GAAG;AACjB,WAAO,KAAK,WAAW,MAAM;AAAA,EACjC;AAAA,EAEA,IAAIC,MAAK,UAAU,CAAC,GAAG;AACnB,WAAO,KAAK,WAAW;AAAA,MACnB,KAAAA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EAEA,KAAKA,MAAK,MAAM,UAAU,CAAC,GAAG;AAC1B,WAAO,KAAK,WAAW;AAAA,MACnB,KAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EAGA,IAAIA,MAAK,MAAM,UAAU,CAAC,GAAG;AACzB,WAAO,KAAK,WAAW;AAAA,MACnB,KAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EAKA,OAAOA,MAAK,MAAM,UAAU,CAAC,GAAG;AAC5B,WAAO,KAAK,WAAW;AAAA,MACnB,KAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EAKA,QAAQA,MAAK,MAAM,UAAU,CAAC,GAAG;AAC7B,WAAO,KAAK,WAAW;AAAA,MACnB,KAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EAKA,KAAKA,MAAK,MAAM,UAAU,CAAC,GAAG;AAC1B,WAAO,KAAK,WAAW;AAAA,MACnB,KAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EAKA,QAAQA,MAAK,MAAM,UAAU,CAAC,GAAG;AAC7B,WAAO,KAAK,WAAW;AAAA,MACnB,KAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EAKA,MAAMA,MAAK,MAAM,UAAU,CAAC,GAAG;AAC3B,WAAO,KAAK,WAAW;AAAA,MACnB,KAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EAIA,OAAOA,MAAK,SAAS,CAAC,GAAG;AACrB,WAAO,MAAMA;AACb,WAAO,SAAS;AAChB,WAAO,KAAK,WAAW,MAAM;AAAA,EACjC;AAAA,EAEA,SAASA,MAAK,SAAS,CAAC,GAAG;AACvB,WAAO,MAAMA;AACb,WAAO,SAAS;AAChB,WAAO,KAAK,WAAW,MAAM;AAAA,EACjC;AACJ;;;AC7LA,IAAO,uBAAQ;;;ACAf,IAAM,OAAO,IAAI,qBAAQ;AACzB,IAAO,eAAQ;;;ACMf,IAAO,sBAAQ;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,SAAS,CAAC;AAAA,IACV,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,OAAO;AAAA,IACP,eAAe;AAAA,EACnB;AACJ;;;AChBA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM,CAAC;AAAA,IACP,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,EACV;AACJ;;;AClBA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU;AAAA,EACd;AACJ;;;ACZA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,IACV,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,MAAM;AAAA,EACV;AACJ;;;AClBA,IAAO,sBAAQ;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM,CAAC;AAAA,IACP,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,KAAK;AAAA,IACX,YAAY;AAAA,EACV;AACJ;;;ACbA,IAAO,kBAAQ;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,IACd;AAAA,EACJ;AACJ;;;ACjBA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ,CAAC;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AACJ;;;ACjBA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,MAAM;AAAA,IACN,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,EACV;AACJ;;;ACjCA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,aAAa;AAAA,IACb,UAAU,OAAO;AAAA;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,aAAa;AAAA,IACb,qBAAqB;AAAA,IACrB,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU,OAAO;AAAA;AAAA,IACjB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACpB,OAAO;AAAA,IACP,UAAU;AAAA,IACJ,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,IAC5C,YAAY,CAAC;AAAA,IACb,iBAAiB;AAAA,EACrB;AACJ;;;ACnCA,IAAO,sBAAQ;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,QAAQ;AAAA,EACZ;AACJ;;;ACLA,IAAO,eAAQ;AAAA;AAAA,EAEd,MAAM;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,WAAW,CAAC;AAAA,IACZ,WAAW,CAAC;AAAA,IACZ,WAAW,CAAC;AAAA,IACZ,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,IACP,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,EAClB;AACD;;;AC9BA,IAAO,eAAQ;AAAA;AAAA,EAEd,MAAM;AAAA,IACL,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW,CAAC;AAAA,IACZ,gBAAgB,CAAC;AAAA,IACjB,WAAW;AAAA,IACX,YAAY,CAAC;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACP;AACD;;;ACzBA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa,CAAC;AAAA,EAClB;AACJ;;;ACPA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,eAAe;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,EACnB;AACJ;;;ACjBA,IAAO,wBAAQ;AAAA;AAAA,EAEX,eAAe;AAAA,IACX,MAAM;AAAA,IACN,OAAO,CAAC;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,IACV,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,EAClB;AACJ;;;ACnBA,IAAO,yBAAQ;AAAA;AAAA,EAEX,gBAAgB;AAAA,IACZ,YAAY;AAAA,EAChB;AACJ;;;ACJA,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,aAAa;AAAA,IACb,WAAW;AAAA,EACf;AACJ;;;ACXA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACb,gBAAgB;AAAA,IACV,WAAW;AAAA,IACX,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACnB,aAAa;AAAA,EACX;AACJ;;;ACnBA,IAAO,cAAQ;AAAA;AAAA,EAEX,KAAK;AAAA,IACD,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,WAAW;AAAA,EACf;AACJ;;;ACTA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,OAAO;AAAA,IACP,WAAW;AAAA,IACX,QAAQ;AAAA,EACZ;AACJ;;;ACPA,IAAO,uBAAQ;AAAA;AAAA,EAEX,cAAc;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,YAAY,CAAC;AAAA,IACb,WAAW,CAAC;AAAA,IAClB,gBAAgB,CAAC;AAAA,IACX,iBAAiB,CAAC;AAAA,IAClB,iBAAiB;AAAA,EACrB;AACJ;;;ACrBA,IAAO,uBAAQ;AAAA;AAAA,EAEX,cAAc;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,cAAc;AAAA,IACpB,gBAAgB;AAAA,EACd;AACJ;;;ACfA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,EACjB;AACJ;;;ACRA,IAAO,kBAAQ;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,WAAW;AAAA,EACf;AACJ;;;ACfA,IAAO,yBAAQ;AAAA;AAAA,EAEX,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACZ,WAAW;AAAA,IACL,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS,IAAI,MAAK,oBAAI,KAAK,GAAE,YAAY,IAAI,IAAI,GAAG,CAAC,EAAE,QAAQ;AAAA,IAC/D,SAAS,IAAI,MAAK,oBAAI,KAAK,GAAE,YAAY,IAAI,IAAI,GAAG,CAAC,EAAE,QAAQ;AAAA,IAC/D,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,qBAAqB;AAAA,IACrB,cAAc,CAAC;AAAA,IACf,aAAa;AAAA,IACb,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,EACjB;AACJ;;;AChCA,IAAO,kBAAQ;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,KAAK;AAAA,IACL,cAAc;AAAA,IACd,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,EACf;AAEJ;;;ACbA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,WAAW;AAAA,EACf;AAEJ;;;AChBA,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,IACR,WAAW;AAAA,IACX,cAAc;AAAA,IACd,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY,CAAC;AAAA,EACjB;AACJ;;;ACZA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO,CAAC;AAAA,IACR,cAAc;AAAA,IACd,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,eAAe;AAAA,EACnB;AACJ;;;ACdA,IAAO,cAAQ;AAAA;AAAA,EAEX,KAAK;AAAA,IACD,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,cAAc;AAAA,IACd,aAAa,CAAC;AAAA,EAClB;AACJ;;;ACTA,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AACJ;;;ACPA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AACJ;;;ACJA,IAAM;AAAA,EACF,OAAAC;AACJ,IAAI;AACJ,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,MAAM;AAAA,IACN,OAAOA,OAAM,iBAAiB;AAAA,IAC9B,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,OAAO;AAAA,IACP,UAAU;AAAA,IACV,WAAW;AAAA,IACX,YAAYA,OAAM,iBAAiB;AAAA,IACnC,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,EACV;AACJ;;;AC1BA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AACJ;;;ACpBA,IAAO,sBAAQ;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ;AACJ;;;ACTA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW,CAAC;AAAA,IACZ,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,eAAe;AAAA,EACnB;AACJ;;;ACVA,IAAO,gBAAQ;AAAA;AAAA,EAEd,OAAO;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,IACP,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,EACZ;AACD;;;ACtCA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,EAChB;AACJ;;;ACpBA,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;;;ACVA,IAAO,uBAAQ;AAAA;AAAA,EAEX,cAAc;AAAA,IACV,aAAa;AAAA,IACb,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,QAAQ;AAAA,IACd,WAAW;AAAA,EACT;AACJ;;;ACRA,IAAM;AAAA,EACF,OAAAC;AACJ,IAAI;AACJ,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,OAAOA,OAAM,WAAW;AAAA,IACxB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,MAAM;AAAA,EACV;AACJ;;;AChBA,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,eAAe;AAAA,EACnB;AACJ;;;AClBA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,QAAQ;AAAA,EACZ;AACJ;;;ACHA,IAAM;AAAA,EACF,OAAAC;AACJ,IAAI;AACJ,IAAO,sBAAQ;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM;AAAA,IACN,OAAOA,OAAM,cAAc;AAAA,IAC3B,WAAWA,OAAM,cAAc;AAAA,IAC/B,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,eAAe;AAAA,EACnB;AACJ;;;ACpBA,IAAO,sBAAQ;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,aAAa;AAAA,IACb,OAAO;AAAA,IACP,aAAa;AAAA,IACb,SAAS;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,QAAQ;AAAA,EACZ;AACJ;;;ACdA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,IAChB,UAAU;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,MAAM;AAAA,IACZ,WAAW;AAAA,IACX,QAAQ;AAAA,EACN;AACJ;;;ACtBA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc,CAAC;AAAA,EACnB;AACJ;;;ACxBA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACd,cAAc;AAAA,IACd,eAAe,cAAM;AAAA,IACrB,UAAU;AAAA,IACV,YAAY;AAAA,EACV;AAEJ;;;ACvBA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AAEJ;;;ACRA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM,CAAC;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,KAAK;AAAA,IACL,UAAU;AAAA,IAChB,gBAAgB;AAAA,EACd;AACJ;;;AClBA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,kBAAkB;AAAA,EACtB;AACJ;;;ACZA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,KAAK,OAAO;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,OAAO;AAAA,IACP,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,cAAc;AAAA,IACd,eAAe;AAAA,IACf,cAAc;AAAA,IACd,aAAa;AAAA,IACb,WAAW;AAAA,IACX,UAAU;AAAA,EACd;AACJ;;;AC7BA,IAAO,yBAAQ;AAAA;AAAA,EAEX,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACZ;AACJ;;;ACPA,IAAO,kBAAQ;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AACJ;;;ACRA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,aAAa;AAAA,EACjB;AACJ;;;ACZA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,MAAM;AAAA,IACZ,WAAW;AAAA,IACL,aAAa;AAAA,IACb,OAAO;AAAA,IACP,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACf,WAAW;AAAA,IACL,qBAAqB;AAAA,IACrB,cAAc,CAAC;AAAA,IACrB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACF,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,IACb,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,gBAAgB;AAAA,EACpB;AACJ;;;AC9BA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc,CAAC;AAAA,IACf,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,qBAAqB;AAAA,IACrB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,EACpB;AACJ;;;ACnBA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,eAAe;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,EACf;AACJ;;;ACjBA,IAAO,qBAAQ;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,IACV,OAAO;AAAA,IACP,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,IACf,KAAK;AAAA,EACT;AACJ;;;ACrBA,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,eAAe;AAAA,IACf,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,EACf;AACJ;;;AChBA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,MAAM;AAAA,EACV;AACJ;;;ACZA,IAAO,cAAQ;AAAA;AAAA,EAEX,KAAK;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,EACX;AACJ;;;ACPA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACX;AACJ;;;ACXA,IAAO,qBAAQ;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,EACpB;AACJ;;;ACVA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,SAAS;AAAA,IACT,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa,CAAC;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY,CAAC;AAAA,IACb,UAAU;AAAA,IACV,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,OAAO;AAAA,IACP,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,OAAO;AAAA,IACP,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AACJ;;;AC5BA,IAAO,kBAAQ;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,OAAO;AAAA,IACP,UAAU;AAAA,IACV,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,EACX;AACJ;;;ACdA,IAAO,mBAAQ;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,aAAa;AAAA,EACjB;AACJ;;;ACfA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,WAAW;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,IACN,aAAa;AAAA,IACb,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACjB,UAAS;AAAA,IACH,YAAY,CAAC;AAAA,IACb,WAAW;AAAA,IACX,QAAQ;AAAA,EACZ;AACJ;;;ACjBA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,SAAS;AAAA,IACf,QAAQ;AAAA,EACN;AACJ;;;ACNA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,WAAW;AAAA,IACX,SAAS;AAAA,IACT,aAAa;AAAA,IACb,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,KAAK;AAAA,EACT;AACJ;;;ACXA,IAAO,oBAAQ;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,EACX;AACJ;;;ACRA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AACJ;;;ACVA,IAAO,qBAAQ;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,MAAM,CAAC;AAAA,IACP,SAAS;AAAA,IACT,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,UAAU;AAAA,EACd;AACJ;;;AChBA,IAAO,sBAAQ;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,WAAW;AAAA,EACf;AACJ;;;ACLA,IAAO,0BAAQ;AAAA;AAAA,EAEX,iBAAiB;AAAA,IACb,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,SAAS,CAAC;AAAA,IACV,UAAU;AAAA,EACd;AACJ;;;ACZA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,MAAM,CAAC;AAAA,IACP,WAAW;AAAA,IACX,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACf;AAEJ;;;AC7BA,IAAO,2BAAQ;AAAA;AAAA,EAEX,iBAAiB;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IAC9B,eAAe;AAAA,EACb;AACJ;;;ACTA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,aAAa;AAAA,IACb,eAAe;AAAA,IACf,OAAO;AAAA,IACP,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,OAAO;AAAA,EACX;AACJ;;;ACdA,IAAO,iBAAQ;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,eAAe;AAAA,IACf,OAAO;AAAA,IACP,aAAa;AAAA,EACjB;AACJ;;;ACZA,IAAO,qBAAQ;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,EAChB;AACJ;;;ACVA,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,UAAU;AAAA,IACV,MAAM,CAAC;AAAA,IACP,WAAW;AAAA,IACX,aAAa;AAAA,MACT,OAAO;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACX,OAAO;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,YAAY;AAAA,IAClB,SAAS;AAAA,IACT,SAAS;AAAA,IACH,WAAW,CAAC;AAAA,EAChB;AACJ;;;ACvBA,IAAO,cAAQ;AAAA;AAAA,EAEd,KAAK;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,cAAc;AAAA,IACd,aAAa;AAAA,EACd;AACD;;;ACzBA,IAAO,eAAQ;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,WAAW;AAAA,MACP,UAAU;AAAA,IACd;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,UAAU;AAAA,IAChB,OAAO;AAAA,EACL;AAEJ;;;AC7BA,IAAO,mBAAQ;AAAA;AAAA,EAEd,UAAU;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,uBAAuB;AAAA,IACvB,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,EACZ;AACD;;;AC1BA,IAAO,gBAAQ;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU;AAAA,IACV,MAAM;AAAA,EACV;AAEJ;;;ACpBA,IAAO,kBAAQ;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,EACX;AAEJ;;;ACXA,IAAO,kBAAQ;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS,CAAC;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,EACf;AACJ;;;ACfA,IAAO,qBAAQ;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,EACpB;AACJ;;;ACRA,IAAO,iBAAQ;AAAA;AAAA,EAEd,QAAQ;AAAA,IACP,QAAQ;AAAA,IACR,WAAW,CAAC;AAAA,IACZ,SAAS,CAAC,SAAS,QAAQ;AAAA,IAC3B,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,MAAM;AAAA,IACN,UAAU,CAAC,YAAY,YAAY;AAAA,IACnC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS,OAAO;AAAA,IAChB,UAAU,CAAC;AAAA,IACX,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,kBAAkB,CAAC;AAAA,IACnB,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,EACxB;AACD;;;AC2DA,IAAM,QAAQ;AAAA,EACV,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACP;AAEA,SAAS,UAAU,SAAS;AAC3B,eAAa,gBAAQ,QAAQ,UAAU,CAAC,CAAC;AACzC,eAAa,OAAO,QAAQ,SAAS,CAAC,CAAC;AACvC,eAAa,eAAO,QAAQ,SAAS,CAAC,CAAC;AACvC,eAAa,gBAAQ,QAAQ,UAAU,CAAC,CAAC;AAC1C;AAGA,IAAI,OAAO,IAAI,YAAY;AAC1B,UAAQ,IAAI,oBAAoB;AAChC,MAAI,OAAO,IAAI,WAAW;AAC1B,MAAI,KAAK,SAAS;AACjB,SAAK,QAAQ,YAAI;AAAA,EAClB;AACA,MAAI,KAAK,SAAS;AACjB,cAAU,KAAK,OAAO;AAAA,EACvB;AACD;AAEA,IAAO,gBAAQ;;;AC/Mf,IAAI,WAAW;AAGf,WAAW;AAgBX,WAAW;AA+CX,IAAO,mBAAQ;;;ACxEf,SAAS,KAAK,IAAI;AACd,MAAI,SAAS;AACb,MAAI;AAEJ,SAAO,YAAY,MAAM;AACrB,QAAI,CAAC,QAAQ;AACT,eAAS,GAAG,MAAM,MAAM,IAAI;AAC5B,eAAS;AAAA,IACb;AACA,WAAO;AAAA,EACX;AACJ;AAGA,IAAM,WAAW,KAAK,MAAM;AAkBxB,MAAI,aAAa;AAAA,IACb,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ,UAAU,eAAO,UAAU;AAAA,IACnC,UAAU;AAAA,IAEV;AAAA,IACA,OAAO;AAAA,IAEP;AAAA,EACJ,CAAC;AACD,MAAI,eAAO,WAAW,QAAQ;AAC1B,QAAI,aAAa;AAAA,MACb,QAAQ;AAAA;AAAA,MACR,QAAQ,eAAO,WAAW;AAAA,MAC1B,QAAQ,UAAU,eAAO,WAAW,MAAM;AAAA,MAC1C,UAAU;AAAA,MAEV;AAAA,MACA,OAAO;AAAA,MAEP;AAAA,IACJ,CAAC;AAAA,EACL;AAUA,SAAO;AACX,CAAC;AAED,IAAI,WAAW;AAAA,EACX;AACJ;AAEA,IAAO,eAAQ;;;AC9Bf,IAAI,YAAY,CAAC,WAAW,WAAW,SAAS,WAAW,MAAM;AAY1D,SAASC,WAAU,SAAS;AAClC,mBAAM,aAAa,gBAAQ,QAAQ,UAAU,CAAC,CAAC;AAC/C,mBAAM,aAAa,eAAO,QAAQ,SAAS,CAAC,CAAC;AAC7C,mBAAM,aAAa,eAAO,QAAQ,SAAS,CAAC,CAAC;AAC7C,mBAAM,aAAa,gBAAQ,QAAQ,UAAU,CAAC,CAAC;AAChD;AACA,iBAAM,YAAYA;AAElB,IAAM,KAAK;AAAA,EACP;AAAA,EACA,MAAM,iBAAM;AAAA;AAAA,EACZ,eAAe,sBAAc;AAAA,EAC7B,UAAU,sBAAc;AAAA,EACxB,UAAU,sBAAc;AAAA,EACxB,aAAa,sBAAc;AAAA,EAC3B;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACH;AAAA,EACG;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AAAA,EACH;AAAA,EACA;AACJ;AAEO,IAAM,UAAU,WAAW;AAC9B,MAAI,KAAK;AACb;AAWA,IAAM,WAAW,YAAY,KAAK,4BAA4B,EAAE,OAAO,KAAK,CAAC;AAC7E,IAAI,aAAa,CAAC;AAGlB,WAAW,OAAO,UAAU;AACxB,MAAI,YAAY,SAAS,GAAG,EAAE;AAC9B,MAAI,UAAU,QAAQ,UAAU,KAAK,QAAQ,KAAK,MAAM,GAAG;AACvD,cAAU,UAAU,SAAU,KAAK;AAC/B,UAAI,UAAU,MAAM,SAAS;AAAA,IACjC;AAGA,eAAW,KAAK,SAAS;AAAA,EAC7B;AACJ;AAGA,IAAM,UAAU,CAAC,KAAK,aAAa,OAAO;AAEtC,aAAW,QAAQ,SAAS,WAAW;AACnC,UAAMC,QAAO,UAAU,KAAK,QAAQ,uBAAuB,OAAO;AACxE,QAAIA,SAAQ,UAAU,MAAM;AAC3B,UAAI,UAAU,UAAU,MAAM,SAAS;AAAA,IACxC;AACM,QAAI,UAAUA,OAAM,SAAS;AAAA,EACjC,CAAC;AAIJ,MAAI,YAAY;AACf,QAAI,aAAa;AACjB,QAAI,OAAO,WAAW;AACtB,QAAI,KAAK,SAAS;AACjB,WAAK,QAAQ,YAAI;AAAA,IAClB;AACA,QAAI,KAAK,SAAS;AACjB,MAAAC,WAAU,KAAK,OAAO;AAAA,IACvB;AAAA,EACD;AAIG,MAAI,KAAK;AAIT,MAAI,OAAO,iBAAiB,KAAK;AACjC,MAAI,MAAM,KAAK;AAEnB;AAEA,IAAO,qBAAQ;AAAA,EACX;AACJ;", "names": ["flag", "range", "name", "string", "array", "times", "date", "timer", "number", "key", "pages", "color", "url", "name", "url", "color", "func", "func", "deepMerge", "url", "url", "validateStatus", "for<PERSON>ach", "mergeKeys", "deepMerge", "clone", "parent", "depth", "promise", "url", "color", "color", "color", "setConfig", "name", "setConfig"]}