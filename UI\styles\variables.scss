/* ===== 设计系统变量定义 ===== */

/* === 颜色系统 === */
// 主色调 - 蓝色系（避免紫色）
$primary-color: #186BFF;
$primary-light: #186BFF;
$primary-dark: #186BFF;
$primary-hover: #186BFF;
$primary-active: #096dd9;

// 辅助色
$secondary-color: #6c757d;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$info-color: #186BFF;

// 中性色
$white: #ffffff;
$gray-50: #fafafa;
$gray-100: #f5f5f5;
$gray-200: #f0f0f0;
$gray-300: #d9d9d9;
$gray-400: #bfbfbf;
$gray-500: #8c8c8c;
$gray-600: #595959;
$gray-700: #434343;
$gray-800: #262626;
$gray-900: #1f1f1f;

// 文本颜色
$text-primary: #262626;
$text-secondary: #595959;
$text-tertiary: #8c8c8c;
$text-disabled: #bfbfbf;
$text-white: #ffffff;

// 背景色
$bg-primary: #ffffff;
$bg-secondary: #fafafa;
$bg-tertiary: #f5f5f5;
$bg-disabled: #f5f5f5;
$bg-mask: rgba(0, 0, 0, 0.45);

// 边框色
$border-primary: #d9d9d9;
$border-secondary: #f0f0f0;
$border-light: #f5f5f5;

/* === 字体系统 === */
// 字体大小 (rpx单位)
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-md: 32rpx;
$font-size-lg: 36rpx;
$font-size-xl: 40rpx;
$font-size-xxl: 48rpx;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.4;
$line-height-relaxed: 1.6;

/* === 间距系统 === */
// 基础间距单位 (rpx)
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-base: 16rpx;
$spacing-md: 20rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;
$spacing-xxl: 40rpx;
$spacing-xxxl: 48rpx;

// 页面间距
$page-padding: 20rpx;
$section-margin: 24rpx;
$card-padding: 24rpx;

/* === 圆角系统 === */
$border-radius-xs: 4rpx;
$border-radius-sm: 8rpx;
$border-radius-base: 12rpx;
$border-radius-md: 16rpx;
$border-radius-lg: 20rpx;
$border-radius-xl: 24rpx;
$border-radius-round: 50%;

/* === 阴影系统 === */
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$shadow-base: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
$shadow-md: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.16);

/* === 动画系统 === */
$transition-fast: 0.2s;
$transition-base: 0.3s;
$transition-slow: 0.5s;

$ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
$ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
$ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);

/* === 布局系统 === */
// 容器宽度
$container-max-width: 1200rpx;
$content-max-width: 750rpx;

// 头部高度
$header-height: 0rpx;
$navbar-height: 100rpx;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 10080;
$z-index-modal: 10100;
$z-index-popover: 10110;
$z-index-tooltip: 10120;

/* === 响应式断点 === */
$breakpoint-sm: 576rpx;
$breakpoint-md: 768rpx;
$breakpoint-lg: 992rpx;
$breakpoint-xl: 1200rpx;

/* === 表单控件 === */
$input-height: 88rpx;
$input-padding: 24rpx;
$button-height: 88rpx;
$button-padding: 32rpx;

/* === 状态颜色 === */
$status-online: #52c41a;
$status-offline: #8c8c8c;
$status-busy: #faad14;
$status-away: #ff4d4f;