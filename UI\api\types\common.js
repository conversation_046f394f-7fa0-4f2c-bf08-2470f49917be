/**
 * 通用类型定义
 * 定义API响应的通用数据结构
 */

/**
 * API响应基础结构
 * @typedef {Object} ApiResult
 * @property {number} code - 响应状态码 (200: 成功, 其他: 失败)
 * @property {string} message - 响应消息
 * @property {*} data - 响应数据
 * @property {number} timestamp - 时间戳
 */

/**
 * 分页查询结果
 * @typedef {Object} PageResult
 * @property {Array} list - 数据列表
 * @property {number} total - 总记录数
 * @property {number} page - 当前页码
 * @property {number} pageSize - 每页大小
 * @property {number} totalPages - 总页数
 */

/**
 * 分页查询参数基础结构
 * @typedef {Object} BasePageParams
 * @property {number} page - 页码
 * @property {number} pageSize - 每页大小
 */

/**
 * 时间范围查询参数
 * @typedef {Object} TimeRangeParams
 * @property {string} startTime - 开始时间
 * @property {string} endTime - 结束时间
 */

/**
 * 用户基础信息
 * @typedef {Object} BaseUser
 * @property {number} id - 用户ID
 * @property {string} username - 用户名
 * @property {string} name - 姓名
 * @property {string} role - 角色
 * @property {string} status - 状态
 */

/**
 * 视频基础信息
 * @typedef {Object} BaseVideo
 * @property {number} id - 视频ID
 * @property {string} title - 视频标题
 * @property {string} url - 视频地址
 * @property {number} duration - 视频时长(秒)
 * @property {string} category - 视频分类
 * @property {string} status - 状态
 */

/**
 * 测验基础信息
 * @typedef {Object} BaseQuiz
 * @property {number} id - 测验ID
 * @property {string} title - 测验标题
 * @property {number} videoId - 关联视频ID
 * @property {number} questionCount - 题目数量
 * @property {number} passingScore - 及格分数
 * @property {string} status - 状态
 */

/**
 * 操作结果
 * @typedef {Object} OperationResult
 * @property {boolean} success - 是否成功
 * @property {string} message - 操作消息
 * @property {*} data - 返回数据
 */

// 导出类型定义（用于JSDoc）
export const Types = {
  ApiResult: 'ApiResult',
  PageResult: 'PageResult',
  BasePageParams: 'BasePageParams',
  TimeRangeParams: 'TimeRangeParams',
  BaseUser: 'BaseUser',
  BaseVideo: 'BaseVideo',
  BaseQuiz: 'BaseQuiz',
  OperationResult: 'OperationResult'
}

// 状态常量
export const STATUS = {
  // 通用状态
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  DELETED: 'deleted',
  
  // 视频状态
  VIDEO_DRAFT: 'draft',
  VIDEO_PUBLISHED: 'published',
  VIDEO_ARCHIVED: 'archived',
  
  // 测验状态
  QUIZ_DRAFT: 'draft',
  QUIZ_ACTIVE: 'active',
  QUIZ_CLOSED: 'closed',
  
  // 用户状态
  USER_ACTIVE: 'active',
  USER_INACTIVE: 'inactive',
  USER_LOCKED: 'locked'
}

// 角色常量
export const ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  EMPLOYEE: 'employee',
  AGENT: 'agent'
}

// API响应码常量
export const API_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500
}

export default {
  Types,
  STATUS,
  ROLES,
  API_CODES
}
