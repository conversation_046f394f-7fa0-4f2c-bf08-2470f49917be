/**
 * 统计分析相关API
 * 基于新的12个统计API端点完全重写
 * 支持用户每日统计、汇总数据、趋势分析、仪表板数据等
 *
 * 更新日期: 2025-01-18
 * API版本: v1.0
 *
 * 包含的API端点:
 * 1. /Statistics/user-daily - 获取用户每日统计数据（分页）
 * 2. /Statistics/user-summary/{userId} - 获取用户统计汇总数据
 * 3. /Statistics/users-summary - 批量获取用户统计汇总数据
 * 4. /Statistics/employee-summary/{employeeId} - 获取员工负责用户的统计汇总
 * 5. /Statistics/daily-trend - 获取每日统计趋势
 * 6. /Statistics/overview - 获取统计概览数据（用于仪表板）
 * 7. /Statistics/user/{userId} - 获取用户统计数据（兼容性接口）
 * 8. /Statistics/my-statistics - 获取我的统计数据（兼容性接口）
 * 9. /Statistics/summary - 获取统计汇总数据（兼容性接口）
 * 10. /Statistics/dashboard - 获取仪表板数据
 * 11. /Statistics/dashboard/key-metrics - 获取仪表板关键指标汇总
 * 12. /Statistics/dashboard/today - 获取今日数据快照
 */

import request from '../utils/request.js'

/**
 * 1. 获取用户每日统计数据（分页）
 * @param {import('./types/statistics.js').UserDailyStatisticsParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/statistics.js').PagedResult<import('./types/statistics.js').VideoUserDailyStatisticsDto>>>}
 */
export function getUserDailyStatistics (params = {}) {
  return request.get('/Statistics/user-daily', params)
}

/**
 * 2. 获取用户统计汇总数据
 * @param {number} userId - 用户ID
 * @param {import('./types/statistics.js').UserSummaryParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/statistics.js').VideoUserStatisticsSummaryDto>>}
 */
export function getUserSummary (userId, params = {}) {
  return request.get(`/Statistics/user-summary/${userId}`, params)
}

/**
 * 3. 批量获取用户统计汇总数据
 * @param {import('./types/statistics.js').UsersSummaryParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<Array<import('./types/statistics.js').VideoUserStatisticsSummaryDto>>>}
 */
export function getUsersSummary (params = {}) {
  return request.get('/Statistics/users-summary', params)
}

/**
 * 4. 获取员工负责用户的统计汇总
 * @param {number} employeeId - 员工ID
 * @param {import('./types/statistics.js').UserSummaryParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/statistics.js').VideoUserStatisticsSummaryDto>>}
 */
export function getEmployeeSummary (employeeId, params = {}) {
  return request.get(`/Statistics/employee-summary/${employeeId}`, params)
}

/**
 * 5. 获取每日统计趋势
 * @param {import('./types/statistics.js').DailyTrendParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<Array<import('./types/statistics.js').VideoDailyStatisticsTrendDto>>>}
 */
export function getDailyTrend (params = {}) {
  return request.get('/Statistics/daily-trend', params)
}

/**
 * 6. 获取统计概览数据（用于仪表板）
 * @param {import('./types/statistics.js').OverviewParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/statistics.js').VideoStatisticsOverviewDto>>}
 */
export function getStatisticsOverview (params = {}) {
  return request.get('/Statistics/overview', params)
}

/**
 * 7. 获取用户统计数据（兼容性接口）
 * @param {number} userId - 用户ID
 * @param {import('./types/statistics.js').UserSummaryParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<Array<import('./types/statistics.js').VideoStatisticsResponseDto>>>}
 */
export function getUserStatistics (userId, params = {}) {
  return request.get(`/Statistics/user/${userId}`, params)
}

/**
 * 8. 获取我的统计数据（兼容性接口）
 * @param {import('./types/statistics.js').UserSummaryParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<Array<import('./types/statistics.js').VideoStatisticsResponseDto>>>}
 */
export function getMyStatistics (params = {}) {
  return request.get('/Statistics/my-statistics', params)
}

/**
 * 9. 获取统计汇总数据（兼容性接口）
 * @param {import('./types/statistics.js').VideoStatisticsSummaryQueryDto} data - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/statistics.js').VideoStatisticsSummaryDto>>}
 */
export function getStatisticsSummary (data) {
  return request.post('/Statistics/summary', data)
}

/**
 * 10. 获取仪表板数据
 * @param {import('./types/statistics.js').OverviewParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/statistics.js').VideoDashboardDto>>}
 */
export function getDashboard (params = {}) {
  return request.get('/Statistics/dashboard', params)
}

/**
 * 11. 获取仪表板关键指标汇总
 * @param {import('./types/statistics.js').KeyMetricsParams} params - 查询参数
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/statistics.js').KeyMetricsSummaryDto>>}
 */
export function getDashboardKeyMetrics (params = {}) {
  return request.get('/Statistics/dashboard/key-metrics', params)
}

/**
 * 12. 获取今日数据快照
 * @returns {Promise<import('./types/common.js').ApiResult<import('./types/statistics.js').VideoDashboardDto>>}
 */
export function getTodayDashboard () {
  return request.get('/Statistics/dashboard/today')
}

/**
 * 工具函数：根据用户角色获取适当的统计数据
 * @param {string} userRole - 用户角色 (超管/管理/员工)
 * @param {Object} params - 查询参数
 * @returns {Promise} 统计数据
 */
export async function getStatisticsByRole (userRole, params = {}) {
  switch (userRole) {
    case '超管':
      // 超管可以查看所有数据
      return getStatisticsOverview(params)
    case '管理':
      // 管理员查看自己负责的员工数据
      if (params.employeeId) {
        return getEmployeeSummary(params.employeeId, params)
      }
      return getStatisticsOverview(params)
    case '员工':
      // 员工只能查看自己的数据
      return getMyStatistics(params)
    default:
      throw new Error('未知的用户角色')
  }
}

/**
 * 工具函数：格式化统计数据用于展示
 * @param {Object} data - 原始统计数据
 * @returns {Object} 格式化后的数据
 */
export function formatStatisticsData (data) {
  if (!data) return {}

  return {
    ...data,
    // 将分转换为元
    rewardAmountYuan: data.rewardAmount ? (data.rewardAmount / 100).toFixed(2) : '0.00',
    totalRewardAmountYuan: data.totalRewardAmount ? (data.totalRewardAmount / 100).toFixed(2) : '0.00',
    // 格式化百分比（API已返回百分比格式的数值，无需再乘100）
    completeRate: data.completeRate ? data.completeRate.toFixed(2) + '%' : '0.00%',
    correctRate: data.correctRate ? data.correctRate.toFixed(2) + '%' : '0.00%',
    // 格式化时长（秒转分钟）
    avgViewDurationMinutes: data.avgViewDuration ? Math.round(data.avgViewDuration / 60) : 0
  }
}

// 默认导出所有新的统计相关API
export default {
  // 核心统计API
  getUserDailyStatistics,
  getUserSummary,
  getUsersSummary,
  getEmployeeSummary,
  getDailyTrend,
  getStatisticsOverview,
  getUserStatistics,
  getMyStatistics,
  getStatisticsSummary,
  getDashboard,
  getDashboardKeyMetrics,
  getTodayDashboard,

  // 工具函数
  getStatisticsByRole,
  formatStatisticsData
}
