<template>
    <view class="container">
        <!-- 页面头部 -->
        <u-navbar title="用户审核" :autoBack="true" :placeholder="true"></u-navbar>

        <!-- 使用通用审核列表组件 -->
        <AuditList auditType="user" :listData="userList" :extraFields="extraFields" typeLabel="用户" :loading="loading"
            @reject="handleReject" @approve="handleApprove" @view-detail="viewDetail" />
    </view>
</template>

<script>
import AuditList from '../../../../components/AuditList.vue';
import { getPendingUsers, approveUser, rejectUser } from '../../../../api/user-audit.js';

export default {
    components: {
        AuditList
    },
    data () {
        return {
            userList: [],
            loading: false,
            extraFields: [
                { label: '推荐人', path: 'referrer', defaultValue: '无' },
                { label: '会员类型', path: 'memberType', defaultValue: '普通用户' }
            ]
        }
    },
    onLoad () {
        this.loadUserList();
    },
    methods: {
        async loadUserList () {
            try {
                this.loading = true;

                // 调用用户审核API获取待审核用户列表
                const response = await getPendingUsers();

                if (response.success && response.data) {
                    this.userList = response.data || [];
                } else {
                    this.$u.toast(response.msg || '获取用户列表失败');
                    this.userList = [];
                }
            } catch (error) {
                console.error('获取待审核用户列表失败:', error);
                this.$u.toast('网络错误，请稍后重试');
                this.userList = [];
            } finally {
                this.loading = false;
            }
        },

        // 处理拒绝审核
        async handleReject (data) {
            try {
                const { item, reason } = data;
                // 调用用户审核API拒绝用户
                const response = await rejectUser(item.id, reason || '无');

                if (response.success) {
                    // 从待审核列表中移除
                    const index = this.userList.findIndex(user => user.id === item.id);
                    if (index !== -1) {
                        this.userList.splice(index, 1);
                    }

                    this.$u.toast('已拒绝申请');
                } else {
                    this.$u.toast(response.msg || '操作失败');
                }
            } catch (error) {
                console.error('拒绝审核出错:', error);
                this.$u.toast('网络错误，请稍后重试');
            }
        },

        // 处理通过审核
        async handleApprove (item) {
            try {
                // 调用用户审核API通过用户
                const response = await approveUser(item.id);

                if (response.success) {
                    // 从待审核列表中移除
                    const index = this.userList.findIndex(user => user.id === item.id);
                    if (index !== -1) {
                        this.userList.splice(index, 1);
                    }

                    this.$u.toast('已通过申请');
                } else {
                    this.$u.toast(response.msg || '操作失败');
                }
            } catch (error) {
                console.error('通过审核出错:', error);
                this.$u.toast('网络错误，请稍后重试');
            }
        },

        // 查看详情
        viewDetail (item) {
            uni.navigateTo({
                url: `/pages/admin/users/info?userId=${item.id}&type=user`
            });
        }
    }
}
</script>

<style lang="scss">
.container {
    min-height: 100vh;
    background-color: #f5f5f5;
}
</style>