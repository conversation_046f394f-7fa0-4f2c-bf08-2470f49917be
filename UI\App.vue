<template>
	<view>
		<!-- 全局 Toast 组件 -->
		<u-toast ref="globalToast" />
	</view>
</template>

<script>
export default {
	onLaunch: function () {
		// App Launch
	},
	onReady: function () {
		// 设置全局 toast 引用
		getApp().globalToast = this.$refs.globalToast;
	},
	onShow: function () {
		// App Show
	},
	onHide: function () {
		// App Hide
	}
}
</script>

<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import "uview-plus/index.scss";

/*每个页面公共css */
@import url('/styles/iconfont.css');
</style>
