/**
 * IP用户管理工具
 * 基于IP地址自动生成和管理用户账号
 */

import CryptoJS from 'crypto-js'

/**
 * 获取用户IP地址
 * @returns {Promise<string>} IP地址
 */
export async function getUserIP () {
  try {
    // 尝试通过第三方服务获取IP
    const response = await uni.request({
      url: 'https://api.ipify.org?format=json',
      method: 'GET',
      timeout: 5000
    })

    if (response.statusCode === 200 && response.data && response.data.ip) {
      return response.data.ip
    }
  } catch (error) {
    console.warn('获取IP地址失败，使用备用方案:', error)
  }

  // 备用方案：使用设备信息生成唯一标识
  return generateDeviceId()
}

/**
 * 生成设备唯一标识
 * @returns {string} 设备标识
 */
function generateDeviceId () {
  try {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync()

    // 组合设备信息
    const deviceInfo = [
      systemInfo.platform || 'unknown',
      systemInfo.system || 'unknown',
      systemInfo.model || 'unknown',
      systemInfo.brand || 'unknown',
      systemInfo.screenWidth || '0',
      systemInfo.screenHeight || '0'
    ].join('|')

    // 尝试获取存储的设备ID
    let deviceId = uni.getStorageSync('deviceId')
    if (!deviceId) {
      // 生成新的设备ID
      const timestamp = Date.now().toString()
      const random = Math.random().toString(36).substring(2)
      deviceId = `${deviceInfo}|${timestamp}|${random}`

      // 保存设备ID
      uni.setStorageSync('deviceId', deviceId)
    }

    return deviceId
  } catch (error) {
    console.error('生成设备ID失败:', error)
    // 最后的备用方案
    return `fallback_${Date.now()}_${Math.random().toString(36).substring(2)}`
  }
}

/**
 * 将IP/设备ID转换为数字用户ID
 * @param {string} input 输入字符串
 * @returns {number} 数字用户ID
 */
export function generateUserHash (input) {
  try {
    // 生成MD5哈希
    const md5Hash = CryptoJS.MD5(input).toString()

    // 将MD5哈希转换为数字ID
    // 取MD5的前8位，转换为16进制数字，然后转为10进制
    const hexPart = md5Hash.substring(0, 8)
    const numericId = parseInt(hexPart, 16)

    // 确保是正数，并且在合理范围内（避免与现有用户ID冲突）
    // 使用负数范围来标识IP用户：-2147483648 到 -1
    return -(Math.abs(numericId) % 2147483647 + 1)
  } catch (error) {
    console.error('生成用户ID失败:', error)
    // 简单的哈希备用方案
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    // 返回负数以标识IP用户
    return -(Math.abs(hash) % 2147483647 + 1)
  }
}

/**
 * 获取或创建IP用户
 * @returns {Promise<Object>} 用户信息
 */
export async function getOrCreateIPUser () {
  try {
    // 检查是否已有缓存的用户信息
    const cachedUser = uni.getStorageSync('ipUser')
    if (cachedUser && cachedUser.userHash && cachedUser.userInfo) {
      console.log('使用缓存的IP用户:', cachedUser.userHash)
      return cachedUser
    }

    // 获取IP或设备标识
    const ipOrDeviceId = await getUserIP()
    console.log('获取到IP/设备ID:', ipOrDeviceId)

    // 生成用户哈希
    const userHash = generateUserHash(ipOrDeviceId)
    console.log('生成用户哈希:', userHash)

    // 生成用户信息
    const userInfo = {
      id: String(userHash), // 确保ID是字符串类型
      userId: String(userHash), // 确保userId是字符串类型
      nickname: `访客_${Math.abs(userHash).toString().substring(0, 8)}`, // 使用绝对值生成昵称
      avatar: '/static/logo.png', // 使用项目logo作为默认头像
      ipAddress: ipOrDeviceId,
      createTime: new Date().toISOString(),
      userType: 'ip_user' // 标识为IP用户
    }

    // 缓存用户信息
    const ipUser = {
      userHash: String(userHash), // 确保userHash是字符串类型
      userInfo,
      ipAddress: ipOrDeviceId,
      createTime: new Date().toISOString()
    }

    uni.setStorageSync('ipUser', ipUser)
    uni.setStorageSync('userInfo', userInfo) // 兼容现有代码

    console.log('创建IP用户成功:', userInfo)
    return ipUser

  } catch (error) {
    console.error('获取或创建IP用户失败:', error)

    // 创建临时用户
    const fallbackHash = generateUserHash(`fallback_${Date.now()}`)
    const fallbackUser = {
      userHash: String(fallbackHash), // 确保userHash是字符串类型
      userInfo: {
        id: String(fallbackHash), // 确保ID是字符串类型
        userId: String(fallbackHash), // 确保userId是字符串类型
        nickname: `临时用户_${Math.abs(fallbackHash).toString().substring(0, 8)}`, // 使用绝对值生成昵称
        avatar: '/static/logo.png',
        userType: 'temp_user'
      },
      createTime: new Date().toISOString()
    }

    uni.setStorageSync('ipUser', fallbackUser)
    uni.setStorageSync('userInfo', fallbackUser.userInfo)

    return fallbackUser
  }
}

/**
 * 获取当前IP用户ID
 * @returns {string|null} 用户ID
 */
export function getCurrentIPUserId () {
  try {
    const ipUser = uni.getStorageSync('ipUser')
    if (ipUser && ipUser.userHash) {
      return String(ipUser.userHash) // 确保返回字符串类型
    }

    const userInfo = uni.getStorageSync('userInfo')
    if (userInfo && userInfo.id) {
      return String(userInfo.id) // 确保返回字符串类型
    }

    return null
  } catch (error) {
    console.error('获取当前IP用户ID失败:', error)
    return null
  }
}

/**
 * 清除IP用户缓存
 */
export function clearIPUserCache () {
  try {
    uni.removeStorageSync('ipUser')
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('deviceId')
    console.log('IP用户缓存已清除')
  } catch (error) {
    console.error('清除IP用户缓存失败:', error)
  }
}

/**
 * 获取IP用户显示信息
 * @returns {Object} 用户显示信息
 */
export function getIPUserDisplayInfo () {
  try {
    const ipUser = uni.getStorageSync('ipUser')
    if (ipUser && ipUser.userInfo) {
      return {
        userId: ipUser.userInfo.id,
        nickname: ipUser.userInfo.nickname,
        avatar: ipUser.userInfo.avatar,
        userType: ipUser.userInfo.userType || 'ip_user',
        ipAddress: ipUser.ipAddress,
        createTime: ipUser.createTime
      }
    }

    return {
      userId: null,
      nickname: '未知用户',
      avatar: '/static/logo.png',
      userType: 'unknown'
    }
  } catch (error) {
    console.error('获取IP用户显示信息失败:', error)
    return {
      userId: null,
      nickname: '错误用户',
      avatar: '/static/logo.png',
      userType: 'error'
    }
  }
}
