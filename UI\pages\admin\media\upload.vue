<template>
  <view class="container">

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 步骤指示器 -->
      <view class="steps-container">
        <view class="steps">
          <view v-for="(step, index) in steps" :key="index"
            :class="['step-item', currentStep >= index ? 'active' : '']">
            <view class="step-number">{{ index + 1 }}</view>
            <view class="step-title">{{ step }}</view>
            <view class="step-line" v-if="index < steps.length - 1"></view>
          </view>
        </view>

      </view>

      <!-- 步骤1：上传视频 -->
      <view class="step-content" v-if="currentStep === 0">
        <view class="content-section">


          <!-- 视频上传组件 -->
          <VideoUploader :value="videoInfo" :is-edit-mode="isEditMode" @input="onVideoInfoChange" />

          <!-- 视频基本信息 -->
          <view class="form-group">
            <view class="form-item">
              <text class="form-label required">视频标题</text>
              <input type="text" class="modern-input" placeholder="请输入视频标题" v-model="videoInfo.title" />
            </view>

            <view class="form-item">
              <text class="form-label">视频描述</text>
              <textarea class="modern-textarea" placeholder="请输入视频描述（可选）" v-model="videoInfo.description"></textarea>
            </view>
          </view>
        </view>


        <view class="form-actions">
          <button class="btn btn-primary" :class="{ 'btn-disabled': !canGoNext }" @tap="nextStep"
            :disabled="!canGoNext">
            下一步
          </button>
        </view>
      </view>

      <!-- 步骤2：添加问题 -->
      <view class="step-content" v-if="currentStep === 1">
        <view class="content-section">


          <view class="question-actions">
            <button class="add-question-btn" @tap="addQuestion">
              <text class="btn-icon">+</text>
              <text class="btn-text">添加问题</text>
            </button>
          </view>

          <view class="empty-list" v-if="videoInfo.questions.length === 0">
            <text class="empty-icon iconfont icon-empty"></text>
            <text class="empty-text">暂无问题，请点击"添加问题"按钮添加</text>
          </view>

          <view class="question-list" v-else>
            <view class="question-item" v-for="(question, qIndex) in videoInfo.questions" :key="qIndex">
              <view class="question-header">
                <text class="question-number">问题 {{ qIndex + 1 }}</text>
                <text class="question-delete" @tap="removeQuestion(qIndex)">删除</text>
              </view>

              <view class="form-item">
                <text class="form-label required">问题内容</text>
                <input type="text" class="form-input" placeholder="请输入问题内容" v-model="question.content" />
              </view>

              <view class="form-item">
                <view class="option-header">
                  <text class="form-label required">选项</text>
                  <text class="option-action" @tap="addOption(qIndex)">+ 添加选项</text>
                </view>
                <view class="option-list">
                  <view class="option-item" v-for="(option, oIndex) in question.options" :key="oIndex">
                    <text class="option-marker">{{
                      String.fromCharCode(65 + oIndex)
                    }}</text>
                    <input type="text" class="option-input" placeholder="请输入选项内容" v-model="option.text" />
                    <radio :checked="question.correctAnswer === oIndex" @tap="setCorrectAnswer(qIndex, oIndex)">
                    </radio>
                    <text class="option-delete" v-if="question.options.length > 2"
                      @tap="removeOption(qIndex, oIndex)">删除</text>
                  </view>
                </view>
              </view>




            </view>
          </view>

          <!-- 红包设置 -->
          <view class="reward-section">


            <view class="reward-input-group">
              <view class="form-item">
                <text class="form-label required">红包金额（元）</text>
                <input type="digit" class="modern-input reward-input" placeholder="请输入红包金额"
                  v-model="videoInfo.totalReward" />
                <text class="form-hint">用户答对问题后可获得的红包金额</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 第二步的表单操作 -->
        <view class="form-actions">
          <button class="btn btn-secondary" @tap="prevStep">上一步</button>
          <button class="btn btn-primary" :class="{ 'btn-disabled': isUploading || !canGoNext }" @tap="submitUpload"
            :disabled="isUploading || !canGoNext">
            {{ isUploading ? "上传中..." : (isEditMode ? "保存修改" : "完成上传") }}
          </button>
        </view>
      </view>
    </view>

    <!-- 上传进度条 -->
    <view class="upload-progress-container" v-if="isUploading">
      <!-- 遮罩层 -->
      <view class="progress-mask"></view>
      <!-- 进度卡片 -->
      <view class="progress-card">
        <view class="progress-header">
          <text class="progress-title">正在上传视频</text>
          <text class="progress-percent">{{ uploadProgress }}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
        </view>
        <view class="progress-info">
          <text class="progress-text">请耐心等待，上传完成后会自动提示</text>
          <text class="progress-warning">⚠️ 请勿离开页面或锁屏，以免上传中断</text>
          <text class="progress-tip">💡 建议保持网络连接稳定</text>
        </view>
      </view>
    </view>

    <!-- 压缩进度组件 -->
    <CompressionProgress :visible="showCompressionProgress" :fileId="compressionFileId"
      @close="onCompressionProgressClose" @retry="onCompressionRetry" />

  </view>
</template>

<script>


import VideoUploader from "@/components/VideoUploader.vue";
import CompressionProgress from "@/components/CompressionProgress.vue";
import { getVideoDetail, updateVideo } from "@/api/video.js";
import { videoUploader } from "@/utils/video-upload.js";
import { videoFormValidator } from "@/utils/form-validator.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
  mixins: [mediaCommonMixin],
  components: {
    VideoUploader,
    CompressionProgress
  },
  data () {
    return {
      steps: ["视频信息", "添加问题"],
      currentStep: 0,
      videoInfo: {
        path: "",
        thumbnail: "",
        duration: "",
        title: "",
        description: "",
        questions: [
          {
            content: "",
            options: [{ text: "" }, { text: "" }, { text: "" }],
            correctAnswer: 0,
          }
        ],
        totalReward: "1.00",
        // 新增的视频详细信息
        fileSize: 0,
        resolution: "",
        format: "",
        bitrate: "",
        fps: "",
      },
      isUploading: false,
      uploadProgress: 0, // 上传进度百分比
      isEditMode: false,
      videoId: null,
      originalVideoData: null, // 保存原始视频数据
      // 压缩进度相关
      showCompressionProgress: false,
      compressionFileId: ''
    };
  },
  computed: {
    canGoNext () {
      // 第一步验证
      if (this.currentStep === 0) {
        // 非编辑模式需要选择视频文件
        if (!this.isEditMode && !this.videoInfo.path) {
          return false;
        }
        // 必须填写视频标题
        if (!this.videoInfo.title || !this.videoInfo.title.trim()) {
          return false;
        }
        // 非编辑模式必须选择封面
        if (!this.isEditMode && !this.videoInfo.thumbnail) {
          return false;
        }
        return true;
      }
      // 第二步验证
      else if (this.currentStep === 1) {
        // 验证红包金额
        if (!this.videoInfo.totalReward || parseFloat(this.videoInfo.totalReward) <= 0) {
          return false;
        }
        // 验证问题
        if (!this.videoInfo.questions || this.videoInfo.questions.length === 0) {
          return false;
        }
        // 验证每个问题的完整性
        for (let question of this.videoInfo.questions) {
          if (!question.content || !question.content.trim()) {
            return false;
          }
          if (!question.options || question.options.length < 2) {
            return false;
          }
          for (let option of question.options) {
            if (!option.text || !option.text.trim()) {
              return false;
            }
          }
        }
        return true;
      }
      return true;
    },
  },
  onLoad (options) {
    // 编辑模式
    if (options.id) {
      this.isEditMode = true;
      this.videoId = parseInt(options.id);
      this.loadVideoData();
    } else {
      // 新建模式，确保从第一步开始
      this.currentStep = 0;
    }
  },
  onShow () {
    // 每次显示页面时，如果不是编辑模式，确保从第一步开始
    if (!this.isEditMode) {
      this.currentStep = 0;
    }
  },
  methods: {
    // 监听子组件数据变化
    onVideoInfoChange (newVideoInfo) {
      this.videoInfo = { ...newVideoInfo };
    },

    async loadVideoData () {
      try {
        uni.showLoading({
          title: "加载视频信息...",
        });

        // 调用API获取视频详情
        const response = await getVideoDetail(this.videoId);

        if (response.success && response.data) {
          const video = response.data;

          // 保存原始视频数据
          this.originalVideoData = video;

          // 填充视频基本信息
          this.videoInfo = {
            path: video.videoUrl || '', // 编辑模式下显示现有视频路径
            thumbnail: video.coverUrl || '/assets/images/video-cover.jpg',
            duration: this.formatDuration(video.duration || 0),
            title: video.title || '',
            description: video.description || '',
            questions: this.processQuestionsForEdit(video.questions || []),
            totalReward: (video.rewardAmount || 0).toString(),
          };

          console.log('加载的视频数据:', this.videoInfo);
        } else {
          throw new Error(response.msg || '获取视频详情失败');
        }

        uni.hideLoading();
      } catch (error) {
        console.error('加载视频数据失败:', error);
        uni.hideLoading();

        uni.showToast({
          title: '加载视频信息失败',
          icon: 'none'
        });

        setTimeout(() => {
          this.goBack();
        }, 1500);
      }
    },
    // 处理API返回的问题数据，转换为编辑表单需要的格式
    processQuestionsForEdit (apiQuestions) {
      if (!apiQuestions || apiQuestions.length === 0) {
        // 如果没有问题，返回一个默认问题
        return [
          {
            content: "",
            options: [{ text: "" }, { text: "" }, { text: "" }],
            correctAnswer: 0,
          }
        ];
      }

      return apiQuestions.map(q => {
        // 处理选项数据
        const options = (q.options || []).map(option => ({
          text: option.optionText || option.text || ''
        }));

        // 确保至少有3个选项（如果原数据少于3个，补充到3个）
        while (options.length < 3) {
          options.push({ text: '' });
        }

        // 找到正确答案的索引
        let correctAnswer = 0;
        if (q.options && q.options.length > 0) {
          const correctOption = q.options.find(opt => opt.isCorrect);
          if (correctOption) {
            correctAnswer = q.options.indexOf(correctOption);
          }
        }

        return {
          content: q.questionText || q.question || '',
          options: options,
          correctAnswer: correctAnswer
        };
      });
    },

    // 问题管理方法
    addQuestion () {
      this.videoInfo.questions.push({
        content: "",
        options: [{ text: "" }, { text: "" }, { text: "" }],
        correctAnswer: 0,
      });
    },
    removeQuestion (index) {
      uni.showModal({
        title: "删除确认",
        content: "确定要删除这个问题吗？",
        success: (res) => {
          if (res.confirm) {
            this.videoInfo.questions.splice(index, 1);
          }
        },
      });
    },
    setCorrectAnswer (questionIndex, optionIndex) {
      this.videoInfo.questions[questionIndex].correctAnswer = optionIndex;
    },
    addOption (questionIndex) {
      this.videoInfo.questions[questionIndex].options.push({ text: "" });
    },
    removeOption (questionIndex, optionIndex) {
      const question = this.videoInfo.questions[questionIndex];

      // 确保至少保留2个选项
      if (question.options.length <= 2) {
        uni.showToast({
          title: "至少需要2个选项",
          icon: "none",
        });
        return;
      }

      // 如果删除的是正确答案，将正确答案重置为第一个选项
      if (question.correctAnswer === optionIndex) {
        question.correctAnswer = 0;
      } else if (question.correctAnswer > optionIndex) {
        // 如果正确答案在被删除选项之后，需要调整索引
        question.correctAnswer--;
      }

      // 删除选项
      question.options.splice(optionIndex, 1);
    },
    nextStep () {
      // 如果按钮被禁用，直接返回
      if (!this.canGoNext) {
        return;
      }

      // 验证当前步骤
      if (this.currentStep === 0) {
        if (!this.isEditMode && !this.videoInfo.path) {
          uni.showToast({
            title: "请选择视频文件",
            icon: "none",
          });
          return;
        }

        if (!this.videoInfo.title || !this.videoInfo.title.trim()) {
          uni.showToast({
            title: "请输入视频标题",
            icon: "none",
          });
          return;
        }

        if (!this.isEditMode && !this.videoInfo.thumbnail) {
          uni.showToast({
            title: "请选择视频封面",
            icon: "none",
          });
          return;
        }
      } else if (this.currentStep === 1) {
        // 验证红包金额
        if (!this.videoInfo.totalReward || parseFloat(this.videoInfo.totalReward) <= 0) {
          uni.showToast({
            title: "请输入有效的红包金额",
            icon: "none",
          });
          return;
        }

        // 验证问题
        if (this.videoInfo.questions.length === 0) {
          uni.showToast({
            title: "请至少添加一个问题",
            icon: "none",
          });
          return;
        }

        // 验证每个问题的必填项
        for (let i = 0; i < this.videoInfo.questions.length; i++) {
          const question = this.videoInfo.questions[i];
          if (!question.content || !question.content.trim()) {
            uni.showToast({
              title: `问题${i + 1}：请输入问题内容`,
              icon: "none",
            });
            return;
          }

          // 验证选项数量
          if (question.options.length < 2) {
            uni.showToast({
              title: `问题${i + 1}：至少需要2个选项`,
              icon: "none",
            });
            return;
          }

          // 验证选项内容
          for (let j = 0; j < question.options.length; j++) {
            if (!question.options[j].text || !question.options[j].text.trim()) {
              uni.showToast({
                title: `问题${i + 1}：请输入选项${String.fromCharCode(65 + j)}内容`,
                icon: "none",
              });
              return;
            }
          }
        }
      }

      // 进入下一步
      if (this.currentStep < 1) {
        this.currentStep++;
      }
    },
    prevStep () {
      if (this.currentStep > 0) {
        this.currentStep--;
      }
    },
    // 验证表单
    validateForm () {
      const result = videoFormValidator.validateVideoForm(this.videoInfo, this.isEditMode);
      if (!result.valid) {
        this.showError(result.message);
        return false;
      }
      return true;
    },

    submitUpload () {
      // 验证必填项
      if (!this.validateForm()) {
        return;
      }

      // 显示上传中提示
      uni.showLoading({
        title: this.isEditMode ? "保存中..." : "上传中...",
        mask: true,
      });

      // 根据模式调用不同的方法
      if (this.isEditMode) {
        uni.hideLoading();
        this.updateVideo();
      } else {
        // 模拟上传延迟
        // 直接调用上传方法，不需要延迟
        this.createVideo();
      }
    },
    async createVideo () {
      try {
        console.log('Creating video with data:', this.videoInfo);

        // 显示上传进度
        uni.showLoading({
          title: "准备上传...",
          mask: true,
        });

        // 验证视频文件路径
        if (!this.videoInfo.path) {
          throw new Error('请先选择视频文件');
        }

        // 验证封面图片（必填）
        if (!this.videoInfo.thumbnail) {
          throw new Error('请选择视频封面图片');
        }

        // 验证必填字段
        if (!this.videoInfo.title || !this.videoInfo.title.trim()) {
          throw new Error('请输入视频标题');
        }

        const rewardAmount = parseFloat(this.videoInfo.totalReward);
        if (!this.videoInfo.totalReward || isNaN(rewardAmount) || rewardAmount <= 0) {
          throw new Error('请输入有效的红包金额');
        }

        if (!this.videoInfo.questions || this.videoInfo.questions.length === 0) {
          throw new Error('请至少添加一个问题');
        }

        // 验证问题完整性
        for (let i = 0; i < this.videoInfo.questions.length; i++) {
          const question = this.videoInfo.questions[i];
          if (!question.content || !question.content.trim()) {
            throw new Error(`问题${i + 1}：请输入问题内容`);
          }
          if (!question.options || question.options.length < 2) {
            throw new Error(`问题${i + 1}：至少需要2个选项`);
          }
          for (let j = 0; j < question.options.length; j++) {
            if (!question.options[j].text || !question.options[j].text.trim()) {
              throw new Error(`问题${i + 1}：请填写选项${String.fromCharCode(65 + j)}`);
            }
          }
        }

        // 初始化上传状态
        this.isUploading = true;
        this.uploadProgress = 0;

        // 隐藏loading，显示进度条
        uni.hideLoading();

        // 打印完整的上传数据用于调试
        console.log('=== 上传数据详情 ===');
        console.log('视频路径:', this.videoInfo.path);
        console.log('封面路径:', this.videoInfo.thumbnail);
        console.log('标题:', this.videoInfo.title);
        console.log('描述:', this.videoInfo.description);
        console.log('红包金额:', this.videoInfo.totalReward, '类型:', typeof this.videoInfo.totalReward);
        console.log('问题数据:', JSON.stringify(this.videoInfo.questions, null, 2));
        console.log('==================');

        // 同时上传视频和封面到同一个接口
        console.log('开始同时上传视频和封面...');
        await this.performUpload();

      } catch (error) {
        console.error('视频上传失败:', error);
        uni.hideLoading();

        // 重置上传状态
        this.isUploading = false;
        this.uploadProgress = 0;

        this.showDetailedError('上传失败', error.message || '未知错误，请重试');
      }
    },

    // 判断是否为自定义封面（非默认生成的封面）
    isCustomCover (thumbnailPath) {
      if (!thumbnailPath) return false;
      // 如果是blob URL或者临时文件路径，说明是用户选择的自定义封面
      return thumbnailPath.startsWith('blob:') ||
        thumbnailPath.startsWith('file://') ||
        thumbnailPath.includes('temp') ||
        thumbnailPath.includes('tmp');
    },

    // 执行上传
    async performUpload () {
      try {
        const result = await videoUploader.uploadVideoAndCover(
          this.videoInfo,
          (progress) => {
            this.uploadProgress = progress;
          }
        );

        // 检查是否启用了压缩
        if (this.videoInfo.enableCompression && result.fileId) {
          this.compressionFileId = result.fileId;
          this.showCompressionProgress = true;
          this.showSuccess("上传成功，开始压缩");
        } else {
          this.showSuccess("上传成功");
          setTimeout(() => {
            this.showUploadSuccessOptions();
          }, 2000);
        }

        return result;
      } catch (error) {
        console.error('上传失败:', error);
        throw error;
      }
    },






    async updateVideo () {
      try {
        // 构建更新数据，按照API文档要求的格式
        const updateData = {
          title: this.videoInfo.title,
          description: this.videoInfo.description,
          coverUrl: this.originalVideoData?.coverUrl || this.videoInfo.thumbnail || '',
          videoUrl: this.originalVideoData?.videoUrl || this.videoInfo.path || '',
          duration: this.originalVideoData?.duration || this.parseDurationToSeconds(this.videoInfo.duration) || 0,
          rewardAmount: parseFloat(this.videoInfo.totalReward) || 0,
          viewCount: this.originalVideoData?.viewCount || 0, // 保持原有观看次数
          questions: videoUploader.buildQuestionsForApi(this.videoInfo.questions)
        };

        console.log('更新视频数据:', JSON.stringify(updateData, null, 2));

        // 调用API更新视频
        const response = await updateVideo(this.videoId, updateData);

        if (response.success) {
          uni.showToast({
            title: "保存成功",
            icon: "success",
          });

          // 延迟返回到详情页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(response.msg || '保存失败');
        }
      } catch (error) {
        console.error('更新视频失败:', error);
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      }
    },


    // 将时长字符串转换为秒数
    parseDurationToSeconds (duration) {
      if (!duration) return 0;
      const parts = duration.split(':');
      if (parts.length === 2) {
        return parseInt(parts[0]) * 60 + parseInt(parts[1]);
      }
      return 0;
    },


    // 从路径获取封面文件
    async getCoverFileFromPath (filePath) {
      try {
        if (!filePath) return null;

        return {
          path: filePath,
          name: `cover_${Date.now()}.jpg`,
          type: 'image/jpeg'
        };
      } catch (error) {
        console.error('获取封面文件失败:', error);
        return null;
      }
    },

    // getCurrentUserId 方法已移除，CreatorId 现在由后端从Token中获取



    // 显示详细错误信息
    showDetailedError (title, message) {
      console.error(`${title}:`, message);

      // 重置上传状态
      this.isUploading = false;
      this.uploadProgress = 0;

      this.showError(message);
    },

    async goBack () {
      const confirmed = await this.showConfirm(
        "提示",
        this.isEditMode
          ? "确定要放弃当前修改并返回吗？"
          : "确定要放弃当前编辑并返回吗？"
      );

      if (confirmed) {
        this.safeNavigateBack();
      }
    },

    // 显示上传成功后的选择对话框
    showUploadSuccessOptions () {
      // 重置上传状态
      this.isUploading = false;
      this.uploadProgress = 0;

      uni.showModal({
        title: "上传成功",
        content: "视频上传成功！您希望继续上传新视频还是返回视频列表？",
        confirmText: "返回列表",
        cancelText: "继续上传",
        success: (res) => {
          if (res.confirm) {
            // 返回列表并刷新
            this.goBackToListWithRefresh();
          } else {
            // 继续上传，重置表单
            this.resetFormForNewUpload();
          }
        }
      });
    },

    // 返回列表并刷新
    goBackToListWithRefresh () {
      // 通过事件总线通知列表页刷新
      uni.$emit('refreshVideoList');

      // 使用 reLaunch 重新启动到媒体库首页
      uni.reLaunch({
        url: '/pages/admin/media/index',
        fail: (err) => {
          console.error('重新启动失败:', err);
          this.safeNavigateBack();
        }
      });
    },

    // 重置表单以便继续上传
    resetFormForNewUpload () {
      // 重置所有表单数据
      this.videoInfo = {
        title: '',
        description: '',
        rewardAmount: 0.01,
        questions: [
          {
            content: '',
            options: [
              { text: '' },
              { text: '' },
              { text: '' }
            ],
            correctAnswer: 0
          }
        ],
        fileSize: 0,
        duration: '',
        format: '',
        resolution: ''
      };

      // 重置步骤
      this.currentStep = 0;

      // 重置上传状态
      this.isUploading = false;
      this.uploadProgress = 0;

      uni.showToast({
        title: "已重置，可以上传新视频",
        icon: "success"
      });
    },

    // 压缩进度相关方法
    onCompressionProgressClose () {
      this.showCompressionProgress = false;
      // 压缩完成后显示成功选项
      setTimeout(() => {
        this.showUploadSuccessOptions();
      }, 500);
    },

    onCompressionRetry (fileId) {
      console.log('重试压缩:', fileId);
      // 这里可以调用重试压缩的API
      uni.showToast({
        title: "重新开始压缩",
        icon: "success"
      });
    },
  },
};
</script>

<style lang="scss">
/* 变量已通过uni.scss全局导入 */

// 使用全局变量，删除重复定义

.container {
  background: linear-gradient(135deg, $gray-50 0%, $white 100%);
  min-height: 100vh;
  padding-bottom: 140rpx;
}

/* 现代化表单区域 */
.form-container {
  padding: 0 32rpx;
  margin-top: 0;
}

/* 极简步骤指示器 */
.steps-container {
  background: transparent;
  padding: 40rpx 0 32rpx 0;
  margin-bottom: 32rpx;
}

.steps {
  display: flex;
  justify-content: center;
  position: relative;
  align-items: center;
  max-width: 600rpx;
  margin: 0 auto;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: $gray-200;
  color: $gray-500;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 3rpx solid transparent;
}

.step-item.active .step-number {
  background: $primary-color;
  color: $white;
  transform: scale(1.15);
  border-color: rgba(24, 107, 255, 0.2);
  box-shadow: 0 0 0 8rpx rgba(24, 107, 255, 0.1);
}

.step-title {
  font-size: 24rpx;
  color: $gray-500;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
}

.step-item.active .step-title {
  color: $primary-color;
  font-weight: $font-weight-bold;
  transform: translateY(-2rpx);
}

.step-line {
  position: absolute;
  top: 24rpx;
  left: calc(50% + 24rpx);
  right: calc(50% - 24rpx);
  height: 2rpx;
  background: $gray-200;
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-item.active .step-line {
  background: $primary-color;
}

/* 现代化内容区域 */
.step-content {
  margin-bottom: 48rpx;
}

.content-section {
  background: transparent;
  padding: 0;
  margin-bottom: 32rpx;
}

.section-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 24rpx 0;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: $gray-900;
  margin-bottom: 12rpx;
  display: block;
}

.section-subtitle {
  font-size: 28rpx;
  color: $gray-500;
  display: block;
  line-height: 1.6;
}

.form-group {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid $gray-200;
  margin-top: 24rpx;
}

.modern-input {
  width: 100%;
  height: 96rpx;
  padding: 0 24rpx;
  border: 2rpx solid $gray-200;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: $white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 4rpx rgba(24, 107, 255, 0.1);
    outline: none;
  }

  &::placeholder {
    color: $gray-400;
  }
}

.modern-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: $spacing-lg;
  border: 2rpx solid $gray-200;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  background: $white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  resize: none;
  box-sizing: border-box;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 4rpx rgba(24, 107, 255, 0.1);
    outline: none;
  }

  &::placeholder {
    color: $gray-400;
  }
}

.form-label {
  font-size: 28rpx;
  color: $gray-700;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;

  &.required::after {
    content: ' *';
    color: $error-color;
    font-weight: bold;
  }
}

.form-item {
  margin-bottom: 0rpx;
  padding: 0.75rem 0.75rem 0px 0.75rem;
}

.form-hint {
  font-size: $font-size-sm;
  color: $gray-500;
  margin-top: $spacing-sm;
  display: block;
  line-height: $line-height-relaxed;
}

/* 视频上传区域样式已移至VideoUploader组件中 */

/* 上传相关样式已移至VideoUploader组件中 */

/* 上传预览样式已移至VideoUploader组件中 */

/* 预览遮罩样式已移至VideoUploader组件中 */

/* 预览操作按钮样式已移至VideoUploader组件中 */

/* 视频信息面板 - 使用全局卡片样式并增强 */
.video-info-panel {
  @extend .card;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 2rpx solid rgba(148, 163, 184, 0.2);
  transition: all $transition-base ease;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-md;
  border-bottom: 2rpx solid rgba(148, 163, 184, 0.1);
}

.panel-title {
  @extend .section-title;
  margin: 0;
}

.panel-status {
  font-size: $font-size-sm;
  padding: $spacing-xs $spacing-md;
  border-radius: $border-radius-base;
  font-weight: $font-weight-semibold;
  transition: all $transition-base ease;
}

.panel-status.success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border: 1rpx solid #c3e6cb;
  box-shadow: 0 2rpx 8rpx rgba(21, 87, 36, 0.1);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-lg;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
  padding: $spacing-md;
  background: rgba(248, 250, 252, 0.5);
  border-radius: $border-radius-base;
  transition: all $transition-base ease;

  &:hover {
    background: rgba(240, 248, 255, 0.7);
  }
}

.info-label {
  font-size: $font-size-sm;
  color: $text-tertiary;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.5rpx;
}

.info-value {
  font-size: $font-size-lg;
  color: $text-primary;
  font-weight: $font-weight-bold;
}

.info-value.status-good {
  color: $success-color;
  text-shadow: 0 1rpx 2rpx rgba(82, 196, 26, 0.2);
}

/* 选择器内容样式 */
.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: $input-height;
  border: 2rpx solid $border-primary;
  border-radius: $border-radius-base;
  padding: 0 $input-padding;
  font-size: $font-size-base;
  box-sizing: border-box;
  background: $bg-primary;
  transition: all $transition-base ease;

  &:hover {
    border-color: $primary-color;
    box-shadow: 0 0 0 4rpx rgba(24, 107, 255, 0.1);
  }
}

/* 现代化空列表提示 */
.empty-list {
  padding: 80rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.5) 0%, rgba(241, 245, 249, 0.3) 100%);
  border-radius: $border-radius-lg;
  margin: $spacing-lg 0;
}

.empty-icon {
  font-size: 120rpx;
  color: rgba(148, 163, 184, 0.6);
  margin-bottom: $spacing-lg;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.empty-text {
  font-size: $font-size-lg;
  color: $text-secondary;
  text-align: center;
  line-height: $line-height-relaxed;
  max-width: 400rpx;
}

/* 现代化问题操作区 */
.question-actions {
  display: flex;
  justify-content: center;
  margin-bottom: $spacing-xl;
}

.add-question-btn {
  background: $primary-color;
  color: $white !important;
  border: none;
  border-radius: $border-radius-lg;
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(24, 107, 255, 0.3);

  &:hover {
    background: $primary-dark;
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(24, 107, 255, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-icon {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
}

.btn-text {
  font-size: $font-size-base;
  color: $white !important;
}

/* 现代化问题列表 */
.question-item {
  background: $white;
  border-radius: $border-radius-lg;
  margin-bottom: $spacing-xl;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid $gray-200;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(24, 107, 255, 0.15);
    border-color: $primary-color;
  }
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(24, 107, 255, 0.05);
  border-bottom: 1rpx solid $gray-200;
}

.question-number {
  font-size: $font-size-base;
  font-weight: $font-weight-bold;
  color: $primary-color;
  background: rgba(24, 107, 255, 0.1);
  padding: $spacing-xs $spacing-md;
  border-radius: $border-radius-base;
}

.question-delete {
  font-size: $font-size-sm;
  color: $error-color;
  font-weight: $font-weight-medium;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: rgba(239, 68, 68, 0.1);
    color: darken($error-color, 10%);
  }
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
}

.option-action {
  font-size: $font-size-base;
  color: $primary-color;
  font-weight: $font-weight-medium;
  transition: all $transition-base ease;

  &:hover {
    color: $primary-dark;
    transform: translateX(4rpx);
  }
}

.option-list {
  margin-top: $spacing-md;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 0.375rem 0;
  border-radius: $border-radius-base;
  transition: all $transition-base ease;

  &:hover {
    background: rgba(248, 250, 252, 0.5);
  }
}

.option-marker {
  width: 56rpx;
  height: 56rpx;
  border-radius: $border-radius-round;
  background: linear-gradient(135deg, $gray-200 0%, #e2e8f0 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: $font-size-base;
  font-weight: $font-weight-bold;
  color: $text-secondary;
  margin-right: $spacing-md;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all $transition-base ease;
}

.option-input {
  flex: 1;
  height: 72rpx;
  border: 2rpx solid $border-primary;
  border-radius: $border-radius-base;
  padding: 0 $spacing-lg;
  font-size: $font-size-base;
  margin-right: $spacing-md;
  background: $bg-primary;
  transition: all $transition-base ease;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 4rpx rgba(24, 107, 255, 0.1);
  }
}

.option-delete {
  font-size: $font-size-base;
  color: $error-color;
  // margin-left: $spacing-sm;
  min-width: 80rpx;
  text-align: center;
  font-weight: $font-weight-medium;
  transition: all $transition-base ease;

  &:hover {
    color: darken($error-color, 10%);
    transform: scale(1.05);
  }
}

/* 现代化红包设置样式 */
.reward-section {
  background: linear-gradient(135deg, #fee2e2 0%, #fef2f2 100%);
  border-radius: $border-radius-lg;
  padding: 0.75rem;
  margin-top: $spacing-lg;
  border: 1rpx solid #ef4444;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #dc2626 0%, #ef4444 50%, #f87171 100%);
  }
}



.reward-input-group {
  background: white;
  border-radius: $border-radius-base;
  padding: $spacing-lg;
  margin-top: $spacing-md;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.1);
}

.reward-input {
  border: 2rpx solid #ef4444;
  background: white;
  color: #dc2626;
  font-weight: $font-weight-semibold;
  font-size: $font-size-lg;
  text-align: center;

  &:focus {
    border-color: #dc2626;
    box-shadow: 0 0 0 4rpx rgba(220, 38, 38, 0.2);
    background: white;
  }

  &::placeholder {
    color: #f87171;
    opacity: 0.7;
  }
}





.form-hint {
  font-size: $font-size-base;
  color: $text-secondary;
  margin-top: $spacing-sm;
  display: block;
  font-style: italic;
  line-height: $line-height-relaxed;
}

/* 现代化表单操作区 */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 -4rpx 24rpx rgba(24, 107, 255, 0.1);
  border-top: 1rpx solid $gray-200;
  z-index: 1;
}

.form-actions .btn {
  min-width: 200rpx;
  height: 88rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 12rpx;
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.btn-primary {
    background: $primary-color;
    color: $white;
    box-shadow: 0 4rpx 12rpx rgba(24, 107, 255, 0.3);

    &:hover {
      background: $primary-dark;
      transform: translateY(-2rpx);
      box-shadow: 0 8rpx 24rpx rgba(24, 107, 255, 0.4);
    }

    &:disabled,
    &.btn-disabled {
      background: #d1d5db !important;
      color: #9ca3af !important;
      box-shadow: none !important;
      cursor: not-allowed !important;
      transform: none !important;
      opacity: 0.6 !important;
      pointer-events: none;
    }
  }

  &.btn-secondary {
    background: $white;
    color: $gray-700;
    border: 2rpx solid $gray-300;

    &:hover {
      background: rgba(240, 248, 255, 1);
      border-color: $primary-light;
      transform: translateY(-2rpx);
    }
  }

  &:active {
    transform: translateY(0);
  }
}


/* 现代化上传进度条样式 */
.upload-progress-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.progress-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%);
  backdrop-filter: blur(12rpx);
}

.progress-card {
  position: relative;
  background: linear-gradient(135deg, $bg-primary 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: $border-radius-xl;
  padding: $spacing-xxl;
  box-shadow: 0 24rpx 64rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid rgba(24, 107, 255, 0.2);
  width: 85%;
  max-width: 600rpx;
  z-index: 10000;
  backdrop-filter: blur(8rpx);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-xl;
}

.progress-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-primary;
  background: linear-gradient(135deg, $primary-color 0%, #4f9eff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.progress-percent {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  text-shadow: 0 2rpx 4rpx rgba(24, 107, 255, 0.2);
}

.progress-bar {
  height: 16rpx;
  background: linear-gradient(135deg, $bg-secondary 0%, #e2e8f0 100%);
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: $spacing-lg;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, $primary-color 0%, #4f9eff 50%, #7db3ff 100%);
  border-radius: 8rpx;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(24, 107, 255, 0.3);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.progress-info {
  text-align: center;
}

.progress-text {
  font-size: $font-size-base;
  color: $text-secondary;
  display: block;
  margin-bottom: $spacing-md;
  font-weight: $font-weight-medium;
}

.progress-warning {
  font-size: $font-size-base;
  color: $warning-color;
  display: block;
  margin-bottom: $spacing-sm;
  font-weight: $font-weight-semibold;
  background: rgba(250, 173, 20, 0.1);
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-base;
  border-left: 4rpx solid $warning-color;
}

.progress-tip {
  font-size: $font-size-sm;
  color: $text-tertiary;
  display: block;
  font-style: italic;
}
</style>