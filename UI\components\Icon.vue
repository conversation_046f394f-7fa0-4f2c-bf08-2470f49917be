<template>
  <text class="iconfont" :class="iconClass" :style="iconStyle"></text>
</template>

<script>
export default {
  name: 'Icon',
  props: {
    type: {
      type: String,
      required: true
    },
    size: {
      type: [Number, String],
      default: 24
    },
    color: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconClass() {
      return `icon-${this.type}`;
    },
    iconStyle() {
      const style = {};
      if (this.size) {
        style.fontSize = `${this.size}rpx`;
      }
      if (this.color) {
        style.color = this.color;
      }
      return style;
    }
  }
}
</script>

<style>
/* 图标样式在App.vue中已全局引入 */
</style> 