/**
 * 通用数据格式化工具
 * 统一处理各种数据的格式化逻辑
 */

// 状态映射配置
export const STATUS_MAPS = {
    user: {
        1: 'active',
        0: 'disabled',
        2: 'dismissed'
    },
    employee: {
        1: 'active',
        0: 'disabled',
        2: 'dismissed'
    },
    video: {
        0: 'offline',
        1: 'online',
        2: 'failed',
        3: 'compressing'
    }
};

// 角色映射配置
export const ROLE_MAPS = {
    1: 'admin',
    2: 'manager',
    3: 'employee'
};

// 角色显示文本
export const ROLE_TEXTS = {
    admin: '超管',
    manager: '管理',
    employee: '员工',
    user: '用户'
};

/**
 * 通用数据格式化器
 */
export class DataFormatter {
    /**
     * 格式化用户数据
     */
    static formatUser (data, type = 'user') {
        if (!data) return null;

        return {
            id: data.id || data.userId,
            username: data.username || data.userName || data.nickname,
            nickname: data.nickname,
            avatar: data.avatar,
            phone: data.phone || data.mobile || '',
            email: data.email || '',
            type: type,
            role: ROLE_MAPS[data.userType] || type,
            level: data.userType,
            status: STATUS_MAPS[type]?.[data.status] || 'active',
            statusCode: data.status,
            disabled: data.status === 0,
            dismissed: data.status === 2,

            // 关系信息
            employeeId: data.employeeId,
            employeeName: data.employeeName,
            managerId: data.managerId || data.parentUserId,
            managerName: data.managerName || data.parentUserName,

            // 时间信息
            registerTime: data.registerTime || data.createTime,
            lastLoginTime: data.lastLoginTime,

            // 统计数据
            userCount: data.userCount || data.totalSubordinateUserCount || 0,
            employeeCount: data.employeeCount || data.directSubordinateCount || 0,

            // 时间维度统计
            totalViews: this.extractTimeStats(data.statistics, 'views'),
            totalQuizzes: this.extractTimeStats(data.statistics, 'quizzes'),
            totalRewards: this.extractTimeStats(data.statistics, 'rewards'),

            // 其他字段
            remark: data.remark || '',
            createTime: data.createTime,
            updateTime: data.updateTime
        };
    }

    /**
     * 格式化视频数据
     */
    static formatVideo (data) {
        if (!data) return null;

        return {
            id: data.id,
            title: data.title,
            description: data.description,
            url: data.url || data.videoUrl,
            coverUrl: data.coverUrl,
            duration: data.duration,
            status: STATUS_MAPS.video[data.status] || 'online',
            statusCode: data.status,
            viewCount: data.viewCount || 0,
            rewardAmount: data.rewardAmount || 0,
            questions: data.questions,
            creator: data.creator || data.createdBy,
            creatorName: data.creatorName,
            createTime: data.createTime,
            updateTime: data.updateTime,
            fileSize: data.fileSize,
            format: data.format,
            resolution: data.resolution
        };
    }

    /**
     * 格式化批次数据
     */
    static formatBatch (data) {
        if (!data) return null;

        return {
            id: data.id,
            name: data.name,
            description: data.description,
            videoId: data.videoId,
            videoTitle: data.videoTitle,
            videoDescription: data.videoDescription,
            videoCoverUrl: data.videoCoverUrl,
            videoUrl: data.videoUrl,
            videoDuration: data.videoDuration,
            currentParticipants: data.currentParticipants || 0,
            rewardAmount: data.rewardAmount || data.redPacketAmount || 0,
            questions: data.questions,
            startTime: data.startTime,
            endTime: data.endTime,
            creatorId: data.creatorId,
            status: data.status === 1 ? 'online' : 'offline',
            statusCode: data.status,
            createTime: data.createTime,
            updateTime: data.updateTime
        };
    }

    /**
     * 从统计数据中提取时间维度统计
     */
    static extractTimeStats (statistics, type) {
        if (!statistics || !statistics[type]) {
            return {
                today: 0,
                yesterday: 0,
                thisWeek: 0,
                thisMonth: 0
            };
        }

        const stats = statistics[type];
        return {
            today: stats.today || 0,
            yesterday: stats.yesterday || 0,
            thisWeek: stats.thisWeek || 0,
            thisMonth: stats.thisMonth || 0
        };
    }

    /**
     * 格式化日期
     */
    static formatDate (dateString, format = 'date') {
        if (!dateString) return '';

        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';

        switch (format) {
            case 'date':
                return date.toISOString().split('T')[0];
            case 'datetime':
                return date.toLocaleString('zh-CN');
            case 'time':
                return date.toLocaleTimeString('zh-CN');
            default:
                return dateString;
        }
    }

    /**
     * 格式化金额
     */
    static formatMoney (amount, currency = '¥') {
        if (amount === null || amount === undefined) return '0.00';
        return `${currency}${parseFloat(amount).toFixed(2)}`;
    }

    /**
     * 格式化文件大小
     */
    static formatFileSize (bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 格式化时长
     */
    static formatDuration (seconds) {
        if (!seconds || seconds < 0) return '00:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * 获取状态文本
     */
    static getStatusText (status, type = 'user') {
        const statusMap = {
            active: '正常',
            disabled: '禁用',
            dismissed: '离职',
            online: '上线',
            offline: '下线',
            failed: '失败',
            compressing: '压缩中'
        };

        return statusMap[status] || '未知';
    }

    /**
     * 获取角色文本
     */
    static getRoleText (role) {
        return ROLE_TEXTS[role] || '未知';
    }

    /**
     * 批量格式化数据
     */
    static formatList (list, formatter, ...args) {
        if (!Array.isArray(list)) return [];
        return list.map(item => formatter(item, ...args)).filter(Boolean);
    }
}

// 导出便捷函数
export const formatUser = (data, type) => DataFormatter.formatUser(data, type);
export const formatVideo = (data) => DataFormatter.formatVideo(data);
export const formatBatch = (data) => DataFormatter.formatBatch(data);
export const formatDate = (date, format) => DataFormatter.formatDate(date, format);
export const formatMoney = (amount, currency) => DataFormatter.formatMoney(amount, currency);
export const formatFileSize = (bytes) => DataFormatter.formatFileSize(bytes);
export const formatDuration = (seconds) => DataFormatter.formatDuration(seconds);
export const getStatusText = (status, type) => DataFormatter.getStatusText(status, type);
export const getRoleText = (role) => DataFormatter.getRoleText(role);
export const formatList = (list, formatter, ...args) => DataFormatter.formatList(list, formatter, ...args); 