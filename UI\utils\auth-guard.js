/**
 * Authentication guard utility
 * Provides middleware functions for protecting pages and checking permissions
 */

import adminAuthService from './adminAuthService.js'
import { showSuccess, showError } from './toast-manager.js'

/**
 * Page authentication guard
 * Call this in onLoad of pages that require authentication
 * @param {Object} options - Page options from onLoad
 * @param {Array} requiredPermissions - Array of required permissions
 * @param {Array} allowedUserTypes - Array of allowed user types
 * @returns {boolean} - Returns true if user is authorized, false otherwise
 */
export function requireAuth (options = {}, requiredPermissions = [], allowedUserTypes = []) {
  // Check if user is logged in
  if (!adminAuthService.isLoggedIn()) {
    adminAuthService.redirectToLogin()
    return false
  }

  // Check if session is valid
  if (!adminAuthService.isSessionValid()) {
    adminAuthService.logout()
    adminAuthService.redirectToLogin()
    return false
  }

  // Check user type if specified
  if (allowedUserTypes.length > 0) {
    const userType = adminAuthService.getUserType()
    if (!allowedUserTypes.includes(userType)) {
      uni.showToast({
        title: '您没有权限访问此页面',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return false
    }
  }

  // Check permissions if specified
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission =>
      adminAuthService.hasPermission(permission)
    )

    if (!hasAllPermissions) {
      uni.showToast({
        title: '您没有权限访问此功能',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return false
    }
  }

  // Refresh session on successful access
  adminAuthService.refreshSession()
  return true
}

/**
 * Admin-only page guard (超管)
 * @param {Object} options - Page options from onLoad
 * @returns {boolean}
 */
export function requireAdmin (options = {}) {
  return requireAuth(options, ['*'], ['admin'])
}

/**
 * Management page guard (超管 + 管理)
 * @param {Object} options - Page options from onLoad
 * @returns {boolean}
 */
export function requireManagement (options = {}) {
  return requireAuth(options, ['view_dashboard'], ['admin', 'manager'])
}

/**
 * Employee page guard (超管 + 管理 + 员工)
 * @param {Object} options - Page options from onLoad
 * @returns {boolean}
 */
export function requireEmployee (options = {}) {
  return requireAuth(options, [], ['admin', 'manager', 'employee'])
}

/**
 * Any authenticated user page guard (任何已登录用户)
 * @param {Object} options - Page options from onLoad
 * @returns {boolean}
 */
export function requireAnyAuth (options = {}) {
  return requireAuth(options, [], [])
}

/**
 * Check if current user can access admin features
 * @returns {boolean}
 */
export function canAccessAdmin () {
  return adminAuthService.isLoggedIn() &&
    adminAuthService.hasPermission('*') &&
    adminAuthService.getUserType() === 'admin'
}

/**
 * Check if current user can access management features
 * @returns {boolean}
 */
export function canAccessManagement () {
  return adminAuthService.isLoggedIn() &&
    (adminAuthService.hasPermission('*') || adminAuthService.hasPermission('view_dashboard')) &&
    ['admin', 'manager'].includes(adminAuthService.getUserType())
}

/**
 * Check if current user is an employee
 * @returns {boolean}
 */
export function isEmployee () {
  return adminAuthService.isLoggedIn() &&
    adminAuthService.getUserType() === 'employee'
}

/**
 * Get user display info for UI (synchronous, uses cached data)
 * @returns {Object}
 */
export function getUserDisplayInfo () {
  if (!adminAuthService.isLoggedIn()) {
    return {
      name: '未登录',
      username: '',
      userType: '',
      userTypeText: '游客',
      avatar: '',
      email: '',
      phone: '',
      department: '',
      position: ''
    }
  }

  const loginInfo = adminAuthService.getLoginInfo()
  const typeMap = {
    'employee': '员工',
    'manager': '管理',
    'admin': '超管'
  }

  return {
    name: loginInfo.nickName || loginInfo.username || '未知用户',
    username: loginInfo.username || '',
    userType: loginInfo.userType || '',
    userTypeText: typeMap[loginInfo.userType] || '未知',
    avatar: loginInfo.avatar || '',
    email: loginInfo.email || '',
    phone: loginInfo.phone || '',
    department: loginInfo.department || '',
    position: loginInfo.position || ''
  }
}

/**
 * Get complete user display info for UI (asynchronous, can refresh from server)
 * @param {boolean} forceRefresh - 是否强制从服务器刷新
 * @returns {Promise<Object>}
 */
export async function getCompleteUserDisplayInfo (forceRefresh = false) {
  return await adminAuthService.getCompleteUserInfo(forceRefresh)
}

/**
 * Navigation guard for tab bar pages
 * Redirects users to appropriate starting page based on their role
 */
export function handleTabBarNavigation () {
  if (!adminAuthService.isLoggedIn() || !adminAuthService.isSessionValid()) {
    adminAuthService.redirectToLogin()
    return
  }

  const userType = adminAuthService.getUserType()

  // Show tab bar for authenticated users
  uni.showTabBar()

  // Different user types see different tab bar items
  // This can be customized based on business requirements
}

/**
 * Logout with confirmation dialog
 * @param {string} message - Custom confirmation message
 */
export function logoutWithConfirm (message = '确定要退出登录吗？') {
  uni.showModal({
    title: '确认退出',
    content: message,
    success: async (res) => {
      if (res.confirm) {
        try {
          const success = await adminAuthService.logout()

          if (success) {
            showSuccess('已退出登录')

            setTimeout(() => {
              adminAuthService.redirectToLogin()
            }, 1500)
          } else {
            showError('退出失败，请重试')
          }
        } catch (error) {
          showError('退出失败，请重试')
        }
      }
    }
  })
}

// Export all functions as default
export default {
  requireAuth,
  requireAdmin,
  requireManagement,
  requireEmployee,
  requireAnyAuth,
  canAccessAdmin,
  canAccessManagement,
  isEmployee,
  getUserDisplayInfo,
  getCompleteUserDisplayInfo,
  handleTabBarNavigation,
  logoutWithConfirm
}
