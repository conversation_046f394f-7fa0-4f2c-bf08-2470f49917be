<template>
  <view class="video-container" :class="{ 'fullscreen': isFullscreen }">
    <!-- 视频元素 -->
    <video ref="videoRef" :src="src" :poster="poster" class="video-element" :controls="false" :autoplay="autoplay"
      :muted="false" preload="metadata" playsinline webkit-playsinline x5-playsinline x5-video-player-type="h5"
      x5-video-player-fullscreen="true" @loadedmetadata="onLoadedMetadata" @timeupdate="onTimeUpdate" @ended="onEnded"
      @play="onPlay" @pause="onPause" @waiting="onWaiting" @canplay="onCanPlay" @progress="onProgress"
      @loadstart="onLoadStart" @durationchange="onDurationChange" @click="toggleControls"></video>

    <!-- 播放按钮覆盖层 -->
    <view class="play-overlay" v-if="showPlayButton" @click="togglePlay">
      <view class="play-button">
        <view class="play-icon">
          <svg v-if="!isPlaying" width="24" height="24" viewBox="0 0 24 24" fill="white">
            <path d="M8 5v14l11-7z" />
          </svg>
          <svg v-else width="24" height="24" viewBox="0 0 24 24" fill="white">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
          </svg>
        </view>
      </view>
    </view>

    <!-- 全屏顶部控制栏（独立显示，不受 showControls 影响） -->
    <view class="top-controls-fullscreen" v-if="isFullscreen">
      <view class="back-button" @click="exitFullscreen">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
        </svg>
      </view>
      <text class="video-title">{{ title }}</text>
    </view>

    <!-- 控制栏 -->
    <view class="controls" v-if="showControls" :class="{ 'fullscreen-controls': isFullscreen }">
      <!-- 顶部控制栏（全屏时显示） -->
      <view class="top-controls" v-if="isFullscreen">
        <view class="back-button" @click="exitFullscreen">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
          </svg>
        </view>
        <text class="video-title">{{ title }}</text>
      </view>

      <!-- 底部控制栏 -->
      <view class="bottom-controls">
        <!-- 播放/暂停按钮 -->
        <view class="control-button" @click="togglePlay">
          <svg v-if="!isPlaying" width="20" height="20" viewBox="0 0 24 24" fill="white">
            <path d="M8 5v14l11-7z" />
          </svg>
          <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="white">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
          </svg>
        </view>

        <!-- 时间显示 -->
        <text class="time-display">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</text>

        <!-- 进度条 -->
        <view class="progress-container" @click="onProgressClick">
          <view class="progress-bg">
            <view class="progress-buffer" :style="{ width: bufferProgress + '%' }"></view>
            <view class="progress-played" :style="{ width: playedProgress + '%' }"></view>
            <view class="progress-thumb" :style="{ left: playedProgress + '%' }" v-if="!disableSeek"></view>
          </view>
        </view>

        <!-- 全屏按钮 -->
        <view class="control-button" @click="toggleFullscreen">
          <svg v-if="!isFullscreen" width="20" height="20" viewBox="0 0 24 24" fill="white">
            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
          </svg>
          <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="white">
            <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z" />
          </svg>
        </view>
      </view>
    </view>

    <!-- 加载指示器 -->
    <view class="loading-overlay" v-if="isLoading || isBuffering">
      <view class="loading-spinner">
        <view class="spinner"></view>
      </view>
      <text class="loading-text">{{ isBuffering ? '缓冲中...' : '加载中...' }}</text>
      <view class="loading-progress" v-if="loadingProgress > 0">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: loadingProgress + '%' }"></view>
        </view>
        <text class="progress-text">{{ Math.round(loadingProgress) }}%</text>
      </view>
    </view>

    <!-- 暂停指示器 -->
    <view class="pause-indicator" v-if="!isPlaying && !showPlayButton && !isLoading && !isBuffering">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="white" opacity="0.8">
        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
      </svg>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomVideoPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    poster: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    disableSeek: {
      type: Boolean,
      default: false
    },
    disableProgressDrag: {
      type: Boolean,
      default: false
    },
    showRate: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      isPlaying: false,
      isFullscreen: false,
      showControls: true,
      showPlayButton: true,
      isLoading: true,
      isBuffering: false,
      loadingProgress: 0,
      currentTime: 0,
      duration: 0,
      bufferProgress: 0,
      maxWatchedTime: 0, // 已观看的最大时间点
      controlsTimer: null
    }
  },
  computed: {
    playedProgress () {
      const progress = this.duration > 0 ? (this.currentTime / this.duration) * 100 : 0
      return Math.min(100, Math.max(0, progress))
    }
  },
  mounted () {
    this.initPlayer()
  },
  beforeUnmount () {
    this.cleanup()
  },
  methods: {
    initPlayer () {
      // 初始化播放器
      if (this.autoplay) {
        this.play()
      }
    },

    cleanup () {
      if (this.controlsTimer) {
        clearTimeout(this.controlsTimer)
      }
    },

    // 获取视频元素
    getVideoElement () {
      const videoRef = this.$refs.videoRef
      // 在 uni-app H5 环境下，可能需要获取实际的 DOM 元素
      if (videoRef && videoRef.$el) {
        return videoRef.$el
      } else if (videoRef && videoRef.tagName === 'VIDEO') {
        return videoRef
      } else if (videoRef) {
        // 尝试查找 video 元素
        const video = videoRef.querySelector ? videoRef.querySelector('video') : null
        return video
      }
      return null
    },

    // 播放控制
    async play () {
      try {
        const video = this.getVideoElement()
        if (video && typeof video.play === 'function') {
          await video.play()
          this.isPlaying = true
          this.showPlayButton = false
        }
      } catch (error) {
        uni.showToast({
          title: '播放失败',
          icon: 'none'
        })
      }
    },

    pause () {
      const video = this.getVideoElement()
      if (video && typeof video.pause === 'function') {
        video.pause()
        this.isPlaying = false
      }
    },

    togglePlay () {
      if (this.isPlaying) {
        this.pause()
      } else {
        this.play()
      }
    },

    // 全屏控制
    toggleFullscreen () {
      if (this.isFullscreen) {
        this.exitFullscreen()
      } else {
        this.enterFullscreen()
      }
    },

    async enterFullscreen () {
      try {
        const video = this.getVideoElement()
        if (!video) {
          return
        }

        // 强制横屏
        if (screen.orientation && screen.orientation.lock) {
          try {
            await screen.orientation.lock('landscape')
          } catch (orientationError) {
            // 横屏锁定失败，忽略错误
          }
        }

        if (video.requestFullscreen) {
          await video.requestFullscreen()
        } else if (video.webkitRequestFullscreen) {
          await video.webkitRequestFullscreen()
        } else if (video.mozRequestFullScreen) {
          await video.mozRequestFullScreen()
        } else if (video.msRequestFullscreen) {
          await video.msRequestFullscreen()
        }

        this.isFullscreen = true
        this.showControls = true
        this.autoHideControls()
        this.$emit('fullscreenchange', true)
      } catch (error) {
        // 进入全屏失败，忽略错误
      }
    },

    async exitFullscreen () {
      try {
        if (document.exitFullscreen) {
          await document.exitFullscreen()
        } else if (document.webkitExitFullscreen) {
          await document.webkitExitFullscreen()
        } else if (document.mozCancelFullScreen) {
          await document.mozCancelFullScreen()
        } else if (document.msExitFullscreen) {
          await document.msExitFullscreen()
        }

        // 解锁屏幕方向
        if (screen.orientation && screen.orientation.unlock) {
          try {
            screen.orientation.unlock()
          } catch (orientationError) {
            // 屏幕方向解锁失败，忽略错误
          }
        }

        this.isFullscreen = false
        this.$emit('fullscreenchange', false)
      } catch (error) {
        // 退出全屏失败，忽略错误
      }
    },

    // 控制栏显示/隐藏
    toggleControls () {
      this.showControls = !this.showControls
      if (this.showControls) {
        this.autoHideControls()
      }
    },

    autoHideControls () {
      if (this.controlsTimer) {
        clearTimeout(this.controlsTimer)
      }
      this.controlsTimer = setTimeout(() => {
        if (this.isPlaying) {
          this.showControls = false
        }
      }, 3000)
    },

    // 进度控制
    onProgressClick (event) {
      if (this.disableSeek) {
        uni.showToast({
          title: '请完整观看视频',
          icon: 'none'
        })
        return
      }

      const rect = event.currentTarget.getBoundingClientRect()
      const clickX = event.detail.x - rect.left
      const percentage = clickX / rect.width
      const targetTime = this.duration * percentage

      // 防快进：不能跳转到未观看的位置
      if (targetTime > this.maxWatchedTime + 2) {
        uni.showToast({
          title: '请完整观看视频',
          icon: 'none'
        })
        return
      }

      const video = this.getVideoElement()
      if (video) {
        video.currentTime = targetTime
      }
    },

    // 事件处理
    onLoadStart () {
      this.isLoading = true
      this.loadingProgress = 0
    },

    onLoadedMetadata () {
      const video = this.getVideoElement()
      if (video) {
        this.duration = video.duration || 0
        this.isLoading = false
      }
    },

    onProgress () {
      const video = this.getVideoElement()
      if (video && video.buffered && video.buffered.length > 0) {
        this.loadingProgress = (video.buffered.end(video.buffered.length - 1) / this.duration) * 100
      }
    },

    onWaiting () {
      this.isBuffering = true
    },

    onCanPlay () {
      this.isBuffering = false
      this.isLoading = false
    },

    onDurationChange () {
      const video = this.getVideoElement()
      if (video && video.duration) {
        this.duration = video.duration
      }
    },

    onTimeUpdate () {
      const video = this.getVideoElement()
      if (!video) return

      this.currentTime = video.currentTime || 0

      // 确保 duration 有值
      if (!this.duration && video.duration) {
        this.duration = video.duration
      }

      // 更新最大观看时间
      if (this.currentTime > this.maxWatchedTime) {
        this.maxWatchedTime = this.currentTime
      }

      // 防快进检查
      if (this.currentTime > this.maxWatchedTime + 2) {
        video.currentTime = this.maxWatchedTime
        uni.showToast({
          title: '请完整观看视频',
          icon: 'none'
        })
      }

      // 更新缓冲进度
      if (video.buffered && video.buffered.length > 0 && this.duration > 0) {
        this.bufferProgress = (video.buffered.end(video.buffered.length - 1) / this.duration) * 100
      }

      this.$emit('timeupdate', {
        current: this.currentTime,
        duration: this.duration
      })
    },

    onEnded () {
      this.isPlaying = false
      this.showPlayButton = true
      this.maxWatchedTime = this.duration
      this.$emit('ended')
    },

    onPlay () {
      this.isPlaying = true
      this.showPlayButton = false
      this.autoHideControls()
    },

    onPause () {
      this.isPlaying = false
      this.showControls = true
    },

    // 工具方法
    formatTime (seconds) {
      if (!seconds || isNaN(seconds)) return '00:00'

      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },

    // 公共方法
    seek (time) {
      if (time <= this.maxWatchedTime + 2) {
        const video = this.getVideoElement()
        if (video) {
          video.currentTime = time
        }
      }
    },

    getCurrentTime () {
      return this.currentTime
    }
  }
}
</script>

<style scoped>
.video-container {
  position: relative;
  width: 100%;
  height: 422rpx;
  background: #000;
  overflow: hidden;

}

.video-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.play-button {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20rpx;
  transition: opacity 0.3s;
}

.controls.fullscreen-controls {
  padding: 40rpx;
}

.top-controls-fullscreen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 40rpx;
  background: linear-gradient(rgba(0, 0, 0, 0.7), transparent);
  z-index: 10001;
}

.top-controls {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.back-button {
  margin-right: 20rpx;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.video-title {
  color: white;
  font-size: 32rpx;
  flex: 1;
}

.bottom-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.control-button {
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  min-height: 40rpx;
}

.time-display {
  color: white;
  font-size: 24rpx;
  white-space: nowrap;
}

.progress-container {
  flex: 1;
  padding: 10rpx 0;
}

.progress-bg {
  position: relative;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
}

.progress-buffer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 3rpx;
  transition: width 0.3s;
}

.progress-played {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #39BFFD;
  border-radius: 3rpx;
  transition: width 0.1s;
}

.progress-thumb {
  position: absolute;
  top: -6rpx;
  width: 18rpx;
  height: 18rpx;
  background: #39BFFD;
  border-radius: 50%;
  transform: translateX(-50%);
  transition: left 0.1s;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.loading-spinner {
  position: relative;
}

.spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #39BFFD;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: white;
  font-size: 28rpx;
  text-align: center;
}

.loading-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  width: 200rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #39BFFD;
  border-radius: 3rpx;
  transition: width 0.3s;
}

.progress-text {
  color: white;
  font-size: 24rpx;
}

.pause-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 20rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
