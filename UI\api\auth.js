/**
 * 认证相关API
 * 处理用户登录、登出、获取用户信息等认证功能
 */

import request from '../utils/request.js'

/**
 * 登录请求参数
 * @typedef {Object} LoginRequestDto
 * @property {string} username - 用户名
 * @property {string} password - 密码
 */

/**
 * 登录响应数据
 * @typedef {Object} LoginResponseDto
 * @property {string} accessToken - 访问令牌
 * @property {Object} userInfo - 用户信息
 */

/**
 * 用户信息数据
 * @typedef {Object} UserInfoDto
 * @property {string} userId - 用户ID
 * @property {string} username - 用户名
 * @property {string} nickName - 昵称
 * @property {number} userType - 用户类型
 */

/**
 * API响应结果
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 用户登录
 * @param {LoginRequestDto} params - 登录参数
 * @returns {Promise<ApiResult<LoginResponseDto>>} 登录结果
 */
export function login (params) {
  return request.post('/Auth/login', params)
}

/**
 * 用户登出
 * @returns {Promise<ApiResult<void>>} 登出结果
 */
export function logout () {
  return request.post('/Auth/logout')
}

/**
 * 获取当前登录用户信息
 * @returns {Promise<ApiResult<UserInfoDto>>} 用户信息
 */
export function getUserInfo () {
  return request.get('/Auth/userinfo')
}

/**
 * 获取当前用户信息 (别名，保持向后兼容)
 * @returns {Promise<ApiResult<UserInfoDto>>} 用户信息
 */
export function getCurrentUser () {
  return getUserInfo()
}

// 默认导出所有认证相关API
export default {
  login,
  logout,
  getUserInfo,
  getCurrentUser
}
