/**
 * 卡片批次管理相关API
 * 处理卡片批次的创建、激活、查询等功能
 */

import request from '../utils/request.js'

/**
 * 批次查询参数接口
 * @typedef {Object} CardBatchQueryParams
 * @property {string} [BatchNo] - 批次号
 * @property {string} [Channel] - 渠道
 * @property {string} [Type] - 类型
 * @property {string} [Status] - 状态
 * @property {string} [CreateTimeStart] - 创建时间开始
 * @property {string} [CreateTimeEnd] - 创建时间结束
 * @property {number} [GameCurrency] - 游戏币
 * @property {number} [ActualValue] - 实际价值
 * @property {number} PageIndex - 页码
 * @property {number} PageSize - 每页大小
 */

/**
 * 批次数据接口
 * @typedef {Object} BatchDto
 * @property {string} batchNo - 批次号
 * @property {string} channel - 渠道
 * @property {string} type - 类型
 * @property {number} gameCurrency - 游戏币
 * @property {number} actualValue - 实际价值
 * @property {number} price - 价格
 * @property {number} quantity - 数量
 * @property {string} status - 状态
 * @property {string} createTime - 创建时间
 * @property {string} creator - 创建者
 * @property {string} [creatorName] - 创建者名称
 * @property {string} [updateTime] - 更新时间
 * @property {string} [updaterName] - 更新者名称
 * @property {string} [activateTime] - 激活时间
 * @property {string} [activatorName] - 激活者名称
 * @property {string} [shippingTime] - 出货时间
 * @property {string} [shippingOperatorName] - 出货操作者名称
 * @property {string} [targetId] - 目标ID
 * @property {string} [targetName] - 目标名称
 * @property {string} [remark] - 备注
 */

/**
 * 创建批次请求接口
 * @typedef {Object} CreateBatchDto
 * @property {string} channel - 渠道
 * @property {string} cardType - 卡片类型
 * @property {number} channelCardValueConfigID - 渠道卡值配置ID
 * @property {number} shippingTargetID - 出货目标ID
 * @property {number} quantity - 数量
 * @property {string} [remark] - 备注
 */

/**
 * 激活批次请求接口
 * @typedef {Object} ActivateCardBatchDto
 * @property {string} batchNo - 批次号
 */

/**
 * 创建发货记录请求接口
 * @typedef {Object} CreateShippingDto
 * @property {string} batchNo - 批次号
 * @property {string} status - 状态
 * @property {string} operator - 操作者
 * @property {string} targetId - 目标ID
 * @property {number} quantity - 数量
 * @property {string} [remark] - 备注
 */

/**
 * 卡片数据接口
 * @typedef {Object} CardDto
 * @property {string} cardNo - 卡号
 * @property {string} batchNo - 批次号
 * @property {number} gameCurrency - 游戏币
 * @property {number} actualValue - 实际价值
 * @property {number} price - 价格
 * @property {string} status - 状态
 * @property {string} [useTime] - 使用时间
 * @property {string} [usePlayerId] - 使用玩家ID
 * @property {string} [holderId] - 持有者ID
 * @property {string} [acquireTime] - 获取时间
 * @property {string} createTime - 创建时间
 */

/**
 * 卡片查询参数接口
 * @typedef {Object} CardQueryParams
 * @property {string} [CardNo] - 卡号
 * @property {string} [BatchNo] - 批次号
 * @property {string} [Status] - 状态
 * @property {string} [HolderId] - 持有者ID
 * @property {string} [CreateTimeStart] - 创建时间开始
 * @property {string} [CreateTimeEnd] - 创建时间结束
 * @property {string} [AcquireTime] - 获取时间
 * @property {string} [UseTime] - 使用时间
 * @property {number} [GameCurrency] - 游戏币
 * @property {number} [ActualValue] - 实际价值
 * @property {number} [Price] - 价格
 * @property {number} PageIndex - 页码
 * @property {number} PageSize - 每页大小
 */

/**
 * 批次出货请求接口
 * @typedef {Object} ShipCardBatchDto
 * @property {string} batchNo - 批次号
 */

/**
 * 分页结果类型
 * @typedef {Object} PageResult
 * @property {Array<*>} list - 数据列表
 * @property {number} total - 总记录数
 * @property {number} page - 当前页码
 * @property {number} pageSize - 每页大小
 */

/**
 * API结果类型
 * @template T
 * @typedef {Object} ApiResult
 * @property {number} code - 状态码
 * @property {string} message - 消息
 * @property {T} data - 数据
 */

/**
 * 批次查询结果类型
 * @typedef {ApiResult<PageResult<BatchDto>>} BatchQueryResult
 */

/**
 * 卡片查询结果类型
 * @typedef {ApiResult<PageResult<CardDto>>} CardQueryResult
 */

/**
 * 创建批次结果类型
 * @typedef {ApiResult<boolean>} CreateBatchResult
 */

/**
 * 激活批次结果类型
 * @typedef {ApiResult<void>} ActivateBatchResult
 */

/**
 * 创建发货记录结果类型
 * @typedef {ApiResult<boolean>} CreateShippingResult
 */

/**
 * 单个批次查询结果类型
 * @typedef {ApiResult<BatchDto>} BatchDetailResult
 */

/**
 * 批次出货结果类型
 * @typedef {ApiResult<boolean>} ShipCardBatchResult
 */

/**
 * 删除批次结果类型
 * @typedef {ApiResult<boolean>} DeleteCardBatchResult
 */

/**
 * 查询批次列表
 * @param {CardBatchQueryParams} params - 查询参数
 * @returns {Promise<BatchQueryResult>} 批次列表
 */
export function queryCardBatches(params) {
  return request.get('/CardBatch/query', params)
}

/**
 * 创建批次
 * @param {CreateBatchDto} data - 批次数据
 * @returns {Promise<CreateBatchResult>} 是否成功
 */
export function createCardBatch(data) {
  return request.post('/CardBatch/create', data)
}

/**
 * 激活批次
 * @param {ActivateCardBatchDto} data - 激活批次数据
 * @returns {Promise<ActivateBatchResult>} 是否成功
 */
export function activateCardBatch(data) {
  return request.post('/CardBatch/activate', data)
}

/**
 * 创建发货记录（出货功能）
 * @param {CreateShippingDto} data - 发货记录数据
 * @returns {Promise<CreateShippingResult>} 是否成功
 */
export function createShipping(data) {
  return request.post('/CardShippingRecord/Create', data)
}

/**
 * 查询卡片列表（用于批次详情页面）
 * @param {CardQueryParams} params - 查询参数
 * @returns {Promise<CardQueryResult>} 卡片列表
 */
export function queryCards(params) {
  return request.get('/CardInfo/query', params)
}

/**
 * 查询单个批次详情（使用getInfo接口）
 * @param {string} batchNo - 批次号
 * @returns {Promise<BatchDetailResult>} 批次详情
 */
export function queryCardBatchInfo(batchNo) {
  return request.get('/CardBatch/getInfo', {
    BatchNo: batchNo
  })
}

/**
 * 批次出货（新接口）
 * @param {ShipCardBatchDto} data - 批次出货数据
 * @returns {Promise<ShipCardBatchResult>} 是否成功
 */
export function shipCardBatch(data) {
  return request.post('/CardBatch/ship', data)
}

/**
 * 删除批次
 * @param {string} batchNo - 批次号
 * @returns {Promise<DeleteCardBatchResult>} 是否成功
 */
export function deleteCardBatch(batchNo) {
  return request.post('/CardBatch/delete', {
    batchNo
  })
}

/**
 * 导出卡片数据
 * @param {CardQueryParams} params - 查询参数
 * @returns {Promise<any>} 导出结果
 */
export function exportCards(params) {
  return request.post('/CardInfo/export', params, {
    responseType: 'blob'
  })
}

/**
 * 获取卡片批次统计信息
 * @returns {Promise<ApiResult<{totalBatches: number, totalCards: number, activeBatches: number, shippedBatches: number}>>} 统计信息
 */
export function getCardBatchStats() {
  return request.get('/CardBatch/stats')
}

/**
 * 获取卡片类型列表
 * @returns {Promise<ApiResult<Array<{id: number, name: string, description: string}>>>} 卡片类型列表
 */
export function getCardTypes() {
  return request.get('/CardBatch/types')
}

/**
 * 获取渠道列表
 * @returns {Promise<ApiResult<Array<{id: number, name: string, description: string}>>>} 渠道列表
 */
export function getChannels() {
  return request.get('/CardBatch/channels')
}

/**
 * 获取渠道卡值配置列表
 * @param {number} channelId - 渠道ID
 * @returns {Promise<ApiResult<Array<{id: number, channelId: number, gameCurrency: number, actualValue: number, price: number}>>>} 渠道卡值配置列表
 */
export function getChannelCardValueConfigs(channelId) {
  return request.get('/CardBatch/channel-card-value-configs', { channelId })
}

/**
 * 获取出货目标列表
 * @returns {Promise<ApiResult<Array<{id: number, name: string, description: string}>>>} 出货目标列表
 */
export function getShippingTargets() {
  return request.get('/CardBatch/shipping-targets')
}

// 默认导出所有卡片批次相关API
export default {
  queryCardBatches,
  createCardBatch,
  activateCardBatch,
  createShipping,
  queryCards,
  queryCardBatchInfo,
  shipCardBatch,
  deleteCardBatch,
  exportCards,
  getCardBatchStats,
  getCardTypes,
  getChannels,
  getChannelCardValueConfigs,
  getShippingTargets
}
