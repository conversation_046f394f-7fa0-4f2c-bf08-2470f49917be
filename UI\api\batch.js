/**
 * 视频批次管理相关API
 * 处理视频批次的创建、查询、删除等功能
 */

import request from '../utils/request.js'

/**
 * 批次创建请求参数
 * @typedef {Object} VideoBatchCreateDto
 * @property {string} name - 批次名称
 * @property {string} description - 批次描述
 * @property {number} videoId - 视频ID
 * @property {string} startTime - 开始时间
 * @property {string} endTime - 结束时间
 * @property {number} redPacketAmount - 红包金额
 */

/**
 * 批次查询参数
 * @typedef {Object} VideoBatchQueryDto
 * @property {string} [Name] - 批次名称
 * @property {number} [VideoId] - 视频ID
 * @property {number} [CreatorId] - 创建者ID
 * @property {number} [Status] - 状态
 * @property {string} [StartTime] - 开始时间
 * @property {string} [EndTime] - 结束时间
 * @property {number} [PageIndex] - 页码
 * @property {number} [PageSize] - 每页大小
 * @property {string} [OrderField] - 排序字段
 * @property {boolean} [IsAsc] - 是否升序
 */

/**
 * 批次题目数据
 * @typedef {Object} VideoBatchQuestionDto
 * @property {string} questionText - 题目文本
 * @property {number} orderNum - 题目序号
 * @property {Array} options - 选项列表
 */

/**
 * 批次响应数据
 * @typedef {Object} VideoBatchResponseDto
 * @property {number} id - 批次ID
 * @property {string} name - 批次名称
 * @property {string} description - 批次描述
 * @property {number} videoId - 视频ID
 * @property {string} videoTitle - 视频标题
 * @property {string} videoDescription - 视频描述
 * @property {string} videoCoverUrl - 视频封面URL
 * @property {string} videoUrl - 视频地址
 * @property {number} videoDuration - 视频时长
 * @property {number} rewardAmount - 奖励金额
 * @property {Array<VideoBatchQuestionDto>} questions - 题目列表
 * @property {string} startTime - 开始时间
 * @property {string} endTime - 结束时间
 * @property {number} creatorId - 创建者ID
 * @property {number} status - 状态
 * @property {number} currentParticipants - 当前参与人数
 * @property {number} redPacketAmount - 红包金额
 * @property {string} createTime - 创建时间
 * @property {string} creatorName - 创建者姓名
 * @property {Object} statistics - 统计信息
 */

/**
 * 分页结果数据
 * @typedef {Object} PagedResultOfVideoBatchResponseDto
 * @property {Array<VideoBatchResponseDto>} items - 数据列表
 * @property {number} totalCount - 总记录数
 * @property {number} pageIndex - 当前页码
 * @property {number} pageSize - 每页大小
 * @property {number} totalPages - 总页数
 * @property {boolean} hasPreviousPage - 是否有上一页
 * @property {boolean} hasNextPage - 是否有下一页
 */

/**
 * API响应结果类型
 * @typedef {Object} ApiResult
 * @property {number} code - 响应代码
 * @property {string} msg - 响应消息
 * @property {boolean} success - 是否成功
 * @property {*} [data] - 响应数据
 */

/**
 * 创建批次
 * @param {VideoBatchCreateDto} data - 批次创建数据
 * @returns {Promise<ApiResult<number>>} 创建结果，返回批次ID
 */
export function createBatch (data) {
  return request.post('/Batch', data)
}

/**
 * 分页查询批次列表
 * @param {VideoBatchQueryDto} params - 查询参数
 * @returns {Promise<ApiResult<PagedResultOfVideoBatchResponseDto>>} 分页查询结果
 */
export function queryBatches (params) {
  return request.get('/Batch', params)
}

/**
 * 删除批次
 * @param {number} batchId - 批次ID
 * @returns {Promise<ApiResult<boolean>>} 删除结果
 */
export function deleteBatch (batchId) {
  return request.delete(`/Batch/${batchId}`)
}

/**
 * 获取批次详情
 * @param {number} batchId - 批次ID
 * @returns {Promise<ApiResult<VideoBatchResponseDto>>} 批次详情
 */
export function getBatchDetail (batchId) {
  return request.get(`/Batch/${batchId}`)
}

/**
 * 批量删除批次
 * @param {Array<number>} batchIds - 批次ID列表
 * @returns {Promise<Array<ApiResult<boolean>>>} 批量删除结果
 */
export function batchDeleteBatches (batchIds) {
  const promises = batchIds.map(batchId => deleteBatch(batchId))
  return Promise.all(promises)
}

/**
 * 根据视频ID查询批次
 * @param {number} videoId - 视频ID
 * @param {Object} [options] - 查询选项
 * @param {number} [options.PageIndex=1] - 页码
 * @param {number} [options.PageSize=20] - 每页大小
 * @returns {Promise<ApiResult<PagedResultOfVideoBatchResponseDto>>} 查询结果
 */
export function queryBatchesByVideo (videoId, options = {}) {
  return queryBatches({
    VideoId: videoId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据创建者ID查询批次
 * @param {number} creatorId - 创建者ID
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoBatchResponseDto>>} 查询结果
 */
export function queryBatchesByCreator (creatorId, options = {}) {
  return queryBatches({
    CreatorId: creatorId,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据状态查询批次
 * @param {number} status - 状态
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoBatchResponseDto>>} 查询结果
 */
export function queryBatchesByStatus (status, options = {}) {
  return queryBatches({
    Status: status,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 根据时间范围查询批次
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {Object} [options] - 查询选项
 * @returns {Promise<ApiResult<PagedResultOfVideoBatchResponseDto>>} 查询结果
 */
export function queryBatchesByTimeRange (startTime, endTime, options = {}) {
  return queryBatches({
    StartTime: startTime,
    EndTime: endTime,
    PageIndex: options.PageIndex || 1,
    PageSize: options.PageSize || 20
  })
}

/**
 * 获取批次统计数据
 * @param {number} batchId - 批次ID
 * @returns {Promise<ApiResult<VideoBatchStatisticsDto>>} 批次统计数据
 */
export function getBatchStatistics (batchId) {
  return request.get(`/Batch/${batchId}/statistics`)
}

/**
 * 获取批次状态选项
 * @returns {Array<{value: number, label: string}>} 状态选项
 */
export function getBatchStatusOptions () {
  return [
    { value: 0, label: '草稿' },
    { value: 1, label: '进行中' },
    { value: 2, label: '已结束' },
    { value: 3, label: '已暂停' }
  ]
}

/**
 * 获取排序字段选项
 * @returns {Array<{value: string, label: string}>} 排序字段选项
 */
export function getBatchSortFieldOptions () {
  return [
    { value: 'createTime', label: '创建时间' },
    { value: 'startTime', label: '开始时间' },
    { value: 'endTime', label: '结束时间' },
    { value: 'name', label: '批次名称' },
    { value: 'currentParticipants', label: '参与人数' },
    { value: 'redPacketAmount', label: '红包金额' }
  ]
}

/**
 * 验证批次创建数据
 * @param {VideoBatchCreateDto} data - 批次创建数据
 * @returns {Object} 验证结果 {valid: boolean, errors: Array<string>}
 */
export function validateBatchCreateData (data) {
  const errors = []

  if (!data.name || data.name.trim() === '') {
    errors.push('批次名称不能为空')
  }

  if (!data.videoId || typeof data.videoId !== 'number') {
    errors.push('视频ID不能为空且必须为数字')
  }

  if (!data.startTime) {
    errors.push('开始时间不能为空')
  }

  if (!data.endTime) {
    errors.push('结束时间不能为空')
  }

  if (data.startTime && data.endTime) {
    const start = new Date(data.startTime)
    const end = new Date(data.endTime)
    if (start >= end) {
      errors.push('开始时间必须早于结束时间')
    }
  }

  if (data.redPacketAmount !== undefined && (typeof data.redPacketAmount !== 'number' || data.redPacketAmount < 0)) {
    errors.push('红包金额必须为非负数')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 检查批次是否可以删除
 * @param {VideoBatchResponseDto} batch - 批次数据
 * @returns {Object} 检查结果 {canDelete: boolean, reason: string}
 */
export function checkBatchDeletable (batch) {
  if (batch.status === 1) {
    return {
      canDelete: false,
      reason: '进行中的批次不能删除'
    }
  }

  if (batch.currentParticipants > 0) {
    return {
      canDelete: false,
      reason: '已有参与者的批次不能删除'
    }
  }

  return {
    canDelete: true,
    reason: ''
  }
}

// 默认导出所有批次相关API
export default {
  createBatch,
  queryBatches,
  deleteBatch,
  getBatchDetail,
  batchDeleteBatches,
  queryBatchesByVideo,
  queryBatchesByCreator,
  queryBatchesByStatus,
  queryBatchesByTimeRange,
  getBatchStatusOptions,
  getBatchSortFieldOptions,
  validateBatchCreateData,
  checkBatchDeletable
}
