/* ===== 用户管理页面通用样式 ===== */
@import './variables.scss';

/* === 页面容器 === */
.user-management-container {
  @extend .fixed-header-layout;
  background: $bg-secondary;
  position: relative;
}

/* === 控制区域样式 === */
.control-section {
  position: fixed;
  width: 100%;
  z-index: $z-index-sticky;
  top: 0;
  background-color: $bg-primary;
  box-shadow: $shadow-sm;
  border-radius: 0 0 $border-radius-lg 0;
  padding: 16rpx 0 16rpx 0;
  min-height: 180rpx;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: $bg-primary;
  border-bottom: 1rpx solid $border-secondary;
  box-shadow: $shadow-sm;
}

.header-content {
  padding: 12rpx 20rpx 16rpx;
}

/* === 选项卡样式 === */
.tab-header {
  @extend .filter-tabs;
}

.tab-item {
  @extend .filter-tab;
}

/* === 搜索容器样式 === */
.search-container {
  margin: 16rpx 0 0 0 !important;
  padding: 0 !important;
}

.search-box {
  @extend .search-filter-section;
}

.search-input {
  @extend .search-box;
  flex: 1;
  margin-right: $spacing-md;
  border: 1rpx solid $border-primary;
  transition: all $transition-base;
}

.search-input:focus-within {
  border-color: $primary-color;
  box-shadow: 0 0 8rpx rgba(24, 144, 255, 0.2);
}

.search-input uni-input {
  height: 1.4em;
}

.search-input .icon-search {
  @extend .search-icon;
}

.search-input input {
  @extend .search-input;
  height: 60rpx;
}

/* === 列表容器样式 === */
.list-container {
  padding: 8rpx 16rpx 16rpx;
  // padding-top: 12rem; /* 为固定头部留出空间 */
}

.container-list {
  margin-top: 8rem;
  /* 为固定头部留出空间 - 时间筛选器 + 搜索框的高度 */
}

.scroll-list {
  height: 100%;
}

/* === 用户列表样式 === */
.user-list {
  margin-top: 0;
}

.user-card {
  margin-bottom: $spacing-sm;
}

.user-card-wrapper {
  position: relative;
  margin-bottom: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-card-wrapper:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.list-bottom-space {
  height: 100rpx;
}

/* === 添加按钮样式 === */
.add-btn-container {
  display: flex;
  align-items: center;
}

.add-btn {
  @extend .action-btn;
  background: linear-gradient(135deg, $primary-color, $primary-hover);
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-lg;
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  .iconfont {
    font-size: $font-size-base;
  }

  text {
    font-size: $font-size-sm;
    color: $text-white;
  }
}

/* === 悬浮按钮样式 === */
.floating-btn-wrapper {
  position: fixed;
  z-index: $z-index-modal;
}

.floating-add-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, $primary-color, $primary-hover);
  border-radius: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.4);
  transition: all $transition-base ease;

  .iconfont {
    font-size: 32rpx;
    color: $text-white;
    margin-bottom: 2rpx;
  }

  .btn-text {
    font-size: 18rpx;
    color: $text-white;
    font-weight: 500;
  }
}

.floating-add-btn:active {
  transform: scale(0.95);
  box-shadow: $shadow-md;
}

/* === 模态框样式 === */
.modal-form {
  padding: 20rpx 0;
}

.modal-buttons {
  padding: 0 20rpx;
}

/* === 自定义卡片样式 === */
.custom-card {
  background: $bg-primary;
  border-radius: $border-radius-md;
  box-shadow: $shadow-base;
  border: 1rpx solid $border-secondary;
  margin: 16rpx;
  overflow: hidden;
  margin-top: 0;
}

/* === 用户头部信息样式 === */
.user-header,
.manager-header {
  display: flex;
  padding: $spacing-base $spacing-lg;
  align-items: center;
}

.user-avatar-section,
.manager-avatar-section {
  margin-right: $spacing-base;
}

.user-basic-info,
.manager-basic-info {
  flex: 1;
}

.user-name-row,
.manager-name-row {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-sm;
}

.user-name,
.manager-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $text-primary;
  margin-right: $spacing-base;
}

.user-id,
.manager-id {
  display: flex;
  align-items: center;
}

.user-actions,
.manager-actions {
  margin-top: $spacing-sm;
}

.id-label {
  font-size: $font-size-sm;
  color: $text-secondary;
}

.id-value {
  font-size: $font-size-sm;
  color: $text-primary;
  margin-right: $spacing-sm;
}

/* === 成员卡片样式 === */
.member-cards-container {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.member-card {
  position: relative;
  background: $bg-primary;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
  border: 1rpx solid $border-secondary;
}

/* === 离职员工卡片样式 === */
.dismissed-card {
  opacity: 0.9;
  background: rgba(248, 249, 250, 0.8);
  border: 1rpx dashed $border-primary;
}

.dismissed-content {
  padding: $spacing-sm;
}

/* === 密码提示样式 === */
.password-tips {
  margin-top: $spacing-base;
  padding: $spacing-sm;
  background: $bg-tertiary;
  border-radius: $border-radius-base;
  border: 1rpx solid $border-secondary;
}

.tip-title {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-primary;
  margin-bottom: $spacing-xs;
}

/* === 提交按钮样式 === */
.submit-btn {
  border-radius: 40rpx;
  margin-top: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

/* === 可滚动内容区域 === */
.scrollable-content {
  flex: 1;
  height: 0;
}

/* === 固定用户信息区域 === */
.fixed-user-section {
  position: relative;
  z-index: $z-index-sticky;
  flex-shrink: 0;
}