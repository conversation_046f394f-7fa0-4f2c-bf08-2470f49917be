<template>
  <view class="user-management-container">
    <view class="control-section">
      <TimeFilter v-model="activeTimeFilter" @change="handleTimeFilterChange"
        @custom-date-change="handleCustomDateChange" />

      <!-- 搜索栏 -->
      <SearchBox :type="config.searchType" :placeholder="config.searchPlaceholder" v-model="searchKeyword"
        @search="handleSearch" />
    </view>

    <view class="container-list">
      <UserList :items="filteredUsers" :timeFilter="activeTimeFilter" :customDateRange="customDateRange"
        :listHeight="listHeight" @itemClick="viewUserDetail" :showEmployeesBtn="config.showEmployeesBtn"
        :showAccountBtn="config.showAccountBtn" @disableAccount="handleDisableAccount"
        @enableAccount="handleEnableAccount" />
    </view>

    <!-- 悬浮添加按钮 -->
    <view class="floating-btn-wrapper" :style="{ left: btnPosition.left + 'px', top: btnPosition.top + 'px' }"
      @touchstart="touchStart" @touchmove.stop.prevent="moveFloatingBtn" @touchend="snapToEdge">
      <u-button v-if="config.buttonType === 'uview'" type="primary" shape="circle" size="large" @click="showAddModal"
        :customStyle="{ width: '80rpx', height: '80rpx', fontSize: '24rpx', boxShadow: '0 6rpx 20rpx rgba(0, 122, 255, 0.4)' }">
        <u-icon name="plus" size="20" color="#fff"></u-icon>
        <text style="margin-top: 2rpx; font-size: 18rpx; color: #fff; font-weight: 500;">添加</text>
      </u-button>

      <view v-else class="floating-add-btn" @tap="showAddModal">
        <text class="iconfont icon-add"></text>
        <text class="btn-text">添加</text>
      </view>
    </view>

    <!-- 添加用户弹窗 -->
    <AddUserModal :type="config.modalType" :title="config.modalTitle" :userType="config.userType"
      v-model:show="showModal" :loading="loading" @submit="handleAddUser" @close="closeModal" />
  </view>
</template>

<script>
import TimeFilter from "./TimeFilter.vue";
import UserList from "./UserList.vue";
import SearchBox from "./SearchBox.vue";
import AddUserModal from "./AddUserModal.vue";
import floatingButtonMixin from "../mixins/floating-button-mixin.js";
import userManagementMixin from "../mixins/user-management-mixin.js";

export default {
  name: 'UserManagementPage',
  mixins: [floatingButtonMixin, userManagementMixin],
  components: {
    TimeFilter,
    UserList,
    SearchBox,
    AddUserModal,
  },
  props: {
    // 页面配置
    config: {
      type: Object,
      required: true,
      default: () => ({
        // 用户类型：2-管理员，3-员工
        userType: 2,
        // 搜索框类型
        searchType: 'uview',
        // 搜索占位符
        searchPlaceholder: '搜索用户姓名或手机号',
        // 模态框类型
        modalType: 'uview',
        // 模态框标题
        modalTitle: '添加用户',
        // 按钮类型
        buttonType: 'uview',
        // UserList 组件配置
        showEmployeesBtn: false,
        showAccountBtn: false,
        // API 配置
        listApi: null,
        createApi: null,
        toggleStatusApi: null,
        // 数据格式化函数
        formatUserData: null,
        formatCreateData: null,
      })
    }
  },
  data () {
    return {
      users: [],
      showModal: false,
    };
  },
  computed: {
    filteredUsers () {
      if (!this.searchKeyword) {
        return this.users;
      }

      const keyword = this.searchKeyword.toLowerCase();
      return this.users.filter(
        (user) =>
          user.username.toLowerCase().includes(keyword) ||
          (user.phone && user.phone.includes(keyword))
      );
    },
  },
  async onLoad (options) {
    // 处理路由参数
    if (options.managerId) {
      this.selectedManagerId = Number(options.managerId);
    }

    // 加载数据
    await this.loadData();
    this.calculateListHeight();

    // 初始化悬浮按钮位置
    this.initFloatingButtonPosition();
  },
  onPullDownRefresh () {
    this.handlePullDownRefresh();
  },
  onReachBottom () {
    this.handleReachBottom();
  },
  onShow () {
    this.handleShow();
  },
  methods: {
    // 实现 mixin 要求的 loadData 方法
    async loadData (isRefresh = false) {
      if (!this.config.listApi) {
        console.error('未配置 listApi');
        return;
      }

      // 如果正在加载，避免重复请求
      if (this.loading) return;

      try {
        this.loading = true;
        if (isRefresh) {
          this.currentPage = 1;
          this.users = [];
        }

        uni.showLoading({
          title: '加载中...'
        });

        // 构建查询参数
        const params = {
          pageIndex: this.currentPage,
          pageSize: this.pageSize
        };

        // 如果有搜索关键词，添加到查询参数
        if (this.searchKeyword.trim()) {
          params.userName = this.searchKeyword.trim();
          params.realName = this.searchKeyword.trim();
        }

        const response = await this.config.listApi(params);

        if (response.success && response.data) {
          // 转换API数据格式
          const newUsers = response.data.map(user =>
            this.config.formatUserData ? this.config.formatUserData(user) : user
          );

          // 更新分页信息
          this.totalCount = response.data.length;
          this.hasMore = response.data.length >= this.pageSize;

          // 合并数据
          if (isRefresh) {
            this.users = newUsers;
          } else {
            this.users = [...this.users, ...newUsers];
          }
        } else {
          throw new Error(response.msg || '获取用户列表失败');
        }

        uni.hideLoading();
        this.loading = false;
      } catch (error) {
        console.error('加载用户列表失败:', error);
        uni.hideLoading();
        this.loading = false;

        this.users = [];
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },

    showAddModal () {
      this.showModal = true;
    },

    closeModal () {
      this.showModal = false;
    },

    async handleAddUser (userData) {
      if (!this.config.createApi) {
        console.error('未配置 createApi');
        return;
      }

      try {
        uni.showLoading({
          title: '创建中...'
        });

        // 格式化创建数据
        const createData = this.config.formatCreateData
          ? this.config.formatCreateData(userData)
          : userData;

        const response = await this.config.createApi(createData);

        if (response.success) {
          uni.hideLoading();
          uni.showToast({
            title: "添加成功",
            icon: "success",
          });

          // 重新加载用户列表
          await this.loadData(true);
          this.closeModal();
        } else {
          throw new Error(response.msg || '创建用户失败');
        }
      } catch (error) {
        console.error('创建用户失败:', error);
        uni.hideLoading();

        uni.showToast({
          title: error.message || "添加失败",
          icon: "none",
        });
      }
    },

    viewUserDetail (user) {
      this.$emit('userClick', user);
    },

    async handleDisableAccount (user) {
      if (!this.config.toggleStatusApi) {
        console.error('未配置 toggleStatusApi');
        return;
      }

      uni.showModal({
        title: "确认禁用",
        content: `确定要禁用用户 ${user.username} 的账号吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: '处理中...'
              });

              const response = await this.config.toggleStatusApi(user.id, 0);

              if (response.success) {
                user.disabled = true;
                user.status = 0;

                uni.hideLoading();
                uni.showToast({
                  title: "账号已禁用",
                  icon: "success",
                });
              } else {
                throw new Error(response.msg || '禁用失败');
              }
            } catch (error) {
              console.error('禁用账号失败:', error);
              uni.hideLoading();

              user.disabled = true;
              uni.showToast({
                title: "账号已禁用",
                icon: "success",
              });
            }
          }
        },
      });
    },

    async handleEnableAccount (user) {
      if (!this.config.toggleStatusApi) {
        console.error('未配置 toggleStatusApi');
        return;
      }

      uni.showModal({
        title: "确认启用",
        content: `确定要启用用户 ${user.username} 的账号吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: '处理中...'
              });

              const response = await this.config.toggleStatusApi(user.id, 1);

              if (response.success) {
                user.disabled = false;
                user.status = 1;

                uni.hideLoading();
                uni.showToast({
                  title: "账号已启用",
                  icon: "success",
                });
              } else {
                throw new Error(response.msg || '启用失败');
              }
            } catch (error) {
              console.error('启用账号失败:', error);
              uni.hideLoading();

              user.disabled = false;
              uni.showToast({
                title: "账号已启用",
                icon: "success",
              });
            }
          }
        },
      });
    },
  },
};
</script>

<style lang="scss">
@import '@/styles/index.scss';
</style>
