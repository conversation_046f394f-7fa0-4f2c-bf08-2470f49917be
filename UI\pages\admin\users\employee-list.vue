<template>
  <view class="redirect-page">
    <view class="loading">
      <text>正在跳转到员工页面...</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'EmployeeListRedirect',
  onLoad (options) {
    console.log('employee-list.vue 重定向到统一页面，参数:', options);

    // 构建重定向URL
    let url = '/pages/admin/users/user-management?type=employee';
    if (options.managerId) {
      url += `&managerId=${options.managerId}`;
    }

    console.log('重定向URL:', url);

    // 立即重定向到统一的用户管理页面
    uni.redirectTo({ url });
  }
};
</script>

<style lang="scss" scoped>
.redirect-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;

  .loading {
    text-align: center;
    color: #666;
  }
}
</style>