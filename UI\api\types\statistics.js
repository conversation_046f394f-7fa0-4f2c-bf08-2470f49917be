/**
 * 统计相关类型定义
 * 基于新的12个统计API端点的数据结构
 */

/**
 * 分页结果基础结构（新API格式）
 * @typedef {Object} PagedResult
 * @property {Array} items - 数据项列表
 * @property {number} totalCount - 总记录数
 * @property {number} pageIndex - 当前页索引（从1开始）
 * @property {number} pageSize - 每页大小
 * @property {number} totalPages - 总页数
 * @property {boolean} hasPreviousPage - 是否有上一页
 * @property {boolean} hasNextPage - 是否有下一页
 */

/**
 * 用户每日统计数据项
 * @typedef {Object} VideoUserDailyStatisticsDto
 * @property {number} id - 统计记录ID
 * @property {number} userId - 用户ID
 * @property {string} statDate - 统计日期
 * @property {number} employeeId - 员工ID
 * @property {string} employeeName - 员工姓名
 * @property {string} userName - 用户姓名
 * @property {number} viewCount - 观看次数
 * @property {number} completeViewCount - 完整观看次数
 * @property {number} totalViewDuration - 总观看时长（秒）
 * @property {number} answerCount - 答题次数
 * @property {number} correctAnswerCount - 正确答题次数
 * @property {number} rewardCount - 奖励次数
 * @property {number} rewardAmount - 奖励金额（分）
 * @property {number} shareCount - 分享次数
 * @property {number} likeCount - 点赞次数
 * @property {number} commentCount - 评论次数
 * @property {number} downloadCount - 下载次数
 * @property {number} completeRate - 完成率
 * @property {number} correctRate - 正确率
 * @property {number} avgViewDuration - 平均观看时长
 * @property {number} rewardAmountYuan - 奖励金额（元）
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 */

/**
 * 用户统计汇总数据
 * @typedef {Object} VideoUserStatisticsSummaryDto
 * @property {number} userId - 用户ID
 * @property {string} userName - 用户姓名
 * @property {number} employeeId - 员工ID
 * @property {string} employeeName - 员工姓名
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 * @property {number} totalViewCount - 总观看次数
 * @property {number} totalCompleteViewCount - 总完整观看次数
 * @property {number} totalViewDuration - 总观看时长
 * @property {number} totalAnswerCount - 总答题次数
 * @property {number} totalCorrectAnswerCount - 总正确答题次数
 * @property {number} totalRewardCount - 总奖励次数
 * @property {number} totalRewardAmount - 总奖励金额（分）
 * @property {number} totalShareCount - 总分享次数
 * @property {number} totalLikeCount - 总点赞次数
 * @property {number} totalCommentCount - 总评论次数
 * @property {number} totalDownloadCount - 总下载次数
 * @property {number} avgCompleteRate - 平均完成率
 * @property {number} avgCorrectRate - 平均正确率
 * @property {number} avgViewDuration - 平均观看时长
 * @property {number} totalRewardAmountYuan - 总奖励金额（元）
 * @property {number} statisticsDays - 统计天数
 */

/**
 * 每日统计趋势数据项
 * @typedef {Object} VideoDailyStatisticsTrendDto
 * @property {string} statDate - 统计日期
 * @property {number} viewCount - 观看次数
 * @property {number} completeViewCount - 完整观看次数
 * @property {number} answerCount - 答题次数
 * @property {number} correctAnswerCount - 正确答题次数
 * @property {number} rewardCount - 奖励次数
 * @property {number} rewardAmountYuan - 奖励金额（元）
 * @property {number} activeUserCount - 活跃用户数
 * @property {number} completeRate - 完成率
 * @property {number} correctRate - 正确率
 */

/**
 * 统计概览数据
 * @typedef {Object} VideoStatisticsOverviewDto
 * @property {number} totalUsers - 总用户数
 * @property {number} totalViewCount - 总观看次数
 * @property {number} totalCompleteViewCount - 总完整观看次数
 * @property {number} totalAnswerCount - 总答题次数
 * @property {number} totalCorrectAnswerCount - 总正确答题次数
 * @property {number} totalRewardCount - 总奖励次数
 * @property {number} totalRewardAmountYuan - 总奖励金额（元）
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 * @property {number} avgCompleteRate - 平均完成率
 * @property {number} avgCorrectRate - 平均正确率
 */

/**
 * 标签统计数据项
 * @typedef {Object} TagStatisticsDto
 * @property {number} tagId - 标签ID
 * @property {string} tagName - 标签名称
 * @property {number} userCount - 用户数量
 * @property {boolean} isUntagged - 是否为未标签用户
 * @property {number} percentage - 百分比
 */

/**
 * 仪表板数据
 * @typedef {Object} VideoDashboardDto
 * @property {Object} summary - 汇总数据
 * @property {Array<TagStatisticsDto>} tagStatistics - 标签统计
 * @property {Object} courseStatistics - 课程统计
 * @property {Object} answerStatistics - 答题统计
 * @property {Object} rewardStatistics - 奖励统计
 * @property {Object} orderStatistics - 订单统计
 * @property {string} timeRange - 时间范围
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 * @property {string} updateTime - 更新时间
 */

/**
 * 关键指标汇总数据
 * @typedef {Object} KeyMetricsSummaryDto
 * @property {number} totalMembers - 总会员数
 * @property {number} todayNewMembers - 今日新增会员
 * @property {number} totalOrders - 总订单数
 * @property {number} viewerCount - 观看者数量
 * @property {number} completeRate - 完成率
 * @property {number} answerUserCount - 答题用户数
 * @property {number} correctRate - 正确率
 * @property {number} totalRewardAmount - 总奖励金额
 * @property {number} untaggedUserCount - 未标签用户数
 */

/**
 * 兼容性接口统计数据项
 * @typedef {Object} VideoStatisticsResponseDto
 * @property {number} id - 统计记录ID
 * @property {string} statDate - 统计日期
 * @property {number} batchId - 批次ID
 * @property {string} batchName - 批次名称
 * @property {number} adminId - 管理员ID
 * @property {string} adminName - 管理员姓名
 * @property {number} employeeId - 员工ID
 * @property {string} employeeName - 员工姓名
 * @property {number} userId - 用户ID
 * @property {number} videoId - 视频ID
 * @property {string} statType - 统计类型
 * @property {number} shareCount - 分享次数
 * @property {number} likeCount - 点赞次数
 * @property {number} commentCount - 评论次数
 * @property {number} downloadCount - 下载次数
 * @property {number} duration - 时长
 * @property {number} viewCount - 观看次数
 * @property {number} completeViewCount - 完整观看次数
 * @property {number} completeRate - 完成率
 * @property {number} newUserCount - 新用户数
 * @property {number} correctAnswerCount - 正确答题次数
 * @property {number} totalAnswerCount - 总答题次数
 * @property {number} correctRate - 正确率
 * @property {number} rewardCount - 奖励次数
 * @property {number} rewardAmount - 奖励金额（分）
 * @property {number} rewardAmountYuan - 奖励金额（元）
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 更新时间
 */

/**
 * 兼容性汇总统计数据
 * @typedef {Object} VideoStatisticsSummaryDto
 * @property {number} totalViewCount - 总观看次数
 * @property {number} totalCompleteViewCount - 总完整观看次数
 * @property {number} averageCompleteRate - 平均完成率
 * @property {number} totalNewUserCount - 总新用户数
 * @property {number} totalCorrectAnswerCount - 总正确答题次数
 * @property {number} totalAnswerCount - 总答题次数
 * @property {number} averageCorrectRate - 平均正确率
 * @property {number} totalRewardCount - 总奖励次数
 * @property {number} totalRewardAmount - 总奖励金额（分）
 * @property {number} totalRewardAmountYuan - 总奖励金额（元）
 * @property {number} totalShareCount - 总分享次数
 * @property {number} totalLikeCount - 总点赞次数
 * @property {number} totalCommentCount - 总评论次数
 * @property {number} totalDownloadCount - 总下载次数
 * @property {number} totalDuration - 总时长
 * @property {number} averageViewCount - 平均观看次数
 * @property {number} averageShareCount - 平均分享次数
 * @property {number} averageLikeCount - 平均点赞次数
 * @property {number} averageCommentCount - 平均评论次数
 * @property {number} averageDownloadCount - 平均下载次数
 * @property {number} averageDuration - 平均时长
 * @property {number} statDays - 统计天数
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 */

/**
 * API请求参数类型定义
 */

/**
 * 用户每日统计查询参数
 * @typedef {Object} UserDailyStatisticsParams
 * @property {number} [UserId] - 用户ID
 * @property {number} [EmployeeId] - 员工ID
 * @property {string} [StartDate] - 开始日期
 * @property {string} [EndDate] - 结束日期
 * @property {Array<number>} [UserIds] - 用户ID数组
 * @property {number} [PageIndex] - 页索引
 * @property {number} [PageSize] - 页大小
 * @property {string} [OrderField] - 排序字段
 * @property {boolean} [IsAsc] - 是否升序
 */

/**
 * 用户汇总统计查询参数
 * @typedef {Object} UserSummaryParams
 * @property {string} [startDate] - 开始日期
 * @property {string} [endDate] - 结束日期
 */

/**
 * 批量用户汇总统计查询参数
 * @typedef {Object} UsersSummaryParams
 * @property {string} [userIds] - 用户ID列表（逗号分隔）
 * @property {string} [startDate] - 开始日期
 * @property {string} [endDate] - 结束日期
 */

/**
 * 每日趋势查询参数
 * @typedef {Object} DailyTrendParams
 * @property {string} [userIds] - 用户ID列表（逗号分隔）
 * @property {number} [employeeId] - 员工ID
 * @property {string} [startDate] - 开始日期
 * @property {string} [endDate] - 结束日期
 */

/**
 * 统计概览查询参数
 * @typedef {Object} OverviewParams
 * @property {string} [startDate] - 开始日期
 * @property {string} [endDate] - 结束日期
 */

/**
 * 关键指标查询参数
 * @typedef {Object} KeyMetricsParams
 * @property {string} [startDate] - 开始日期
 * @property {string} [endDate] - 结束日期
 */

/**
 * 兼容性汇总查询参数
 * @typedef {Object} VideoStatisticsSummaryQueryDto
 * @property {number} [userId] - 用户ID
 * @property {number} [videoId] - 视频ID
 * @property {number} [batchId] - 批次ID
 * @property {string} [statType] - 统计类型
 * @property {Array<number>} [batchIds] - 批次ID数组
 * @property {number} [adminId] - 管理员ID
 * @property {number} [employeeId] - 员工ID
 * @property {string} [startDate] - 开始日期
 * @property {string} [endDate] - 结束日期
 */

// 导出类型定义
export const StatisticsTypes = {
  // 响应数据类型
  PagedResult: 'PagedResult',
  VideoUserDailyStatisticsDto: 'VideoUserDailyStatisticsDto',
  VideoUserStatisticsSummaryDto: 'VideoUserStatisticsSummaryDto',
  VideoDailyStatisticsTrendDto: 'VideoDailyStatisticsTrendDto',
  VideoStatisticsOverviewDto: 'VideoStatisticsOverviewDto',
  TagStatisticsDto: 'TagStatisticsDto',
  VideoDashboardDto: 'VideoDashboardDto',
  KeyMetricsSummaryDto: 'KeyMetricsSummaryDto',
  VideoStatisticsResponseDto: 'VideoStatisticsResponseDto',
  VideoStatisticsSummaryDto: 'VideoStatisticsSummaryDto',

  // 请求参数类型
  UserDailyStatisticsParams: 'UserDailyStatisticsParams',
  UserSummaryParams: 'UserSummaryParams',
  UsersSummaryParams: 'UsersSummaryParams',
  DailyTrendParams: 'DailyTrendParams',
  OverviewParams: 'OverviewParams',
  KeyMetricsParams: 'KeyMetricsParams',
  VideoStatisticsSummaryQueryDto: 'VideoStatisticsSummaryQueryDto'
}

export default StatisticsTypes
