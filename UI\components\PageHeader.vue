<template>
  <view>

    <view class="_page-header">
      <view class="_back-btn" @tap="goBack">
        <view class="_back-chevron">&lt;</view>
      </view>
      <view class="_title-container">
        <text class="_page-title">{{ title }}</text>
        <slot name="subtitle"></slot>
      </view>
      <view class="_right-slot">
        <slot name="right"></slot>
      </view>
    </view>

    <view class="_page-header-2"> </view>

  </view>


</template>

<script>
export default {
  name: '<PERSON>Header',
  props: {
    title: {
      type: String,
      default: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    goBack () {
      if (this.showBack) {
        uni.navigateBack();
      }
    }
  }
}
</script>

<style>
._page-header-2 {

  height: 92rpx;
}

._page-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #FFFEFF;
  height: 88rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(36, 106, 208, 0.1);
}

._back-btn {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

._back-chevron {
  font-size: 40rpx;
  color: #333;
  font-weight: 500;
  transform: scale(0.9, 1.3);
  line-height: 40rpx;
}

._title-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

._page-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

._right-slot {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>